<?php

namespace App\Http\Controllers;

use App\Models\CustomDomain;
use App\Services\DnsVerificationService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class CustomDomainController extends Controller
{
    protected DnsVerificationService $dnsService;

    public function __construct(DnsVerificationService $dnsService)
    {
        $this->middleware('auth');
        $this->dnsService = $dnsService;
    }

    /**
     * Display a listing of user's custom domains.
     */
    public function index(): View
    {
        $domains = Auth::user()->customDomains()
            ->withCount('urls')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('custom-domains.index', compact('domains'));
    }

    /**
     * Show the form for creating a new custom domain.
     */
    public function create(): View
    {
        return view('custom-domains.create');
    }

    /**
     * Store a newly created custom domain.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'domain' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/',
                'unique:custom_domains,domain',
            ],
        ]);

        $domain = $request->domain;
        
        // Remove protocol if provided
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = rtrim($domain, '/');

        // Check if domain is already in use
        if (CustomDomain::where('domain', $domain)->exists()) {
            return back()->withErrors(['domain' => 'This domain is already registered.']);
        }

        // Create the custom domain
        $customDomain = Auth::user()->customDomains()->create([
            'domain' => $domain,
            'verification_token' => CustomDomain::generateVerificationToken(),
            'is_verified' => false,
            'is_active' => false,
        ]);

        return redirect()->route('custom-domains.show', $customDomain)
            ->with('success', 'Custom domain added successfully! Please verify your domain ownership.');
    }

    /**
     * Display the specified custom domain.
     */
    public function show(CustomDomain $customDomain): View
    {
        $this->authorize('view', $customDomain);

        $customDomain->load(['urls' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        $verificationInstructions = $this->getVerificationInstructions($customDomain);

        return view('custom-domains.show', compact('customDomain', 'verificationInstructions'));
    }

    /**
     * Show the form for editing the specified custom domain.
     */
    public function edit(CustomDomain $customDomain): View
    {
        $this->authorize('update', $customDomain);

        return view('custom-domains.edit', compact('customDomain'));
    }

    /**
     * Update the specified custom domain.
     */
    public function update(Request $request, CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('update', $customDomain);

        $request->validate([
            'is_active' => 'boolean',
        ]);

        // Only allow toggling active status if domain is verified
        if ($request->has('is_active') && $customDomain->is_verified) {
            $customDomain->update(['is_active' => $request->boolean('is_active')]);
            
            $status = $request->boolean('is_active') ? 'activated' : 'deactivated';
            return redirect()->route('custom-domains.show', $customDomain)
                ->with('success', "Custom domain {$status} successfully!");
        }

        return redirect()->route('custom-domains.show', $customDomain)
            ->with('error', 'Domain must be verified before it can be activated.');
    }

    /**
     * Remove the specified custom domain.
     */
    public function destroy(CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('delete', $customDomain);

        // Check if domain has URLs
        if ($customDomain->urls()->count() > 0) {
            return back()->with('error', 'Cannot delete domain that has URLs. Please delete or reassign URLs first.');
        }

        $domain = $customDomain->domain;
        $customDomain->delete();

        return redirect()->route('custom-domains.index')
            ->with('success', "Custom domain '{$domain}' deleted successfully!");
    }

    /**
     * Verify domain ownership.
     */
    public function verify(CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('update', $customDomain);

        if ($customDomain->is_verified) {
            return back()->with('info', 'Domain is already verified.');
        }

        $verificationResult = $this->dnsService->verifyDomain($customDomain);

        if ($verificationResult['verified']) {
            $customDomain->markAsVerified();
            $customDomain->update(['is_active' => true]);

            return back()->with('success', 'Domain verified successfully! Your custom domain is now active.');
        }

        return back()->with('error', 'Domain verification failed: ' . $verificationResult['message']);
    }

    /**
     * Regenerate verification token.
     */
    public function regenerateToken(CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('update', $customDomain);

        if ($customDomain->is_verified) {
            return back()->with('error', 'Cannot regenerate token for verified domain.');
        }

        $customDomain->update([
            'verification_token' => CustomDomain::generateVerificationToken(),
        ]);

        return back()->with('success', 'Verification token regenerated successfully!');
    }

    /**
     * Check SSL status for domain.
     */
    public function checkSsl(CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('view', $customDomain);

        if (!$customDomain->is_verified) {
            return back()->with('error', 'Domain must be verified before checking SSL.');
        }

        $sslInfo = $this->dnsService->checkSslCertificate($customDomain->domain);

        if ($sslInfo['valid']) {
            $customDomain->update([
                'ssl_enabled' => true,
                'ssl_expires_at' => $sslInfo['expires_at'],
            ]);

            return back()->with('success', 'SSL certificate is valid and active!');
        }

        $customDomain->update(['ssl_enabled' => false]);
        return back()->with('warning', 'SSL certificate not found or invalid: ' . $sslInfo['message']);
    }

    /**
     * Get verification instructions for the domain.
     */
    private function getVerificationInstructions(CustomDomain $customDomain): array
    {
        $appDomain = parse_url(config('app.url'), PHP_URL_HOST);
        
        return [
            'txt_record' => [
                'name' => '_minilink-verification',
                'value' => $customDomain->verification_token,
                'type' => 'TXT',
            ],
            'cname_record' => [
                'name' => '@',
                'value' => $appDomain,
                'type' => 'CNAME',
            ],
            'a_record' => [
                'name' => '@',
                'value' => gethostbyname($appDomain),
                'type' => 'A',
            ],
        ];
    }

    /**
     * Get domain statistics.
     */
    public function stats(CustomDomain $customDomain): View
    {
        $this->authorize('view', $customDomain);

        $stats = [
            'total_urls' => $customDomain->urls()->count(),
            'active_urls' => $customDomain->urls()->where('is_active', true)->count(),
            'total_clicks' => $customDomain->urls()->sum('click_count'),
            'clicks_today' => $customDomain->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->whereDate('analytics.clicked_at', today())
                ->count(),
            'clicks_this_month' => $customDomain->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->where('analytics.clicked_at', '>=', now()->subMonth())
                ->count(),
            'top_urls' => $customDomain->urls()
                ->orderBy('click_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'short_code', 'click_count']),
        ];

        return view('custom-domains.stats', compact('customDomain', 'stats'));
    }

    /**
     * Test domain configuration.
     */
    public function test(CustomDomain $customDomain): RedirectResponse
    {
        $this->authorize('view', $customDomain);

        $testResults = $this->dnsService->testDomainConfiguration($customDomain);

        if ($testResults['success']) {
            return back()->with('success', 'Domain configuration test passed! All DNS records are correctly configured.');
        }

        return back()->with('error', 'Domain configuration test failed: ' . implode(', ', $testResults['errors']));
    }
}
