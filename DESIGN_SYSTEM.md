# Minilink.at Design System

## Overview
This document outlines the design system and layout standards for Minilink.at. All new pages should follow these guidelines to maintain consistency and professional appearance.

## Layout System

### Primary Layouts

#### 1. `layouts.modern` - Public Pages
- **Use for**: Homepage, authentication pages, public-facing content
- **Features**: Clean header, footer, responsive design
- **Navigation**: Simple public navigation

#### 2. `layouts.admin-modern` - Dashboard/Admin Pages
- **Use for**: User dashboard, admin panels, authenticated user pages
- **Features**: Full navigation, user menu, notifications, breadcrumbs
- **Navigation**: Complete authenticated user navigation

### Layout Usage Examples

```blade
{{-- For public pages --}}
@extends('layouts.modern')

{{-- For authenticated user pages --}}
@extends('layouts.admin-modern')
```

## Design Components

### Color Palette

#### Primary Colors
- **Blue Primary**: `#3B82F6` (blue-600)
- **Blue Light**: `#60A5FA` (blue-400)
- **Blue Dark**: `#1D4ED8` (blue-700)

#### Status Colors
- **Success**: `#10B981` (green-500)
- **Warning**: `#F59E0B` (amber-500)
- **Error**: `#EF4444` (red-500)
- **Info**: `#06B6D4` (cyan-500)

#### Neutral Colors
- **Gray 50**: `#F9FAFB` (backgrounds)
- **Gray 100**: `#F3F4F6` (light backgrounds)
- **Gray 200**: `#E5E7EB` (borders)
- **Gray 600**: `#4B5563` (secondary text)
- **Gray 900**: `#111827` (primary text)

### Typography

#### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: system-ui, sans-serif

#### Font Weights
- **Light**: 300
- **Regular**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

#### Text Sizes
```css
text-xs     /* 12px */
text-sm     /* 14px */
text-base   /* 16px */
text-lg     /* 18px */
text-xl     /* 20px */
text-2xl    /* 24px */
text-3xl    /* 30px */
```

### Component Classes

#### Cards
```html
<!-- Standard Card -->
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900">Card Title</h3>
    </div>
    <div class="card-body">
        <!-- Card content -->
    </div>
</div>

<!-- Gradient Card -->
<div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
    <div class="card-body">
        <!-- Content -->
    </div>
</div>
```

#### Buttons
```html
<!-- Primary Button -->
<button class="btn-primary">
    <i class="fas fa-icon mr-2"></i>Button Text
</button>

<!-- Secondary Button -->
<button class="btn-secondary">Button Text</button>

<!-- Outline Button -->
<button class="btn-outline">Button Text</button>

<!-- Danger Button -->
<button class="btn-danger">Button Text</button>
```

#### Form Elements
```html
<!-- Input Field -->
<input type="text" class="input-field" placeholder="Enter text">

<!-- Select Field -->
<select class="input-field">
    <option>Option 1</option>
</select>

<!-- Textarea -->
<textarea class="input-field" rows="4"></textarea>
```

#### Badges
```html
<span class="badge badge-primary">Primary</span>
<span class="badge badge-success">Success</span>
<span class="badge badge-warning">Warning</span>
<span class="badge badge-danger">Danger</span>
<span class="badge badge-info">Info</span>
```

#### Navigation Links
```html
<a href="#" class="nav-link">Navigation Item</a>
<a href="#" class="nav-link active">Active Item</a>
```

### Layout Patterns

#### Page Header
```html
<div class="mb-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-icon mr-3 text-blue-600"></i>
                Page Title
            </h1>
            <p class="text-gray-600 mt-1">Page description</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
            <a href="#" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
            <a href="#" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>Create New
            </a>
        </div>
    </div>
</div>
```

#### Statistics Cards Grid
```html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <div class="card-body text-center">
            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-icon text-xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ $value }}</div>
            <div class="text-blue-100 text-sm">Label</div>
        </div>
    </div>
</div>
```

#### Data Tables
```html
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900">Table Title</h3>
    </div>
    <div class="card-body p-0">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Column Header
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-sm text-gray-900">
                            Cell Content
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
```

### Interactive Elements

#### Dropdowns (using Alpine.js)
```html
<div class="relative" x-data="{ open: false }">
    <button @click="open = !open" class="btn-outline">
        Dropdown <i class="fas fa-chevron-down ml-2"></i>
    </button>
    <div x-show="open" 
         @click.away="open = false"
         x-transition
         class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
        <div class="py-1">
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Option 1
            </a>
        </div>
    </div>
</div>
```

#### Modals
```html
<div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Modal Title</h3>
                <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <!-- Modal content -->
        </div>
    </div>
</div>
```

### Chart Styling

#### Chart.js Configuration
```javascript
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
        x: {
            grid: { display: false },
            ticks: { color: '#6B7280' }
        },
        y: {
            grid: { color: '#F3F4F6' },
            ticks: { color: '#6B7280' }
        }
    },
    plugins: {
        tooltip: {
            backgroundColor: '#1F2937',
            titleColor: '#F9FAFB',
            bodyColor: '#F9FAFB',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8
        }
    }
};
```

## Best Practices

### 1. Consistency
- Always use the defined color palette
- Maintain consistent spacing using Tailwind's spacing scale
- Use consistent icon styles (Font Awesome 6)

### 2. Responsiveness
- Design mobile-first
- Use Tailwind's responsive prefixes (sm:, md:, lg:, xl:)
- Test on multiple screen sizes

### 3. Accessibility
- Include proper ARIA labels
- Ensure sufficient color contrast
- Use semantic HTML elements

### 4. Performance
- Optimize images and assets
- Use lazy loading where appropriate
- Minimize JavaScript bundle size

### 5. SEO
- Include proper meta tags
- Use semantic HTML structure
- Implement structured data where relevant

## Implementation Checklist

When creating a new page:

- [ ] Choose appropriate layout (`layouts.modern` or `layouts.admin-modern`)
- [ ] Include proper page title and meta tags
- [ ] Use consistent header structure
- [ ] Apply proper spacing and typography
- [ ] Implement responsive design
- [ ] Add interactive elements with Alpine.js
- [ ] Include proper error handling
- [ ] Test on multiple devices
- [ ] Validate accessibility
- [ ] Optimize for performance

## Examples

See the following pages for reference implementations:
- **URL Analytics**: `resources/views/urls/show.blade.php`
- **Dashboard**: `resources/views/dashboard.blade.php`
- **Admin Dashboard**: `resources/views/admin/dashboard.blade.php`
- **Bulk Operations**: `resources/views/bulk-operations/index.blade.php`
