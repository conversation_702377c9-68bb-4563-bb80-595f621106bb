<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Services\UrlShortenerService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

class BulkOperationsController extends Controller
{
    protected UrlShortenerService $urlShortenerService;

    public function __construct(UrlShortenerService $urlShortenerService)
    {
        $this->middleware('auth');
        $this->urlShortenerService = $urlShortenerService;
    }

    /**
     * Show bulk operations dashboard.
     */
    public function index(): View
    {
        $user = Auth::user();
        
        // Check if user has bulk operations feature
        if (!$user->subscription?->bulk_operations) {
            return view('bulk-operations.upgrade-required');
        }

        $recentBulkOperations = $this->getRecentBulkOperations();

        return view('bulk-operations.index', compact('recentBulkOperations'));
    }

    /**
     * Show bulk URL creation form.
     */
    public function create(): View
    {
        $user = Auth::user();
        
        if (!$user->subscription?->bulk_operations) {
            return view('bulk-operations.upgrade-required');
        }

        $customDomains = $user->customDomains()
            ->where('is_verified', true)
            ->where('is_active', true)
            ->get();

        return view('bulk-operations.create', compact('customDomains'));
    }

    /**
     * Process bulk URL creation.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->bulk_operations) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Bulk operations require a Pro or Premium subscription.');
        }

        $request->validate([
            'urls' => 'required|string',
            'custom_domain_id' => 'nullable|exists:custom_domains,id',
            'expires_at' => 'nullable|date|after:now',
            'password' => 'nullable|string|min:4',
        ]);

        $urlsText = $request->input('urls');
        $lines = array_filter(array_map('trim', explode("\n", $urlsText)));

        if (empty($lines)) {
            return back()->with('error', 'No URLs provided.');
        }

        if (count($lines) > 1000) {
            return back()->with('error', 'Maximum 1000 URLs allowed per batch.');
        }

        $results = $this->processBulkUrls($lines, $request->all(), $user);

        return redirect()->route('bulk-operations.results', ['batch_id' => $results['batch_id']])
            ->with('success', "Processed {$results['total']} URLs. {$results['success']} successful, {$results['failed']} failed.");
    }

    /**
     * Show bulk operation results.
     */
    public function results(Request $request): View
    {
        $batchId = $request->get('batch_id');
        $results = cache()->get("bulk_operation_{$batchId}");

        if (!$results) {
            return redirect()->route('bulk-operations.index')
                ->with('error', 'Bulk operation results not found or expired.');
        }

        return view('bulk-operations.results', compact('results'));
    }

    /**
     * Show bulk edit form.
     */
    public function edit(): View
    {
        $user = Auth::user();
        
        if (!$user->subscription?->bulk_operations) {
            return view('bulk-operations.upgrade-required');
        }

        $urls = $user->urls()->orderBy('created_at', 'desc')->paginate(50);

        return view('bulk-operations.edit', compact('urls'));
    }

    /**
     * Process bulk URL updates.
     */
    public function update(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->bulk_operations) {
            return response()->json(['error' => 'Bulk operations not available'], 403);
        }

        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,update_expiry,add_password,remove_password',
            'url_ids' => 'required|array|min:1|max:100',
            'url_ids.*' => 'exists:urls,id',
            'expires_at' => 'nullable|date|after:now',
            'password' => 'nullable|string|min:4',
        ]);

        $urlIds = $request->get('url_ids');
        $action = $request->get('action');

        // Verify user owns all URLs
        $userUrls = $user->urls()->whereIn('id', $urlIds)->pluck('id')->toArray();
        if (count($userUrls) !== count($urlIds)) {
            return response()->json(['error' => 'You can only modify your own URLs'], 403);
        }

        $affectedCount = 0;

        switch ($action) {
            case 'activate':
                $affectedCount = Url::whereIn('id', $urlIds)->update(['is_active' => true]);
                break;

            case 'deactivate':
                $affectedCount = Url::whereIn('id', $urlIds)->update(['is_active' => false]);
                break;

            case 'delete':
                $affectedCount = Url::whereIn('id', $urlIds)->count();
                Url::whereIn('id', $urlIds)->delete();
                break;

            case 'update_expiry':
                $affectedCount = Url::whereIn('id', $urlIds)
                    ->update(['expires_at' => $request->get('expires_at')]);
                break;

            case 'add_password':
                if ($request->filled('password')) {
                    $affectedCount = Url::whereIn('id', $urlIds)
                        ->update(['password' => bcrypt($request->get('password'))]);
                }
                break;

            case 'remove_password':
                $affectedCount = Url::whereIn('id', $urlIds)->update(['password' => null]);
                break;
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully {$action}d {$affectedCount} URL(s)",
            'affected_count' => $affectedCount,
        ]);
    }

    /**
     * Export URLs in bulk.
     */
    public function export(Request $request): RedirectResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->bulk_operations) {
            return back()->with('error', 'Bulk operations not available');
        }

        $request->validate([
            'format' => 'required|in:csv,json,xlsx',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'include_analytics' => 'boolean',
        ]);

        $query = $user->urls();

        // Apply date filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $urls = $query->with(['customDomain', 'analytics'])->get();

        $filename = $this->generateExport($urls, $request->all());

        return redirect()->route('bulk-operations.download', ['filename' => $filename])
            ->with('success', 'Export generated successfully!');
    }

    /**
     * Download exported file.
     */
    public function download(string $filename)
    {
        $filePath = storage_path('app/exports/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'Export file not found.');
        }

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Process bulk URLs creation.
     */
    private function processBulkUrls(array $lines, array $options, $user): array
    {
        $batchId = uniqid('bulk_');
        $results = [
            'batch_id' => $batchId,
            'total' => count($lines),
            'success' => 0,
            'failed' => 0,
            'urls' => [],
            'errors' => [],
        ];

        foreach ($lines as $index => $line) {
            try {
                // Parse line (could be just URL or "URL,title,description")
                $parts = str_getcsv($line);
                $originalUrl = trim($parts[0]);
                $title = isset($parts[1]) ? trim($parts[1]) : null;
                $description = isset($parts[2]) ? trim($parts[2]) : null;

                // Validate URL
                if (!filter_var($originalUrl, FILTER_VALIDATE_URL)) {
                    throw new \Exception("Invalid URL: {$originalUrl}");
                }

                // Check user's URL limit
                if ($user->subscription?->url_limit) {
                    $currentCount = $user->urls()->count() + $results['success'];
                    if ($currentCount >= $user->subscription->url_limit) {
                        throw new \Exception("URL limit reached");
                    }
                }

                // Create URL
                $urlData = [
                    'original_url' => $originalUrl,
                    'title' => $title,
                    'description' => $description,
                    'custom_domain_id' => $options['custom_domain_id'] ?? null,
                    'expires_at' => $options['expires_at'] ?? null,
                    'password' => $options['password'] ?? null,
                ];

                $url = $this->urlShortenerService->shortenUrl($urlData, $user);

                $results['urls'][] = [
                    'original_url' => $originalUrl,
                    'short_url' => $url->short_url,
                    'title' => $title,
                    'status' => 'success',
                ];

                $results['success']++;

            } catch (\Exception $e) {
                $results['errors'][] = [
                    'line' => $index + 1,
                    'url' => $line,
                    'error' => $e->getMessage(),
                ];
                $results['failed']++;
            }
        }

        // Cache results for 1 hour
        cache()->put("bulk_operation_{$batchId}", $results, 3600);

        return $results;
    }

    /**
     * Generate export file.
     */
    private function generateExport($urls, array $options): string
    {
        $format = $options['format'];
        $includeAnalytics = $options['include_analytics'] ?? false;
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "urls_export_{$timestamp}.{$format}";
        $filePath = storage_path('app/exports/' . $filename);

        // Ensure directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        switch ($format) {
            case 'csv':
                $this->generateCsvExport($urls, $filePath, $includeAnalytics);
                break;
            case 'json':
                $this->generateJsonExport($urls, $filePath, $includeAnalytics);
                break;
            case 'xlsx':
                $this->generateXlsxExport($urls, $filePath, $includeAnalytics);
                break;
        }

        return $filename;
    }

    /**
     * Generate CSV export.
     */
    private function generateCsvExport($urls, string $filePath, bool $includeAnalytics): void
    {
        $file = fopen($filePath, 'w');
        
        $headers = ['Title', 'Original URL', 'Short URL', 'Domain', 'Clicks', 'Status', 'Created'];
        if ($includeAnalytics) {
            $headers = array_merge($headers, ['Unique Clicks', 'Countries', 'Top Referrer']);
        }
        
        fputcsv($file, $headers);

        foreach ($urls as $url) {
            $row = [
                $url->title ?: 'Untitled',
                $url->original_url,
                $url->short_url,
                $url->customDomain?->domain ?: 'Default',
                $url->click_count,
                $url->is_active ? 'Active' : 'Inactive',
                $url->created_at->format('Y-m-d H:i:s'),
            ];

            if ($includeAnalytics) {
                $uniqueClicks = $url->analytics()->distinct('ip_address')->count();
                $countries = $url->analytics()->distinct('country')->count();
                $topReferrer = $url->analytics()
                    ->selectRaw('referrer, COUNT(*) as count')
                    ->whereNotNull('referrer')
                    ->groupBy('referrer')
                    ->orderBy('count', 'desc')
                    ->first()?->referrer ?? 'Direct';

                $row = array_merge($row, [$uniqueClicks, $countries, $topReferrer]);
            }

            fputcsv($file, $row);
        }

        fclose($file);
    }

    /**
     * Generate JSON export.
     */
    private function generateJsonExport($urls, string $filePath, bool $includeAnalytics): void
    {
        $data = $urls->map(function ($url) use ($includeAnalytics) {
            $urlData = [
                'id' => $url->id,
                'title' => $url->title,
                'original_url' => $url->original_url,
                'short_url' => $url->short_url,
                'short_code' => $url->short_code,
                'custom_alias' => $url->custom_alias,
                'domain' => $url->customDomain?->domain,
                'click_count' => $url->click_count,
                'is_active' => $url->is_active,
                'expires_at' => $url->expires_at?->toISOString(),
                'created_at' => $url->created_at->toISOString(),
            ];

            if ($includeAnalytics) {
                $urlData['analytics'] = [
                    'unique_clicks' => $url->analytics()->distinct('ip_address')->count(),
                    'countries' => $url->analytics()->distinct('country')->count(),
                    'devices' => $url->analytics()->distinct('device_type')->count(),
                    'browsers' => $url->analytics()->distinct('browser')->count(),
                ];
            }

            return $urlData;
        });

        file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Generate XLSX export (simplified - would use PhpSpreadsheet in production).
     */
    private function generateXlsxExport($urls, string $filePath, bool $includeAnalytics): void
    {
        // For simplicity, generate CSV with .xlsx extension
        // In production, use PhpSpreadsheet library
        $this->generateCsvExport($urls, $filePath, $includeAnalytics);
    }

    /**
     * Get recent bulk operations.
     */
    private function getRecentBulkOperations(): array
    {
        // In a real app, this would fetch from a bulk_operations table
        return [
            [
                'id' => 'bulk_001',
                'type' => 'create',
                'description' => 'Created 150 URLs from CSV upload',
                'items_count' => 150,
                'status' => 'completed',
                'total' => 150,
                'success' => 148,
                'failed' => 2,
                'created_at' => now()->subHours(2)->diffForHumans(),
            ],
            [
                'id' => 'bulk_002',
                'type' => 'export',
                'description' => 'Exported 500 URLs to CSV format',
                'items_count' => 500,
                'status' => 'completed',
                'total' => 500,
                'success' => 500,
                'failed' => 0,
                'created_at' => now()->subDay()->diffForHumans(),
            ],
            [
                'id' => 'bulk_003',
                'type' => 'edit',
                'description' => 'Updated titles for 75 URLs',
                'items_count' => 75,
                'status' => 'completed',
                'total' => 75,
                'success' => 75,
                'failed' => 0,
                'created_at' => now()->subDays(3)->diffForHumans(),
            ],
        ];
    }

    /**
     * Download CSV template for bulk operations.
     */
    public function downloadTemplate()
    {
        // Create comprehensive CSV template with examples
        $csvContent = "URL,Title,Description\n";
        $csvContent .= "https://example.com/product/laptop,Gaming Laptop - High Performance,Professional gaming laptop with RTX graphics\n";
        $csvContent .= "https://example.com/blog/seo-tips,10 SEO Tips for 2024,Complete guide to search engine optimization\n";
        $csvContent .= "https://example.com/contact,Contact Us - Example Company,Get in touch with our support team\n";
        $csvContent .= "https://example.com/pricing,Pricing Plans,Compare our affordable pricing options\n";
        $csvContent .= "https://example.com/about,About Our Company,Learn more about our mission and values\n";

        // Add comment lines for guidance (will be ignored during processing)
        $csvContent .= "\n# CSV Format Guidelines:\n";
        $csvContent .= "# - First column: URL (required) - must include http:// or https://\n";
        $csvContent .= "# - Second column: Title (optional) - will be auto-generated if empty\n";
        $csvContent .= "# - Third column: Description (optional) - for SEO and organization\n";
        $csvContent .= "# - Lines starting with # are comments and will be ignored\n";
        $csvContent .= "# - Maximum 1000 URLs per upload\n";
        $csvContent .= "# - Duplicate URLs will be automatically skipped\n";

        $headers = [
            'Content-Type' => 'text/csv; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="minilink-bulk-urls-template.csv"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ];

        return response($csvContent, 200, $headers);
    }
}
