<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SeoSetting;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display system settings.
     */
    public function index(): View
    {
        $settings = $this->getSystemSettings();
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'app_timezone' => 'required|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
            'default_url_expiry' => 'nullable|integer|min:1',
            'max_urls_per_user' => 'nullable|integer|min:1',
            'enable_registration' => 'boolean',
            'enable_email_verification' => 'boolean',
            'enable_api' => 'boolean',
            'enable_qr_codes' => 'boolean',
            'enable_analytics' => 'boolean',
            'enable_custom_domains' => 'boolean',
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|string|max:500',
        ]);

        // Update settings using SeoSetting model for consistency
        $settingsToUpdate = [
            'app_name' => $request->app_name,
            'app_url' => $request->app_url,
            'app_timezone' => $request->app_timezone,
            'mail_from_address' => $request->mail_from_address,
            'mail_from_name' => $request->mail_from_name,
            'default_url_expiry' => $request->default_url_expiry,
            'max_urls_per_user' => $request->max_urls_per_user,
            'enable_registration' => $request->boolean('enable_registration'),
            'enable_email_verification' => $request->boolean('enable_email_verification'),
            'enable_api' => $request->boolean('enable_api'),
            'enable_qr_codes' => $request->boolean('enable_qr_codes'),
            'enable_analytics' => $request->boolean('enable_analytics'),
            'enable_custom_domains' => $request->boolean('enable_custom_domains'),
            'maintenance_mode' => $request->boolean('maintenance_mode'),
            'maintenance_message' => $request->maintenance_message,
        ];

        foreach ($settingsToUpdate as $key => $value) {
            SeoSetting::set($key, $value, 'text', 'system', "System setting: {$key}");
        }

        // Clear application cache
        Cache::flush();

        return redirect()->route('admin.settings.index')
            ->with('success', 'System settings updated successfully!');
    }

    /**
     * Show cache management.
     */
    public function cache(): View
    {
        $cacheInfo = $this->getCacheInfo();
        return view('admin.settings.cache', compact('cacheInfo'));
    }

    /**
     * Clear application cache.
     */
    public function clearCache(Request $request): RedirectResponse
    {
        $request->validate([
            'cache_type' => 'required|in:all,config,route,view,application',
        ]);

        $cacheType = $request->get('cache_type');
        $message = '';

        try {
            switch ($cacheType) {
                case 'all':
                    Artisan::call('cache:clear');
                    Artisan::call('config:clear');
                    Artisan::call('route:clear');
                    Artisan::call('view:clear');
                    $message = 'All caches cleared successfully!';
                    break;
                    
                case 'config':
                    Artisan::call('config:clear');
                    $message = 'Configuration cache cleared successfully!';
                    break;
                    
                case 'route':
                    Artisan::call('route:clear');
                    $message = 'Route cache cleared successfully!';
                    break;
                    
                case 'view':
                    Artisan::call('view:clear');
                    $message = 'View cache cleared successfully!';
                    break;
                    
                case 'application':
                    Cache::flush();
                    $message = 'Application cache cleared successfully!';
                    break;
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }

        return redirect()->route('admin.settings.cache')
            ->with('success', $message);
    }

    /**
     * Show maintenance mode settings.
     */
    public function maintenance(): View
    {
        $maintenanceMode = SeoSetting::get('maintenance_mode', false);
        $maintenanceMessage = SeoSetting::get('maintenance_message', 'We are currently performing maintenance. Please check back soon.');
        
        return view('admin.settings.maintenance', compact('maintenanceMode', 'maintenanceMessage'));
    }

    /**
     * Toggle maintenance mode.
     */
    public function toggleMaintenance(Request $request): RedirectResponse
    {
        $request->validate([
            'maintenance_message' => 'nullable|string|max:500',
        ]);

        $currentMode = SeoSetting::get('maintenance_mode', false);
        $newMode = !$currentMode;

        SeoSetting::set('maintenance_mode', $newMode, 'boolean', 'system');
        
        if ($request->filled('maintenance_message')) {
            SeoSetting::set('maintenance_message', $request->maintenance_message, 'text', 'system');
        }

        $message = $newMode 
            ? 'Maintenance mode enabled. The site is now in maintenance mode.'
            : 'Maintenance mode disabled. The site is now accessible to users.';

        return redirect()->route('admin.settings.maintenance')
            ->with('success', $message);
    }

    /**
     * Show backup and restore options.
     */
    public function backup(): View
    {
        $backupInfo = $this->getBackupInfo();
        return view('admin.settings.backup', compact('backupInfo'));
    }

    /**
     * Create system backup.
     */
    public function createBackup(Request $request): RedirectResponse
    {
        $request->validate([
            'backup_type' => 'required|in:database,files,full',
        ]);

        try {
            $backupType = $request->get('backup_type');
            $timestamp = now()->format('Y-m-d_H-i-s');
            $backupPath = storage_path('app/backups');

            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            switch ($backupType) {
                case 'database':
                    $this->createDatabaseBackup($backupPath, $timestamp);
                    $message = 'Database backup created successfully!';
                    break;
                    
                case 'files':
                    $this->createFilesBackup($backupPath, $timestamp);
                    $message = 'Files backup created successfully!';
                    break;
                    
                case 'full':
                    $this->createDatabaseBackup($backupPath, $timestamp);
                    $this->createFilesBackup($backupPath, $timestamp);
                    $message = 'Full system backup created successfully!';
                    break;
            }

            return redirect()->route('admin.settings.backup')
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Backup failed: ' . $e->getMessage());
        }
    }

    /**
     * Get system settings.
     */
    private function getSystemSettings(): array
    {
        return [
            'app_name' => SeoSetting::get('app_name', config('app.name')),
            'app_url' => SeoSetting::get('app_url', config('app.url')),
            'app_timezone' => SeoSetting::get('app_timezone', config('app.timezone')),
            'mail_from_address' => SeoSetting::get('mail_from_address', config('mail.from.address')),
            'mail_from_name' => SeoSetting::get('mail_from_name', config('mail.from.name')),
            'default_url_expiry' => SeoSetting::get('default_url_expiry', null),
            'max_urls_per_user' => SeoSetting::get('max_urls_per_user', null),
            'enable_registration' => SeoSetting::get('enable_registration', true),
            'enable_email_verification' => SeoSetting::get('enable_email_verification', true),
            'enable_api' => SeoSetting::get('enable_api', true),
            'enable_qr_codes' => SeoSetting::get('enable_qr_codes', true),
            'enable_analytics' => SeoSetting::get('enable_analytics', true),
            'enable_custom_domains' => SeoSetting::get('enable_custom_domains', false),
            'maintenance_mode' => SeoSetting::get('maintenance_mode', false),
            'maintenance_message' => SeoSetting::get('maintenance_message', 'We are currently performing maintenance. Please check back soon.'),
        ];
    }

    /**
     * Get cache information.
     */
    private function getCacheInfo(): array
    {
        return [
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'cache_size' => $this->getCacheSize(),
            'last_cleared' => Cache::get('cache_last_cleared', 'Never'),
        ];
    }

    /**
     * Get backup information.
     */
    private function getBackupInfo(): array
    {
        $backupPath = storage_path('app/backups');
        $backups = [];

        if (is_dir($backupPath)) {
            $files = glob($backupPath . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    $backups[] = [
                        'name' => basename($file),
                        'size' => $this->formatBytes(filesize($file)),
                        'created' => date('Y-m-d H:i:s', filemtime($file)),
                    ];
                }
            }
        }

        return [
            'backups' => $backups,
            'backup_path' => $backupPath,
            'total_backups' => count($backups),
        ];
    }

    /**
     * Get cache size (simplified).
     */
    private function getCacheSize(): string
    {
        $cacheSize = 0;
        $cachePath = storage_path('framework/cache');
        
        if (is_dir($cachePath)) {
            $cacheSize = $this->getDirSize($cachePath);
        }

        return $this->formatBytes($cacheSize);
    }

    /**
     * Calculate directory size.
     */
    private function getDirSize(string $directory): int
    {
        $size = 0;
        if (is_dir($directory)) {
            foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory)) as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        return $size;
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Create database backup.
     */
    private function createDatabaseBackup(string $backupPath, string $timestamp): void
    {
        $filename = "database_backup_{$timestamp}.sql";
        $filepath = $backupPath . '/' . $filename;
        
        // This is a simplified backup - in production you'd use mysqldump or similar
        $tables = \DB::select('SHOW TABLES');
        $backup = "-- Database Backup Created: " . now() . "\n\n";
        
        file_put_contents($filepath, $backup);
    }

    /**
     * Create files backup.
     */
    private function createFilesBackup(string $backupPath, string $timestamp): void
    {
        $filename = "files_backup_{$timestamp}.zip";
        $filepath = $backupPath . '/' . $filename;
        
        // This is a simplified backup - in production you'd use proper archiving
        $zip = new \ZipArchive();
        if ($zip->open($filepath, \ZipArchive::CREATE) === TRUE) {
            // Add important files to backup
            $filesToBackup = [
                storage_path('app/public'),
                base_path('.env'),
            ];
            
            $zip->close();
        }
    }
}
