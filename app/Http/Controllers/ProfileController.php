<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user profile.
     */
    public function show(): View
    {
        $user = Auth::user();
        
        // Get user statistics
        $stats = [
            'total_urls' => $user->urls()->count(),
            'total_clicks' => $user->urls()->sum('click_count'),
            'account_age' => $user->created_at->diffForHumans(),
            'last_login' => $user->updated_at->diffForHumans(),
        ];

        return view('dashboard.profile.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the user profile.
     */
    public function edit(): View
    {
        $user = Auth::user();
        return view('dashboard.profile.modern-edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        $user->update($request->only(['name', 'email']));

        return redirect()->route('profile.show')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Show the form for changing password.
     */
    public function editPassword(): View
    {
        return view('dashboard.profile.password');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile.show')
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Show API key management.
     */
    public function apiKeys(): View
    {
        $user = Auth::user();
        return view('dashboard.profile.api-keys', compact('user'));
    }

    /**
     * Generate a new API key.
     */
    public function generateApiKey(): RedirectResponse
    {
        $user = Auth::user();
        $apiKey = $user->generateApiKey();

        return redirect()->route('profile.api-keys')
            ->with('success', 'New API key generated successfully!')
            ->with('new_api_key', $apiKey);
    }

    /**
     * Revoke the current API key.
     */
    public function revokeApiKey(): RedirectResponse
    {
        $user = Auth::user();
        $user->update(['api_key' => null]);

        return redirect()->route('profile.api-keys')
            ->with('success', 'API key revoked successfully!');
    }

    /**
     * Show account settings.
     */
    public function settings(): View
    {
        $user = Auth::user();
        return view('dashboard.profile.settings', compact('user'));
    }

    /**
     * Update account settings.
     */
    public function updateSettings(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $request->validate([
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'email_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
        ]);

        // For now, we'll store these in the metadata field
        $metadata = $user->metadata ?? [];
        $metadata['timezone'] = $request->input('timezone', 'UTC');
        $metadata['language'] = $request->input('language', 'en');
        $metadata['email_notifications'] = $request->boolean('email_notifications');
        $metadata['marketing_emails'] = $request->boolean('marketing_emails');

        $user->update(['metadata' => $metadata]);

        return redirect()->route('profile.settings')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Show account deletion confirmation.
     */
    public function deleteAccount(): View
    {
        $user = Auth::user();
        
        // Get data that will be deleted
        $dataToDelete = [
            'urls_count' => $user->urls()->count(),
            'analytics_count' => $user->urls()->withCount('analytics')->get()->sum('analytics_count'),
            'custom_domains_count' => $user->customDomains()->count(),
        ];

        return view('dashboard.profile.delete-account', compact('user', 'dataToDelete'));
    }

    /**
     * Delete the user account.
     */
    public function destroyAccount(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => 'required|current_password',
            'confirmation' => 'required|in:DELETE',
        ]);

        $user = Auth::user();
        
        // Log out the user
        Auth::logout();
        
        // Delete the user (this will cascade delete related data)
        $user->delete();

        return redirect()->route('home')
            ->with('success', 'Your account has been deleted successfully.');
    }
}
