@extends('layouts.dashboard-template')

@section('page_title', 'Edit Profile - Minilink.at')
@section('page_heading')
    <i class="fas fa-edit mr-3 text-blue-600"></i>Edit Profile
@endsection
@section('page_description', 'Update your account information and preferences')

@section('page_actions')
    <a href="{{ route('profile.show') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Profile
    </a>
    <a href="{{ route('dashboard') }}" class="btn-outline">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
    </a>
@endsection

@section('stats_cards')
    <div class="bg-white p-6 rounded-lg border border-gray-200">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-link text-blue-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-600">Total URLs</p>
                <p class="text-2xl font-bold text-gray-900">{{ $user->urls()->count() }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg border border-gray-200">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-mouse-pointer text-green-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-600">Total Clicks</p>
                <p class="text-2xl font-bold text-gray-900">{{ $user->urls()->sum('click_count') }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg border border-gray-200">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-line text-purple-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-600">Active URLs</p>
                <p class="text-2xl font-bold text-gray-900">{{ $user->urls()->where('is_active', true)->count() }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg border border-gray-200">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar text-orange-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-600">Days Active</p>
                <p class="text-2xl font-bold text-gray-900">{{ $user->created_at->diffInDays(now()) }}</p>
            </div>
        </div>
    </div>
@endsection

@section('main_content')
    <!-- Quick Actions -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-bolt mr-2 text-blue-600"></i>Quick Actions
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                <a href="{{ route('profile.password') }}" class="btn-outline flex items-center justify-center text-center">
                    <i class="fas fa-key mr-2 text-blue-500"></i>Change Password
                </a>
                <a href="{{ route('profile.api-keys') }}" class="btn-outline flex items-center justify-center text-center">
                    <i class="fas fa-code mr-2 text-green-500"></i>API Keys
                </a>
                <a href="{{ route('profile.settings') }}" class="btn-outline flex items-center justify-center text-center">
                    <i class="fas fa-cog mr-2 text-purple-500"></i>Settings
                </a>
                <a href="{{ route('profile.notifications') }}" class="btn-outline flex items-center justify-center text-center">
                    <i class="fas fa-bell mr-2 text-orange-500"></i>Notifications
                </a>
                @if(!$user->email_verified_at)
                <form action="{{ route('verification.resend') }}" method="POST" class="w-full">
                    @csrf
                    <button type="submit" class="btn-outline flex items-center justify-center text-center w-full">
                        <i class="fas fa-envelope mr-2 text-yellow-500"></i>Verify Email
                    </button>
                </form>
                @endif
            </div>
        </div>
    </div>



    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Profile Form -->
        <div class="lg:col-span-2">
            <!-- Personal Information Form -->
            <div class="card mb-8">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-edit mr-2 text-green-600"></i>Personal Information
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('profile.update') }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-user mr-1 text-blue-500"></i>Full Name *
                                </label>
                                <input type="text"
                                       class="input-field @error('name') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                       id="name"
                                       name="name"
                                       value="{{ old('name', $user->name) }}"
                                       placeholder="Enter your full name"
                                       required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-envelope mr-1 text-green-500"></i>Email Address *
                                </label>
                                <input type="email"
                                       class="input-field @error('email') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email', $user->email) }}"
                                       placeholder="Enter your email address"
                                       required>
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                @if(!$user->email_verified_at)
                                    <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                            <span class="text-sm text-yellow-700">Your email address is not verified. Please check your inbox for a verification email.</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Account Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user-tag mr-1 text-purple-500"></i>Account Type
                                    </label>
                                    <input type="text"
                                           class="input-field bg-gray-50"
                                           value="{{ ucfirst($user->role) }}"
                                           readonly>
                                    <p class="mt-1 text-sm text-gray-500">Contact support to change your account type.</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-calendar mr-1 text-orange-500"></i>Member Since
                                    </label>
                                    <input type="text"
                                           class="input-field bg-gray-50"
                                           value="{{ $user->created_at->format('M j, Y') }}"
                                           readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="{{ route('profile.show') }}" class="btn-secondary">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save mr-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Column - Profile Overview -->
        <div class="lg:col-span-1">
            <!-- Profile Overview -->
            <div class="card mb-8 sticky top-8">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-user mr-2 text-blue-600"></i>Profile Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <!-- Avatar -->
                        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white font-bold text-2xl">{{ substr($user->name, 0, 1) }}</span>
                        </div>

                        <!-- User Info -->
                        <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ $user->name }}</h4>
                        <p class="text-gray-600 mb-4">{{ $user->email }}</p>

                        <div class="flex flex-col space-y-2">
                            <span class="inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' }}">
                                {{ $user->role === 'admin' ? '👑 Admin' : '👤 User' }}
                            </span>
                            @if($user->email_verified_at)
                                <span class="inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    ✔️ Verified
                                </span>
                            @else
                                <span class="inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    ⏳ Unverified
                                </span>
                            @endif
                            <span class="text-sm text-gray-500">Member since {{ $user->created_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('bottom_section')
    <!-- Email Verification -->
    @if(!$user->email_verified_at)
    <div class="card mb-8">
        <div class="card-header bg-yellow-50 border-yellow-200">
            <h3 class="text-lg font-semibold text-yellow-800">
                <i class="fas fa-envelope mr-2"></i>Email Verification Required
            </h3>
        </div>
        <div class="card-body">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-yellow-800 mb-2">Verify Your Email Address</h4>
                    <p class="text-yellow-700 mb-4">
                        Your email address <strong>{{ $user->email }}</strong> is not verified.
                        Please check your inbox and click the verification link to activate your account.
                    </p>
                    <form action="{{ route('verification.resend') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="btn-warning">
                            <i class="fas fa-paper-plane mr-2"></i>Resend Verification Email
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Danger Zone -->
    <div class="card border-red-200">
        <div class="card-header bg-red-50 border-red-200">
            <h3 class="text-lg font-semibold text-red-800">
                <i class="fas fa-exclamation-triangle mr-2"></i>Danger Zone
            </h3>
        </div>
        <div class="card-body">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-trash text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-red-800 mb-2">Delete Account</h4>
                    <p class="text-red-700 mb-4">
                        Permanently delete your account and all associated data. This action cannot be undone.
                        All your URLs, analytics data, and account information will be permanently removed.
                    </p>
                    <a href="{{ route('profile.delete-account') }}" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Account
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
