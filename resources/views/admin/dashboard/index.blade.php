@extends('layouts.modern')

@section('title', 'Admin Dashboard')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
                    <p class="text-muted mb-0">System overview and management</p>
                </div>
                <div>
                    <a href="{{ route('admin.system-info') }}" class="btn btn-outline-info">
                        <i class="fas fa-info-circle"></i> System Info
                    </a>
                    <a href="{{ route('admin.seo.index') }}" class="btn btn-outline-success">
                        <i class="fas fa-search"></i> SEO Management
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Alert -->
    @if($systemHealth['system_status'] !== 'healthy')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-{{ $systemHealth['system_status'] === 'critical' ? 'danger' : 'warning' }} alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>System Status:</strong> 
                @if($systemHealth['system_status'] === 'critical')
                    Critical issues detected. Immediate attention required.
                @else
                    Warning: System performance issues detected.
                @endif
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Users Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-primary">Total Users</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_users']) }}</h3>
                            <small class="text-muted">
                                +{{ $stats['new_users_today'] }} today
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> {{ $stats['new_users_week'] }} this week
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- URLs Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-success">Total URLs</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_urls']) }}</h3>
                            <small class="text-muted">
                                +{{ $stats['urls_today'] }} today
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-link fa-2x text-success"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> {{ $stats['urls_week'] }} this week
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clicks Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-warning">Total Clicks</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_clicks']) }}</h3>
                            <small class="text-muted">
                                +{{ $stats['clicks_today'] }} today
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-mouse-pointer fa-2x text-warning"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> {{ $stats['clicks_week'] }} this week
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-{{ $systemHealth['system_status'] === 'healthy' ? 'success' : ($systemHealth['system_status'] === 'warning' ? 'warning' : 'danger') }}">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-{{ $systemHealth['system_status'] === 'healthy' ? 'success' : ($systemHealth['system_status'] === 'warning' ? 'warning' : 'danger') }}">System Health</h6>
                            <h3 class="mb-0">
                                @if($systemHealth['system_status'] === 'healthy')
                                    <i class="fas fa-check-circle text-success"></i>
                                @elseif($systemHealth['system_status'] === 'warning')
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                @else
                                    <i class="fas fa-times-circle text-danger"></i>
                                @endif
                            </h3>
                            <small class="text-muted">
                                DB: {{ $systemHealth['database_response_time'] }}ms
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-heartbeat fa-2x text-{{ $systemHealth['system_status'] === 'healthy' ? 'success' : ($systemHealth['system_status'] === 'warning' ? 'warning' : 'danger') }}"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            {{ ucfirst($systemHealth['system_status']) }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="row">
        <!-- Growth Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Growth Metrics (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <canvas id="growthChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                        <a href="{{ route('admin.urls.index') }}" class="btn btn-outline-success">
                            <i class="fas fa-link"></i> Manage URLs
                        </a>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-info">
                            <i class="fas fa-cog"></i> System Settings
                        </a>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-warning">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                        <a href="{{ route('admin.seo.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-search"></i> SEO Management
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Users -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user-plus"></i> Recent Users</h5>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm">View All</a>
                </div>
                <div class="card-body p-0">
                    @if($recentActivity['recent_users']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>User</th>
                                        <th>Status</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentActivity['recent_users'] as $user)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $user->name }}</strong><br>
                                                <small class="text-muted">{{ $user->email }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                            @if($user->email_verified_at)
                                                <span class="badge bg-info">Verified</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $user->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No recent users</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent URLs -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-link"></i> Recent URLs</h5>
                    <a href="{{ route('admin.urls.index') }}" class="btn btn-outline-success btn-sm">View All</a>
                </div>
                <div class="card-body p-0">
                    @if($recentActivity['recent_urls']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>URL</th>
                                        <th>User</th>
                                        <th>Clicks</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentActivity['recent_urls'] as $url)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $url->title ?: 'Untitled' }}</strong><br>
                                                <small class="text-muted">{{ Str::limit($url->original_url, 40) }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $url->user->name }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $url->click_count }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $url->created_at->diffForHumans() }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-link fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No recent URLs</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Growth Chart
const ctx = document.getElementById('growthChart').getContext('2d');
const growthChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: @json($growthMetrics['dates']),
        datasets: [
            {
                label: 'New Users',
                data: @json($growthMetrics['user_growth']),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.1
            },
            {
                label: 'New URLs',
                data: @json($growthMetrics['url_growth']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            },
            {
                label: 'Clicks',
                data: @json($growthMetrics['click_growth']),
                borderColor: 'rgb(255, 205, 86)',
                backgroundColor: 'rgba(255, 205, 86, 0.1)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        }
    }
});
</script>
@endpush
