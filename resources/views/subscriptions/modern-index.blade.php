@extends('layouts.dashboard-template')

@section('page_title', 'Subscription Plans - Minilink.at')
@section('page_heading', 'Choose Your Plan')
@section('page_description', 'Unlock powerful features and scale your link management with our flexible pricing plans.')

@section('page_actions')
    <a href="{{ route('dashboard') }}" class="btn-secondary">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
    </a>
@endsection

@section('main_content')

    <!-- Current Plan Status -->
    @if($currentSubscription)
    <div class="card mb-8">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-crown text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Current Plan</h3>
                        <p class="text-gray-600">
                            You are currently on the <strong>{{ $currentSubscription->name }}</strong> plan
                            @if($currentSubscription->price > 0)
                                ({{ $currentSubscription->getFormattedPriceWithCycle() }})
                            @endif
                        </p>
                    </div>
                </div>
                @if($currentSubscription->price > 0)
                    <form action="{{ route('subscriptions.cancel') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" 
                                class="btn-danger"
                                onclick="return confirm('Are you sure you want to cancel your subscription?')">
                            <i class="fas fa-times mr-2"></i>Cancel Subscription
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Pricing Plans -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12 items-stretch">
        @foreach($subscriptions as $subscription)
        <div class="card {{ $currentSubscription && $currentSubscription->id === $subscription->id ? 'ring-2 ring-blue-500' : '' }} relative flex flex-col h-full min-h-[600px]">
            @if($currentSubscription && $currentSubscription->id === $subscription->id)
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                        Current Plan
                    </span>
                </div>
            @endif
            
            @if($subscription->name === 'Premium')
                <div class="absolute -top-3 right-4">
                    <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                        <i class="fas fa-star mr-1"></i>Popular
                    </span>
                </div>
            @endif

            <div class="card-header text-center">
                <h3 class="text-2xl font-bold text-gray-900">{{ $subscription->name }}</h3>
                <div class="mt-4">
                    @if($subscription->price == 0)
                        <span class="text-4xl font-bold text-gray-900">Free</span>
                    @else
                        <span class="text-4xl font-bold text-gray-900">${{ number_format($subscription->price / 100, 0) }}</span>
                        <span class="text-gray-600">/{{ $subscription->billing_cycle ?? 'month' }}</span>
                    @endif
                </div>
                <p class="text-gray-600 mt-2">{{ $subscription->description }}</p>
            </div>

            <div class="card-body flex flex-col flex-grow">
                <!-- Features List -->
                <ul class="space-y-3 mb-6 flex-grow">
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">
                            @if($subscription->url_limit)
                                {{ number_format($subscription->url_limit) }} URLs per month
                            @else
                                Unlimited URLs
                            @endif
                        </span>
                    </li>
                    
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">
                            @if($subscription->click_limit)
                                {{ number_format($subscription->click_limit) }} clicks per month
                            @else
                                Unlimited clicks
                            @endif
                        </span>
                    </li>

                    @if($subscription->analytics)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">Advanced Analytics</span>
                    </li>
                    @endif

                    @if($subscription->custom_domains)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">Custom Domains</span>
                    </li>
                    @endif

                    @if($subscription->api_access)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">API Access</span>
                    </li>
                    @endif

                    @if($subscription->bulk_operations)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">Bulk Operations</span>
                    </li>
                    @endif

                    @if($subscription->password_protection)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">Password Protection</span>
                    </li>
                    @endif

                    @if($subscription->expiration_dates)
                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">Expiration Dates</span>
                    </li>
                    @endif

                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">QR Code Generation</span>
                    </li>

                    <li class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span class="text-gray-700">24/7 Support</span>
                    </li>
                </ul>

                <!-- Action Button -->
                <div class="mt-auto">
                @if($currentSubscription && $currentSubscription->id === $subscription->id)
                    <button class="btn-secondary w-full" disabled>
                        <i class="fas fa-check mr-2"></i>Current Plan
                    </button>
                @elseif($subscription->price == 0)
                    @if($currentSubscription && $currentSubscription->price > 0)
                        <form action="{{ route('subscriptions.cancel') }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="btn-outline w-full"
                                    onclick="return confirm('This will downgrade you to the free plan. Are you sure?')">
                                <i class="fas fa-arrow-down mr-2"></i>Downgrade to Free
                            </button>
                        </form>
                    @else
                        <button class="btn-secondary w-full" disabled>
                            <i class="fas fa-check mr-2"></i>Current Plan
                        </button>
                    @endif
                @else
                    <form action="{{ route('subscriptions.upgrade', $subscription) }}" method="POST">
                        @csrf
                        <button type="submit" class="btn-primary w-full">
                            @if($currentSubscription && $currentSubscription->price > 0)
                                <i class="fas fa-arrow-up mr-2"></i>
                                @if($subscription->price > $currentSubscription->price)
                                    Upgrade to {{ $subscription->name }}
                                @else
                                    Change to {{ $subscription->name }}
                                @endif
                            @else
                                <i class="fas fa-rocket mr-2"></i>Get Started
                            @endif
                        </button>
                    </form>
                @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Features Comparison -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-2xl font-bold text-gray-900 text-center">Feature Comparison</h3>
        </div>
        <div class="card-body overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-semibold text-gray-900">Feature</th>
                        @foreach($subscriptions as $subscription)
                        <th class="text-center py-3 px-4 font-semibold text-gray-900">{{ $subscription->name }}</th>
                        @endforeach
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">URLs per month</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->url_limit)
                                {{ number_format($subscription->url_limit) }}
                            @else
                                <span class="text-green-600 font-semibold">Unlimited</span>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">Clicks per month</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->click_limit)
                                {{ number_format($subscription->click_limit) }}
                            @else
                                <span class="text-green-600 font-semibold">Unlimited</span>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">Advanced Analytics</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->analytics)
                                <i class="fas fa-check text-green-500"></i>
                            @else
                                <i class="fas fa-times text-red-500"></i>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">Custom Domains</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->custom_domains)
                                <i class="fas fa-check text-green-500"></i>
                            @else
                                <i class="fas fa-times text-red-500"></i>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">API Access</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->api_access)
                                <i class="fas fa-check text-green-500"></i>
                            @else
                                <i class="fas fa-times text-red-500"></i>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium text-gray-900">Bulk Operations</td>
                        @foreach($subscriptions as $subscription)
                        <td class="text-center py-3 px-4">
                            @if($subscription->bulk_operations)
                                <i class="fas fa-check text-green-500"></i>
                            @else
                                <i class="fas fa-times text-red-500"></i>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-12">
        <h3 class="text-2xl font-bold text-gray-900 text-center mb-8">Frequently Asked Questions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="font-semibold text-gray-900 mb-2">Can I change my plan anytime?</h4>
                    <p class="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h4 class="font-semibold text-gray-900 mb-2">What happens to my links if I downgrade?</h4>
                    <p class="text-gray-600">Your existing links will continue to work, but you'll be limited by your new plan's features.</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h4 class="font-semibold text-gray-900 mb-2">Do you offer refunds?</h4>
                    <p class="text-gray-600">We offer a 30-day money-back guarantee for all paid plans. No questions asked.</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h4 class="font-semibold text-gray-900 mb-2">Is there a setup fee?</h4>
                    <p class="text-gray-600">No setup fees. You only pay the monthly subscription price for your chosen plan.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
