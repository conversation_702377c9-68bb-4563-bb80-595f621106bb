@extends('layouts.modern')

@section('title', 'Minilink.at - Professional URL Shortener')
@section('description', 'Create short, branded URLs with advanced analytics, custom domains, and team collaboration features.')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
                Shorten URLs.<br>
                <span class="text-blue-200">Amplify Results.</span>
            </h1>
            <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto animate-slide-up">
                Transform your long URLs into powerful, branded short links with advanced analytics,
                custom domains, and team collaboration features.
            </p>

            @guest
                <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
                    <a href="{{ route('register') }}" class="bg-white text-blue-600 hover:bg-blue-50 font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                        Get Started Free
                    </a>
                    <a href="#features" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-4 px-8 rounded-lg transition-all duration-200">
                        Learn More
                    </a>
                </div>
            @else
                <div class="animate-slide-up">
                    <a href="{{ route('dashboard') }}" class="bg-white text-blue-600 hover:bg-blue-50 font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                        Go to Dashboard
                    </a>
                </div>
            @endguest
        </div>
    </div>
</div>

<!-- URL Shortening Form -->
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10 relative z-10">
    <div class="card">
        <div class="card-body">
            <form id="shortenForm" action="{{ route('urls.store') }}" method="POST" class="space-y-6">
                @csrf
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div class="lg:col-span-3">
                        <label for="original_url" class="block text-sm font-medium text-gray-700 mb-2">
                            Enter your long URL
                        </label>
                        <input type="url"
                               class="input-field @error('original_url') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               id="original_url"
                               name="original_url"
                               placeholder="https://example.com/very-long-url"
                               value="{{ old('original_url') }}"
                               required>
                        @error('original_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="lg:col-span-1">
                        <label class="block text-sm font-medium text-secondary-700 mb-2">&nbsp;</label>
                        <button type="submit" class="btn-primary w-full">
                            <i class="fas fa-magic mr-2"></i>Shorten URL
                        </button>
                    </div>
                </div>

                <!-- Advanced Options (Collapsible) -->
                <div x-data="{ showAdvanced: false }" class="border-t border-secondary-200 pt-6">
                    <button type="button" @click="showAdvanced = !showAdvanced" class="flex items-center text-gray-600 hover:text-gray-900 font-medium">
                        <i class="fas fa-cog mr-2"></i>
                        Advanced Options
                        <i class="fas fa-chevron-down ml-2 transform transition-transform" :class="{ 'rotate-180': showAdvanced }"></i>
                    </button>
                    
                    <div x-show="showAdvanced" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="custom_alias" class="block text-sm font-medium text-secondary-700 mb-2">
                                Custom Alias (Optional)
                            </label>
                            <input type="text" 
                                   class="input-field" 
                                   id="custom_alias" 
                                   name="custom_alias" 
                                   placeholder="my-custom-link"
                                   value="{{ old('custom_alias') }}">
                        </div>
                        
                        <div>
                            <label for="title" class="block text-sm font-medium text-secondary-700 mb-2">
                                Title (Optional)
                            </label>
                            <input type="text" 
                                   class="input-field" 
                                   id="title" 
                                   name="title" 
                                   placeholder="My Link Title"
                                   value="{{ old('title') }}">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                                Description (Optional)
                            </label>
                            <textarea class="input-field" 
                                      id="description" 
                                      name="description" 
                                      rows="3" 
                                      placeholder="Brief description of your link">{{ old('description') }}</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Features Section -->
<div id="features" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
                Powerful Features for Modern Teams
            </h2>
            <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
                Everything you need to create, manage, and analyze your short links at scale.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                    <i class="fas fa-chart-line text-2xl text-primary-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">Advanced Analytics</h3>
                <p class="text-secondary-600">
                    Track clicks, geographic data, devices, and referrers with detailed insights and real-time reporting.
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-success-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-success-200 transition-colors">
                    <i class="fas fa-globe text-2xl text-success-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">Custom Domains</h3>
                <p class="text-secondary-600">
                    Use your own branded domains to create professional short links that build trust and recognition.
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-warning-200 transition-colors">
                    <i class="fas fa-users text-2xl text-warning-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">Team Collaboration</h3>
                <p class="text-secondary-600">
                    Work together with your team, share links, and manage permissions with enterprise-grade features.
                </p>
            </div>
            
            <!-- Feature 4 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                    <i class="fas fa-qrcode text-2xl text-purple-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">QR Codes</h3>
                <p class="text-secondary-600">
                    Generate beautiful QR codes for your short links with customizable designs and branding options.
                </p>
            </div>
            
            <!-- Feature 5 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-indigo-200 transition-colors">
                    <i class="fas fa-code text-2xl text-indigo-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">Developer API</h3>
                <p class="text-secondary-600">
                    Integrate with your applications using our comprehensive REST API with authentication and rate limiting.
                </p>
            </div>
            
            <!-- Feature 6 -->
            <div class="text-center group">
                <div class="w-16 h-16 bg-pink-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-pink-200 transition-colors">
                    <i class="fas fa-layer-group text-2xl text-pink-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-2">Bulk Operations</h3>
                <p class="text-secondary-600">
                    Create, edit, and manage thousands of links at once with our powerful bulk operation tools.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="py-16 bg-secondary-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">1M+</div>
                <div class="text-secondary-600">Links Created</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">50M+</div>
                <div class="text-secondary-600">Clicks Tracked</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">10K+</div>
                <div class="text-secondary-600">Happy Users</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">99.9%</div>
                <div class="text-secondary-600">Uptime</div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="py-20 gradient-bg">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Get Started?
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            Join thousands of professionals who trust Minilink.at for their URL shortening needs.
        </p>
        
        @guest
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                    Start Free Trial
                </a>
                <a href="{{ route('subscriptions.index') }}" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-4 px-8 rounded-lg transition-all duration-200">
                    View Pricing
                </a>
            </div>
        @else
            <a href="{{ route('dashboard') }}" class="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                Go to Dashboard
            </a>
        @endguest
    </div>
</div>

@if(session('shortened_url'))
<!-- Success Modal -->
<div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4 animate-scale-in">
        <div class="text-center">
            <div class="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-2xl text-success-600"></i>
            </div>
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">URL Shortened Successfully!</h3>
            <p class="text-secondary-600 mb-6">Your short URL is ready to use:</p>
            
            <div class="bg-secondary-50 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between">
                    <span class="font-mono text-primary-600">{{ session('shortened_url') }}</span>
                    <button onclick="copyToClipboard('{{ session('shortened_url') }}')" class="text-secondary-400 hover:text-secondary-600">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
            
            <div class="flex gap-3">
                <button onclick="closeModal()" class="btn-secondary flex-1">Close</button>
                <a href="{{ session('shortened_url') }}" target="_blank" class="btn-primary flex-1">Test Link</a>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = event.target;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('text-success-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-success-600');
        }, 2000);
    });
}

function closeModal() {
    document.getElementById('successModal').style.display = 'none';
}

// Auto-hide modal after 10 seconds
@if(session('shortened_url'))
setTimeout(() => {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.display = 'none';
    }
}, 10000);
@endif
</script>
@endpush
