<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class TeamController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display team dashboard.
     */
    public function index(): View
    {
        $user = Auth::user();
        
        // Check if user has team features
        if (!$user->subscription?->team_features) {
            return view('teams.upgrade-required');
        }

        $ownedTeams = $user->ownedTeams()->with('members.user')->get();
        $memberTeams = $user->teams()->with('owner', 'members.user')->get();

        return view('teams.index', compact('ownedTeams', 'memberTeams'));
    }

    /**
     * Show the form for creating a new team.
     */
    public function create(): View
    {
        $user = Auth::user();
        
        if (!$user->subscription?->team_features) {
            return view('teams.upgrade-required');
        }

        // Check team limit
        $teamLimit = $user->subscription->team_limit ?? 1;
        if ($user->ownedTeams()->count() >= $teamLimit) {
            return redirect()->route('teams.index')
                ->with('error', 'You have reached your team limit. Upgrade your subscription to create more teams.');
        }

        return view('teams.create');
    }

    /**
     * Store a newly created team.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->team_features) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Team features require a Premium subscription.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        $team = $user->ownedTeams()->create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('teams.show', $team)
            ->with('success', 'Team created successfully!');
    }

    /**
     * Display the specified team.
     */
    public function show(Team $team): View
    {
        $this->authorize('view', $team);

        $team->load(['members.user', 'urls' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        $stats = [
            'total_members' => $team->members()->count(),
            'total_urls' => $team->urls()->count(),
            'total_clicks' => $team->urls()->sum('click_count'),
            'active_urls' => $team->urls()->where('is_active', true)->count(),
        ];

        return view('teams.show', compact('team', 'stats'));
    }

    /**
     * Show the form for editing the team.
     */
    public function edit(Team $team): View
    {
        $this->authorize('update', $team);

        return view('teams.edit', compact('team'));
    }

    /**
     * Update the specified team.
     */
    public function update(Request $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        $team->update($request->only(['name', 'description']));

        return redirect()->route('teams.show', $team)
            ->with('success', 'Team updated successfully!');
    }

    /**
     * Remove the specified team.
     */
    public function destroy(Team $team): RedirectResponse
    {
        $this->authorize('delete', $team);

        // Check if team has URLs
        if ($team->urls()->count() > 0) {
            return back()->with('error', 'Cannot delete team with existing URLs. Please reassign or delete URLs first.');
        }

        $teamName = $team->name;
        $team->delete();

        return redirect()->route('teams.index')
            ->with('success', "Team '{$teamName}' deleted successfully!");
    }

    /**
     * Invite a user to the team.
     */
    public function invite(Request $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        $request->validate([
            'email' => 'required|email',
            'role' => 'required|in:member,admin',
        ]);

        $email = $request->email;
        $role = $request->role;

        // Check if user exists
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            return back()->with('error', 'User with this email does not exist.');
        }

        // Check if user is already a member
        if ($team->members()->where('user_id', $user->id)->exists()) {
            return back()->with('error', 'User is already a member of this team.');
        }

        // Check team member limit
        $memberLimit = $team->owner->subscription->team_member_limit ?? 5;
        if ($team->members()->count() >= $memberLimit) {
            return back()->with('error', 'Team member limit reached. Upgrade subscription to add more members.');
        }

        // Create team member
        $team->members()->create([
            'user_id' => $user->id,
            'role' => $role,
            'invited_by' => Auth::id(),
            'invited_at' => now(),
        ]);

        // Send invitation email (in production)
        // Mail::to($user)->send(new TeamInvitation($team, $user, Auth::user()));

        return back()->with('success', "Invitation sent to {$email}!");
    }

    /**
     * Update team member role.
     */
    public function updateMember(Request $request, Team $team, TeamMember $member): RedirectResponse
    {
        $this->authorize('update', $team);

        $request->validate([
            'role' => 'required|in:member,admin',
        ]);

        $member->update(['role' => $request->role]);

        return back()->with('success', 'Member role updated successfully!');
    }

    /**
     * Remove a member from the team.
     */
    public function removeMember(Team $team, TeamMember $member): RedirectResponse
    {
        $this->authorize('update', $team);

        // Cannot remove team owner
        if ($member->user_id === $team->owner_id) {
            return back()->with('error', 'Cannot remove team owner.');
        }

        $userName = $member->user->name;
        $member->delete();

        return back()->with('success', "{$userName} removed from team successfully!");
    }

    /**
     * Leave a team.
     */
    public function leave(Team $team): RedirectResponse
    {
        $user = Auth::user();
        
        // Cannot leave if user is the owner
        if ($team->owner_id === $user->id) {
            return back()->with('error', 'Team owners cannot leave their own team. Transfer ownership or delete the team.');
        }

        $member = $team->members()->where('user_id', $user->id)->first();
        
        if (!$member) {
            return back()->with('error', 'You are not a member of this team.');
        }

        $member->delete();

        return redirect()->route('teams.index')
            ->with('success', "You have left the team '{$team->name}'.");
    }

    /**
     * Transfer team ownership.
     */
    public function transferOwnership(Request $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        $request->validate([
            'new_owner_id' => 'required|exists:users,id',
        ]);

        $newOwnerId = $request->new_owner_id;
        
        // Check if new owner is a team member
        $member = $team->members()->where('user_id', $newOwnerId)->first();
        if (!$member) {
            return back()->with('error', 'New owner must be a team member.');
        }

        // Check if new owner has team features
        $newOwner = User::find($newOwnerId);
        if (!$newOwner->subscription?->team_features) {
            return back()->with('error', 'New owner must have a subscription that supports team features.');
        }

        // Transfer ownership
        $oldOwner = $team->owner;
        $team->update(['owner_id' => $newOwnerId]);

        // Add old owner as admin member
        $team->members()->create([
            'user_id' => $oldOwner->id,
            'role' => 'admin',
            'invited_by' => $newOwnerId,
            'invited_at' => now(),
        ]);

        // Remove new owner from members (they're now the owner)
        $member->delete();

        return back()->with('success', "Team ownership transferred to {$newOwner->name}!");
    }

    /**
     * Show team analytics.
     */
    public function analytics(Team $team): View
    {
        $this->authorize('view', $team);

        $stats = [
            'total_urls' => $team->urls()->count(),
            'total_clicks' => $team->urls()->sum('click_count'),
            'active_urls' => $team->urls()->where('is_active', true)->count(),
            'clicks_today' => $team->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->whereDate('analytics.clicked_at', today())
                ->count(),
            'clicks_week' => $team->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->where('analytics.clicked_at', '>=', now()->subWeek())
                ->count(),
            'top_performers' => $team->urls()
                ->orderBy('click_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'short_code', 'click_count']),
            'member_contributions' => $team->members()
                ->with('user')
                ->get()
                ->map(function ($member) {
                    return [
                        'user' => $member->user,
                        'urls_created' => $member->user->urls()->where('team_id', $member->team_id)->count(),
                        'total_clicks' => $member->user->urls()->where('team_id', $member->team_id)->sum('click_count'),
                    ];
                }),
        ];

        return view('teams.analytics', compact('team', 'stats'));
    }

    /**
     * Show team settings.
     */
    public function settings(Team $team): View
    {
        $this->authorize('update', $team);

        return view('teams.settings', compact('team'));
    }

    /**
     * Update team settings.
     */
    public function updateSettings(Request $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        $request->validate([
            'allow_member_invites' => 'boolean',
            'require_approval' => 'boolean',
            'default_url_privacy' => 'required|in:public,private,team',
        ]);

        $settings = $team->settings ?? [];
        $settings['allow_member_invites'] = $request->boolean('allow_member_invites');
        $settings['require_approval'] = $request->boolean('require_approval');
        $settings['default_url_privacy'] = $request->default_url_privacy;

        $team->update(['settings' => $settings]);

        return back()->with('success', 'Team settings updated successfully!');
    }

    /**
     * Get team usage statistics.
     */
    public function usage(Team $team): View
    {
        $this->authorize('view', $team);

        $subscription = $team->owner->subscription;
        
        $usage = [
            'members' => [
                'current' => $team->members()->count(),
                'limit' => $subscription->team_member_limit ?? 5,
            ],
            'urls' => [
                'current' => $team->urls()->count(),
                'limit' => $subscription->url_limit,
            ],
            'storage' => [
                'current' => $team->urls()->whereNotNull('qr_code_path')->count(),
                'limit' => $subscription->storage_limit ?? 1000,
            ],
        ];

        return view('teams.usage', compact('team', 'usage'));
    }
}
