<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SeoSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("seo_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->where('is_active', true)->first();
            
            if (!$setting) {
                return $default;
            }

            return match ($setting->type) {
                'boolean' => (bool) $setting->value,
                'json' => json_decode($setting->value, true),
                default => $setting->value,
            };
        });
    }

    /**
     * Set a setting value.
     */
    public static function set(string $key, $value, string $type = 'text', string $category = 'general', string $description = null): self
    {
        $processedValue = match ($type) {
            'boolean' => $value ? '1' : '0',
            'json' => json_encode($value),
            default => (string) $value,
        };

        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $processedValue,
                'type' => $type,
                'category' => $category,
                'description' => $description,
                'is_active' => true,
            ]
        );

        // Clear cache
        Cache::forget("seo_setting_{$key}");

        return $setting;
    }

    /**
     * Get all settings by category.
     */
    public static function getByCategory(string $category): array
    {
        return Cache::remember("seo_settings_category_{$category}", 3600, function () use ($category) {
            $settings = self::where('category', $category)
                          ->where('is_active', true)
                          ->get();

            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = match ($setting->type) {
                    'boolean' => (bool) $setting->value,
                    'json' => json_decode($setting->value, true),
                    default => $setting->value,
                };
            }

            return $result;
        });
    }

    /**
     * Clear all SEO settings cache.
     */
    public static function clearCache(): void
    {
        $keys = self::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("seo_setting_{$key}");
        }

        $categories = self::distinct('category')->pluck('category');
        foreach ($categories as $category) {
            Cache::forget("seo_settings_category_{$category}");
        }
    }

    /**
     * Get default SEO settings.
     */
    public static function getDefaults(): array
    {
        return [
            // General Meta
            'site_title' => [
                'value' => 'Minilink.at - Professional URL Shortener',
                'type' => 'text',
                'category' => 'meta',
                'description' => 'Default site title'
            ],
            'site_description' => [
                'value' => 'Shorten your URLs with Minilink.at - Fast, reliable, and feature-rich URL shortening service with analytics, QR codes, and custom domains',
                'type' => 'textarea',
                'category' => 'meta',
                'description' => 'Default site description'
            ],
            'site_keywords' => [
                'value' => 'url shortener, short links, link management, qr codes, analytics',
                'type' => 'text',
                'category' => 'meta',
                'description' => 'Default site keywords'
            ],
            'site_author' => [
                'value' => 'Minilink.at Team',
                'type' => 'text',
                'category' => 'meta',
                'description' => 'Site author'
            ],

            // Open Graph
            'og_site_name' => [
                'value' => 'Minilink.at',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Open Graph site name'
            ],
            'og_image' => [
                'value' => '/images/og-image.jpg',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Default Open Graph image'
            ],
            'og_image_width' => [
                'value' => '1200',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Open Graph image width'
            ],
            'og_image_height' => [
                'value' => '630',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Open Graph image height'
            ],

            // Twitter Card
            'twitter_card' => [
                'value' => 'summary_large_image',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Twitter card type'
            ],
            'twitter_site' => [
                'value' => '@minilinkat',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Twitter site handle'
            ],
            'twitter_creator' => [
                'value' => '@minilinkat',
                'type' => 'text',
                'category' => 'social',
                'description' => 'Twitter creator handle'
            ],

            // Analytics
            'google_analytics_id' => [
                'value' => '',
                'type' => 'text',
                'category' => 'analytics',
                'description' => 'Google Analytics 4 Measurement ID'
            ],
            'google_tag_manager_id' => [
                'value' => '',
                'type' => 'text',
                'category' => 'analytics',
                'description' => 'Google Tag Manager Container ID'
            ],
            'facebook_pixel_id' => [
                'value' => '',
                'type' => 'text',
                'category' => 'analytics',
                'description' => 'Facebook Pixel ID'
            ],

            // Verification
            'google_site_verification' => [
                'value' => '',
                'type' => 'text',
                'category' => 'verification',
                'description' => 'Google Search Console verification code'
            ],
            'bing_site_verification' => [
                'value' => '',
                'type' => 'text',
                'category' => 'verification',
                'description' => 'Bing Webmaster Tools verification code'
            ],
            'facebook_domain_verification' => [
                'value' => '',
                'type' => 'text',
                'category' => 'verification',
                'description' => 'Facebook domain verification code'
            ],

            // General Settings
            'enable_sitemap' => [
                'value' => '1',
                'type' => 'boolean',
                'category' => 'general',
                'description' => 'Enable XML sitemap generation'
            ],
            'enable_robots_txt' => [
                'value' => '1',
                'type' => 'boolean',
                'category' => 'general',
                'description' => 'Enable robots.txt'
            ],
            'enable_schema_markup' => [
                'value' => '1',
                'type' => 'boolean',
                'category' => 'general',
                'description' => 'Enable Schema.org markup'
            ],
        ];
    }

    /**
     * Seed default settings.
     */
    public static function seedDefaults(): void
    {
        $defaults = self::getDefaults();

        foreach ($defaults as $key => $config) {
            self::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'category' => $config['category'],
                    'description' => $config['description'],
                    'is_active' => true,
                ]
            );
        }
    }
}
