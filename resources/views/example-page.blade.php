{{-- 
    EXAMPLE PAGE TEMPLATE
    This file demonstrates how to create new pages using the admin-modern layout
    Copy this template and modify it for new pages
--}}

@extends('layouts.admin-modern')

@section('title', 'Example Page - Minilink.at')
@section('description', 'This is an example page demonstrating the modern design system')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-star mr-3 text-blue-600"></i>
                    Example Page
                </h1>
                <p class="text-gray-600 mt-1">This demonstrates the modern design system and layout patterns</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
                <a href="{{ route('dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <button class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Create New
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Card 1 -->
        <div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">1,234</div>
                <div class="text-blue-100 text-sm">Total Users</div>
            </div>
        </div>

        <!-- Card 2 -->
        <div class="card bg-gradient-to-r from-green-500 to-green-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-link text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">5,678</div>
                <div class="text-green-100 text-sm">Total Links</div>
            </div>
        </div>

        <!-- Card 3 -->
        <div class="card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-mouse-pointer text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">12,345</div>
                <div class="text-purple-100 text-sm">Total Clicks</div>
            </div>
        </div>

        <!-- Card 4 -->
        <div class="card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">98.5%</div>
                <div class="text-orange-100 text-sm">Uptime</div>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Chart Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                    Sample Chart
                </h3>
            </div>
            <div class="card-body">
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500">Chart would go here</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-edit mr-2 text-green-600"></i>
                    Sample Form
                </h3>
            </div>
            <div class="card-body">
                <form class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                        <input type="text" id="name" class="input-field" placeholder="Enter your name">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="email" class="input-field" placeholder="Enter your email">
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category" class="input-field">
                            <option>Select a category</option>
                            <option>Option 1</option>
                            <option>Option 2</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="btn-secondary">Cancel</button>
                        <button type="submit" class="btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-table mr-2 text-purple-600"></i>
                    Sample Data Table
                </h3>
                <div class="flex space-x-2">
                    <button class="btn-outline">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                    <button class="btn-primary">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                Sample Item 1
                            </td>
                            <td class="px-6 py-4">
                                <span class="badge badge-success">Active</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                Jan 15, 2024
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-700">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-700">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                Sample Item 2
                            </td>
                            <td class="px-6 py-4">
                                <span class="badge badge-warning">Pending</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                Jan 14, 2024
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-700">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-700">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Interactive Elements Demo -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <!-- Dropdown Demo -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Interactive Elements</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <!-- Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="btn-outline w-full justify-between">
                            Dropdown Menu
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div x-show="open" 
                             @click.away="open = false"
                             x-transition
                             class="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Option 1
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Option 2
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Option 3
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Badges -->
                    <div class="flex flex-wrap gap-2">
                        <span class="badge badge-primary">Primary</span>
                        <span class="badge badge-success">Success</span>
                        <span class="badge badge-warning">Warning</span>
                        <span class="badge badge-danger">Danger</span>
                        <span class="badge badge-info">Info</span>
                    </div>

                    <!-- Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <button class="btn-primary">Primary</button>
                        <button class="btn-secondary">Secondary</button>
                        <button class="btn-outline">Outline</button>
                        <button class="btn-danger">Danger</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Demo -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Progress Indicators</h3>
            </div>
            <div class="card-body">
                <div class="space-y-6">
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700">Progress 1</span>
                            <span class="text-sm text-gray-500">75%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700">Progress 2</span>
                            <span class="text-sm text-gray-500">50%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 50%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700">Progress 3</span>
                            <span class="text-sm text-gray-500">90%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Example JavaScript functionality
console.log('Example page loaded with modern design system');

// Example of interactive functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add any page-specific JavaScript here
});
</script>
@endpush
