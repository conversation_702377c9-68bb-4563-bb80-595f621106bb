@extends('layouts.dashboard-template')

@section('page_title', 'User Management - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-users mr-3 text-blue-600"></i>User Management
@endsection
@section('page_description', 'Manage all users and their accounts')

@section('page_actions')
    <a href="{{ route('admin.dashboard') }}" class="btn-secondary">
        <i class="fas fa-tachometer-alt mr-2"></i>Admin Dashboard
    </a>
    <a href="{{ route('admin.users.create') }}" class="btn-primary">
        <i class="fas fa-plus mr-2"></i>Create User
    </a>
@endsection

@section('main_content')

    <!-- Filters and Search -->
    <div class="card mb-8">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i>
                    Filters & Search
                </h3>
                <div class="text-sm text-gray-500">
                    {{ $users->total() }} users found
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.users.index') }}" id="filterForm">
                <!-- Main Filters - All in Same Line -->
                <div class="flex flex-wrap items-end gap-4 mb-6">
                    <!-- Search -->
                    <div class="flex-1 min-w-[200px] max-w-[300px]">
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search mr-1 text-blue-500"></i>Search Users
                        </label>
                        <input type="text"
                               class="input-field w-full"
                               id="search"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search by name, email, or ID...">
                    </div>

                    <!-- Role Filter -->
                    <div class="min-w-[140px] flex-1">
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user-tag mr-1 text-green-500"></i>Role
                        </label>
                        <select class="input-field w-full" id="role" name="role" onchange="this.form.submit()">
                            <option value="">All Roles</option>
                            <option value="user" {{ request('role') === 'user' ? 'selected' : '' }}>
                                👤 User ({{ $filterCounts['user'] ?? 0 }})
                            </option>
                            <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>
                                👑 Admin ({{ $filterCounts['admin'] ?? 0 }})
                            </option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="min-w-[140px] flex-1">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-toggle-on mr-1 text-purple-500"></i>Status
                        </label>
                        <select class="input-field w-full" id="status" name="status" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                                ✅ Active ({{ $filterCounts['active'] ?? 0 }})
                            </option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                                ❌ Inactive ({{ $filterCounts['inactive'] ?? 0 }})
                            </option>
                            <option value="verified" {{ request('status') === 'verified' ? 'selected' : '' }}>
                                ✔️ Verified ({{ $filterCounts['verified'] ?? 0 }})
                            </option>
                            <option value="unverified" {{ request('status') === 'unverified' ? 'selected' : '' }}>
                                ⏳ Unverified ({{ $filterCounts['unverified'] ?? 0 }})
                            </option>
                        </select>
                    </div>

                    <!-- Search Button -->
                    <div class="flex-shrink-0">
                        <button type="submit" class="btn-primary flex items-center">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters Row - All in Same Line -->
                <div class="flex flex-wrap items-end gap-4 mb-6">
                    <!-- Sort By -->
                    <div class="flex-1 min-w-[150px]">
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-sort mr-1 text-blue-500"></i>Sort By
                        </label>
                        <select class="input-field w-full" id="sort_by" name="sort_by" onchange="this.form.submit()">
                            <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>📅 Date Joined</option>
                            <option value="name" {{ request('sort_by') === 'name' ? 'selected' : '' }}>👤 Name</option>
                            <option value="email" {{ request('sort_by') === 'email' ? 'selected' : '' }}>📧 Email</option>
                            <option value="role" {{ request('sort_by') === 'role' ? 'selected' : '' }}>🏷️ Role</option>
                        </select>
                    </div>

                    <!-- Sort Order -->
                    <div class="flex-1 min-w-[120px]">
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-arrow-up-down mr-1 text-green-500"></i>Order
                        </label>
                        <select class="input-field w-full" id="sort_order" name="sort_order" onchange="this.form.submit()">
                            <option value="desc" {{ request('sort_order') === 'desc' ? 'selected' : '' }}>⬇️ Descending</option>
                            <option value="asc" {{ request('sort_order') === 'asc' ? 'selected' : '' }}>⬆️ Ascending</option>
                        </select>
                    </div>

                    <!-- Per Page -->
                    <div class="flex-1 min-w-[120px]">
                        <label for="per_page" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-list mr-1 text-purple-500"></i>Per Page
                        </label>
                        <select class="input-field w-full" id="per_page" name="per_page" onchange="this.form.submit()">
                            <option value="10" {{ request('per_page') == '10' ? 'selected' : '' }}>10 users</option>
                            <option value="25" {{ request('per_page') == '25' ? 'selected' : '' }}>25 users</option>
                            <option value="50" {{ request('per_page') == '50' ? 'selected' : '' }}>50 users</option>
                            <option value="100" {{ request('per_page') == '100' ? 'selected' : '' }}>100 users</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 pt-6 border-t border-gray-200">
                    <div class="flex flex-wrap gap-3">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-search mr-2"></i>Apply Filters
                        </button>
                        <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                            <i class="fas fa-undo mr-2"></i>Clear All
                        </a>
                    </div>

                    <div class="flex flex-wrap gap-3">
                        <!-- Bulk Actions Dropdown -->
                        <div class="relative">
                            <button type="button"
                                    id="bulkActionsBtn"
                                    class="btn-outline flex items-center"
                                    onclick="toggleBulkDropdown(event)"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                <i class="fas fa-cog mr-2"></i>Bulk Actions
                                <span id="selectedCount" class="ml-1 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full hidden">0</span>
                                <i class="fas fa-chevron-down ml-2 transition-transform duration-200" id="bulkChevron"></i>
                            </button>

                            <div id="bulkDropdown"
                                 class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 z-50 hidden"
                                 role="menu"
                                 aria-orientation="vertical">
                                <div class="py-2">
                                    <button type="button"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
                                            onclick="bulkAction('activate')"
                                            role="menuitem">
                                        <i class="fas fa-check mr-3 text-green-500"></i>Activate Selected
                                    </button>
                                    <button type="button"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
                                            onclick="bulkAction('deactivate')"
                                            role="menuitem">
                                        <i class="fas fa-pause mr-3 text-yellow-500"></i>Deactivate Selected
                                    </button>
                                    <button type="button"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
                                            onclick="bulkAction('verify')"
                                            role="menuitem">
                                        <i class="fas fa-shield-check mr-3 text-blue-500"></i>Verify Email
                                    </button>
                                    <button type="button"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors"
                                            onclick="bulkAction('unverify')"
                                            role="menuitem">
                                        <i class="fas fa-shield-times mr-3 text-orange-500"></i>Unverify Email
                                    </button>
                                    <hr class="my-2 border-gray-200">
                                    <button type="button"
                                            class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors"
                                            onclick="bulkAction('delete')"
                                            role="menuitem">
                                        <i class="fas fa-trash mr-3"></i>Delete Selected
                                    </button>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('admin.users.create') }}" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Add User
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-list mr-2"></i>Users
                    <span class="badge badge-secondary ml-2">{{ $users->total() }}</span>
                </h3>
                <div class="flex items-center">
                    <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600" id="selectAll">
                    <label for="selectAll" class="ml-2 text-sm text-gray-700">Select All</label>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($users->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                    <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600" id="selectAllHeader">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Details</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role & Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($users as $user)
                            <tr class="hover:bg-gray-50 transition-colors {{ !$user->is_active ? 'bg-yellow-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 user-checkbox" value="{{ $user->id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                                <span class="text-white font-semibold text-sm">{{ substr($user->name, 0, 1) }}</span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                            @if($user->api_key)
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                                    <i class="fas fa-key mr-1"></i>API Access
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="space-y-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800' }}">
                                            @if($user->role === 'admin')
                                                <i class="fas fa-crown mr-1"></i>Admin
                                            @else
                                                <i class="fas fa-user mr-1"></i>User
                                            @endif
                                        </span>
                                        <div class="flex flex-wrap gap-1">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                @if($user->is_active)
                                                    <i class="fas fa-check-circle mr-1"></i>Active
                                                @else
                                                    <i class="fas fa-pause-circle mr-1"></i>Inactive
                                                @endif
                                            </span>
                                            @if($user->email_verified_at)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-shield-check mr-1"></i>Verified
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-clock mr-1"></i>Unverified
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <div class="space-y-1">
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-link text-blue-500 mr-1"></i>
                                            <span class="text-sm font-medium text-gray-900">{{ $user->urls_count ?? 0 }}</span>
                                            <span class="text-xs text-gray-500 ml-1">URLs</span>
                                        </div>
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-mouse-pointer text-green-500 mr-1"></i>
                                            <span class="text-sm font-medium text-gray-900">{{ number_format($user->total_clicks ?: 0) }}</span>
                                            <span class="text-xs text-gray-500 ml-1">clicks</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $user->created_at->format('M j, Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->created_at->diffForHumans() }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.users.show', $user) }}"
                                           class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 transition-all duration-200 transform hover:scale-110"
                                           title="View User Details">
                                            <i class="fas fa-eye text-sm"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="{{ route('admin.users.edit', $user) }}"
                                           class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-700 transition-all duration-200 transform hover:scale-110"
                                           title="Edit User">
                                            <i class="fas fa-edit text-sm"></i>
                                        </a>

                                        @if($user->role !== 'admin' && $user->id !== auth()->id())
                                        <!-- Impersonate Button -->
                                        <form action="{{ route('admin.users.impersonate', $user) }}" method="POST" class="inline-block">
                                            @csrf
                                            <button type="submit"
                                                    class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-600 hover:bg-purple-200 hover:text-purple-700 transition-all duration-200 transform hover:scale-110"
                                                    title="Impersonate User"
                                                    onclick="return confirm('Are you sure you want to impersonate this user?')">
                                                <i class="fas fa-user-secret text-sm"></i>
                                            </button>
                                        </form>
                                        @endif

                                        @if($user->id !== auth()->id())
                                        <!-- Delete Button -->
                                        <button type="button"
                                                class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 transition-all duration-200 transform hover:scale-110"
                                                onclick="confirmDeleteUser({{ $user->id }}, '{{ $user->name }}')"
                                                title="Delete User">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="bg-white px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        @if($users->onFirstPage())
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-white cursor-not-allowed">
                                Previous
                            </span>
                        @else
                            <a href="{{ $users->previousPageUrl() }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        @endif

                        @if($users->hasMorePages())
                            <a href="{{ $users->nextPageUrl() }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        @else
                            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-white cursor-not-allowed">
                                Next
                            </span>
                        @endif
                    </div>

                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ $users->firstItem() }}</span> to <span class="font-medium">{{ $users->lastItem() }}</span> of <span class="font-medium">{{ $users->total() }}</span> results
                            </p>
                        </div>
                        <div>
                            {{ $users->links() }}
                        </div>
                    </div>
                </div>
            @else
                <div class="text-center py-12">
                    <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                        <i class="fas fa-users text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                    <p class="text-gray-500 mb-6">
                        @if(request()->hasAny(['search', 'role', 'status']))
                            Try adjusting your filters or <a href="{{ route('admin.users.index') }}" class="text-blue-600 hover:text-blue-500">clear all filters</a>.
                        @else
                            No users have been created yet.
                        @endif
                    </p>
                    @if(!request()->hasAny(['search', 'role', 'status']))
                    <a href="{{ route('admin.users.create') }}" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>Create First User
                    </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...'); // Debug log
    initializeFilters();
    initializeCheckboxes();
    initializeBulkActions();

    // Add click outside listener to close dropdowns
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('bulkDropdown');
        const button = document.getElementById('bulkActionsBtn');

        if (dropdown && button && !dropdown.classList.contains('hidden')) {
            // Check if click is outside the dropdown and button
            if (!dropdown.contains(event.target) && !button.contains(event.target)) {
                closeAllDropdowns();
            }
        }
    });

    // Test dropdown elements
    const dropdown = document.getElementById('bulkDropdown');
    const button = document.getElementById('bulkActionsBtn');
    console.log('Dropdown element:', dropdown); // Debug log
    console.log('Button element:', button); // Debug log
});

// Auto-submit form on filter changes
function initializeFilters() {
    const form = document.getElementById('filterForm');
    const autoSubmitElements = ['role', 'status', 'sort_by', 'sort_order', 'per_page'];

    autoSubmitElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', () => form.submit());
        }
    });
}

// Initialize checkbox functionality
function initializeCheckboxes() {
    const selectAllBtn = document.getElementById('selectAll');
    const selectAllHeaderBtn = document.getElementById('selectAllHeader');

    if (selectAllBtn) {
        selectAllBtn.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = this.checked);
            updateSelectedCount();
        });
    }

    if (selectAllHeaderBtn) {
        selectAllHeaderBtn.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = this.checked);
            if (selectAllBtn) selectAllBtn.checked = this.checked;
            updateSelectedCount();
        });
    }

    // Add event listeners to individual checkboxes
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
}

// Initialize bulk actions
function initializeBulkActions() {
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('bulkDropdown');
        const button = document.getElementById('bulkActionsBtn');

        if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.classList.add('hidden');
            document.getElementById('bulkChevron').classList.remove('rotate-180');
        }
    });
}

// Toggle bulk actions dropdown
function toggleBulkDropdown(event) {
    if (event) {
        event.stopPropagation();
    }

    const dropdown = document.getElementById('bulkDropdown');
    const chevron = document.getElementById('bulkChevron');
    const button = document.getElementById('bulkActionsBtn');

    console.log('Toggle dropdown called'); // Debug log

    if (dropdown && chevron) {
        const isHidden = dropdown.classList.contains('hidden');

        if (isHidden) {
            // Close any other open dropdowns first
            closeAllDropdowns();

            // Show dropdown
            dropdown.classList.remove('hidden');
            dropdown.style.display = 'block';
            dropdown.style.opacity = '1';
            dropdown.style.transform = 'scale(1)';
            chevron.classList.add('rotate-180');
            if (button) button.setAttribute('aria-expanded', 'true');
            console.log('Dropdown opened'); // Debug log
        } else {
            // Hide dropdown
            dropdown.classList.add('hidden');
            dropdown.style.display = 'none';
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'scale(0.95)';
            chevron.classList.remove('rotate-180');
            if (button) button.setAttribute('aria-expanded', 'false');
            console.log('Dropdown closed'); // Debug log
        }
    } else {
        console.error('Dropdown elements not found'); // Debug log
    }
}

// Close all dropdowns
function closeAllDropdowns() {
    const dropdown = document.getElementById('bulkDropdown');
    const chevron = document.getElementById('bulkChevron');
    const button = document.getElementById('bulkActionsBtn');

    if (dropdown && !dropdown.classList.contains('hidden')) {
        dropdown.classList.add('hidden');
        dropdown.style.display = 'none';
        dropdown.style.opacity = '0';
        dropdown.style.transform = 'scale(0.95)';
        if (chevron) chevron.classList.remove('rotate-180');
        if (button) button.setAttribute('aria-expanded', 'false');
    }
}

// Update selected count display
function updateSelectedCount() {
    const selectedUsers = getSelectedUsers();
    const countElement = document.getElementById('selectedCount');

    if (selectedUsers.length > 0) {
        countElement.textContent = selectedUsers.length;
        countElement.classList.remove('hidden');
    } else {
        countElement.classList.add('hidden');
    }
}

// Clear search
function clearSearch() {
    document.getElementById('search').value = '';
    document.getElementById('filterForm').submit();
}

// Get selected user IDs
function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Bulk actions
function bulkAction(action) {
    const selectedUsers = getSelectedUsers();

    if (selectedUsers.length === 0) {
        showNotification('Please select at least one user.', 'warning');
        return;
    }

    // Close dropdown
    toggleBulkDropdown();

    // Get action details
    const actionDetails = {
        'activate': { text: 'activate', color: 'green' },
        'deactivate': { text: 'deactivate', color: 'yellow' },
        'verify': { text: 'verify email for', color: 'blue' },
        'unverify': { text: 'unverify email for', color: 'orange' },
        'delete': { text: 'delete', color: 'red' }
    };

    const detail = actionDetails[action];
    const actionText = detail ? detail.text : action;

    if (!confirmAction(`Are you sure you want to ${actionText} ${selectedUsers.length} user(s)?`, detail.color)) {
        return;
    }

    // Show loading state
    showNotification(`Processing ${actionText} for ${selectedUsers.length} user(s)...`, 'info');

    fetch('{{ route("admin.users.bulk-action") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: action,
            user_ids: selectedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Confirm delete single user with better UX
function confirmDeleteUser(userId, userName) {
    if (!confirmAction(`Are you sure you want to delete "${userName}"? This action cannot be undone.`, 'red')) {
        return;
    }

    showNotification(`Deleting user "${userName}"...`, 'info');

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/users/${userId}`;

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'DELETE';

    form.appendChild(csrfToken);
    form.appendChild(methodField);
    document.body.appendChild(form);
    form.submit();
}

// Enhanced confirmation dialog
function confirmAction(message, color = 'blue') {
    return confirm(message);
}

// Show notification (you can enhance this with a toast library)
function showNotification(message, type = 'info') {
    // For now, using alert - you can replace with a toast notification
    if (type === 'error') {
        alert('❌ ' + message);
    } else if (type === 'success') {
        alert('✅ ' + message);
    } else if (type === 'warning') {
        alert('⚠️ ' + message);
    } else {
        alert('ℹ️ ' + message);
    }
}
</script>

<style>
/* Enhanced action button animations */
.action-btn {
    transition: all 0.2s ease-in-out;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Action button spacing */
.action-btn + .action-btn {
    margin-left: 0.5rem;
}

/* Dropdown animation and styling */
#bulkDropdown {
    transition: all 0.2s ease-in-out;
    transform-origin: top right;
    min-width: 200px;
    z-index: 1000;
}

#bulkDropdown.hidden {
    transform: scale(0.95);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

#bulkDropdown:not(.hidden) {
    transform: scale(1);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Ensure dropdown appears above other elements */
.relative {
    position: relative;
}

/* Dropdown button hover effect */
#bulkActionsBtn:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

/* Chevron rotation */
#bulkChevron {
    transition: transform 0.2s ease-in-out;
}

/* Selected count badge animation */
#selectedCount {
    transition: all 0.2s ease-in-out;
}

/* Table row hover effect */
tbody tr:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Checkbox styling */
.user-checkbox:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Badge hover effects */
.badge {
    transition: all 0.2s ease-in-out;
}

.badge:hover {
    transform: scale(1.05);
}
</style>
@endpush
