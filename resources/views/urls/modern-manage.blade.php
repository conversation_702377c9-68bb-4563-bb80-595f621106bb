@extends('layouts.modern')

@section('title', 'My Links - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Links</h1>
                <p class="text-gray-600 mt-1">Manage and track all your shortened URLs</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('urls.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Create New Link
                </a>
                @if(Auth::user()->subscription?->bulk_operations)
                    <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">
                        <i class="fas fa-layer-group mr-2"></i>Bulk Operations
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" action="{{ route('urls.manage') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <input type="text" 
                           name="search" 
                           placeholder="Search links..." 
                           class="input-field"
                           value="{{ request('search') }}">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <select name="status" class="input-field">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                    </select>
                </div>
                
                <!-- Date Filter -->
                <div>
                    <select name="date_range" class="input-field">
                        <option value="">All Time</option>
                        <option value="today" {{ request('date_range') === 'today' ? 'selected' : '' }}>Today</option>
                        <option value="week" {{ request('date_range') === 'week' ? 'selected' : '' }}>This Week</option>
                        <option value="month" {{ request('date_range') === 'month' ? 'selected' : '' }}>This Month</option>
                    </select>
                </div>
                
                <!-- Actions -->
                <div class="flex space-x-2">
                    <button type="submit" class="btn-primary flex-1">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    <a href="{{ route('urls.manage') }}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-blue-600">{{ $stats['total'] }}</div>
                <div class="text-sm text-gray-600">Total Links</div>
            </div>
        </div>
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-green-600">{{ $stats['active'] }}</div>
                <div class="text-sm text-gray-600">Active</div>
            </div>
        </div>
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ number_format($stats['total_clicks']) }}</div>
                <div class="text-sm text-gray-600">Total Clicks</div>
            </div>
        </div>
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-purple-600">{{ number_format($stats['clicks_today']) }}</div>
                <div class="text-sm text-gray-600">Clicks Today</div>
            </div>
        </div>
    </div>

    <!-- URLs Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Your Links</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">{{ $urls->total() }} total</span>
                </div>
            </div>
        </div>
        
        @if($urls->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Link Details
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($urls as $url)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        @if($url->qr_code_path)
                                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-qrcode text-gray-600"></i>
                                            </div>
                                        @else
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-link text-blue-600"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-900 truncate">
                                            {{ $url->title ?: 'Untitled Link' }}
                                        </div>
                                        <div class="text-sm text-gray-500 truncate">
                                            {{ $url->original_url }}
                                        </div>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                                {{ $url->short_url }}
                                            </span>
                                            <button onclick="copyToClipboard('{{ $url->short_url }}')" 
                                                    class="ml-2 text-gray-400 hover:text-gray-600">
                                                <i class="fas fa-copy text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-mouse-pointer text-gray-400 mr-1"></i>
                                        <span class="font-medium">{{ number_format($url->click_count) }}</span>
                                        <span class="text-gray-500 ml-1">clicks</span>
                                    </div>
                                    @if($url->analytics->count() > 0)
                                        <div class="text-xs text-gray-500 mt-1">
                                            Last click: {{ $url->analytics->first()->clicked_at->diffForHumans() }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    @if($url->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                    
                                    @if($url->expires_at)
                                        @if($url->expires_at->isPast())
                                            <span class="badge badge-danger">Expired</span>
                                        @else
                                            <span class="badge badge-warning">
                                                Expires {{ $url->expires_at->diffForHumans() }}
                                            </span>
                                        @endif
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <div>{{ $url->created_at->format('M j, Y') }}</div>
                                <div class="text-xs">{{ $url->created_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <!-- View Analytics -->
                                    <a href="{{ route('urls.show', $url) }}" 
                                       class="text-blue-600 hover:text-blue-700" 
                                       title="View Analytics">
                                        <i class="fas fa-chart-bar"></i>
                                    </a>
                                    
                                    <!-- Visit Link -->
                                    <a href="{{ $url->short_url }}" 
                                       target="_blank" 
                                       class="text-green-600 hover:text-green-700" 
                                       title="Visit Link">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    
                                    <!-- QR Code -->
                                    @if($url->qr_code_path)
                                        <a href="{{ route('qr.show', $url->short_code) }}" 
                                           target="_blank" 
                                           class="text-purple-600 hover:text-purple-700" 
                                           title="View QR Code">
                                            <i class="fas fa-qrcode"></i>
                                        </a>
                                    @endif
                                    
                                    <!-- Edit -->
                                    <a href="{{ route('urls.edit', $url) }}" 
                                       class="text-yellow-600 hover:text-yellow-700" 
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <!-- Delete -->
                                    <form method="POST" 
                                          action="{{ route('urls.destroy', $url) }}" 
                                          class="inline"
                                          onsubmit="return confirm('Are you sure you want to delete this link?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="text-red-600 hover:text-red-700" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $urls->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-link text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No links found</h3>
                <p class="text-gray-600 mb-6">
                    @if(request()->hasAny(['search', 'status', 'date_range']))
                        No links match your current filters. Try adjusting your search criteria.
                    @else
                        You haven't created any links yet. Create your first short link to get started.
                    @endif
                </p>
                <a href="{{ route('urls.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Create Your First Link
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-xs"></i>';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-600');
        }, 2000);
    });
}
</script>
@endpush
