@extends('layouts.modern')

@section('title', 'URL Analytics - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-chart-bar mr-3 text-blue-600"></i>
                    URL Analytics
                </h1>
                <p class="text-gray-600 mt-1">{{ $url->title ?: 'Untitled URL' }}</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
                <a href="{{ route('dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <a href="{{ route('analytics.show', $url) }}" class="btn-outline">
                    <i class="fas fa-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="{{ route('urls.edit', $url) }}" class="btn-primary">
                    <i class="fas fa-edit mr-2"></i>Edit URL
                </a>
            </div>
        </div>
    </div>

    <!-- URL Info Card -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">URL Information</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Original URL -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Original URL</label>
                    <div class="bg-gray-50 rounded-lg p-4 border">
                        <a href="{{ $url->original_url }}" target="_blank"
                           class="text-blue-600 hover:text-blue-700 break-all text-sm">
                            {{ $url->original_url }}
                            <i class="fas fa-external-link-alt ml-1"></i>
                        </a>
                    </div>
                </div>

                <!-- Short URL -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Short URL</label>
                    <div class="flex">
                        <input type="text"
                               class="input-field rounded-r-none flex-1"
                               value="{{ $url->short_url }}"
                               readonly
                               id="shortUrlInput">
                        <button onclick="copyToClipboard('{{ $url->short_url }}')"
                                class="btn-secondary rounded-l-none border-l-0"
                                id="copyButton">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <!-- QR Code Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="btn-outline flex items-center">
                                <i class="fas fa-qrcode mr-2"></i>QR Code
                                <i class="fas fa-chevron-down ml-2"></i>
                            </button>
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <div class="py-1">
                                    <a href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}"
                                       target="_blank"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-eye mr-2"></i>View QR Code
                                    </a>
                                    <a href="{{ route('qr.download', $url->custom_alias ?: $url->short_code) }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-download mr-2"></i>Download QR Code
                                    </a>
                                    <hr class="my-1">
                                    <a href="{{ route('qr.customize', $url) }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-palette mr-2"></i>Customize QR Code
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Share Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="btn-outline flex items-center">
                                <i class="fas fa-share-alt mr-2"></i>Share
                                <i class="fas fa-chevron-down ml-2"></i>
                            </button>
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <div class="py-1">
                                    <a href="#" onclick="shareOnPlatform('twitter')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fab fa-twitter mr-2 text-blue-400"></i>Twitter
                                    </a>
                                    <a href="#" onclick="shareOnPlatform('facebook')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fab fa-facebook mr-2 text-blue-600"></i>Facebook
                                    </a>
                                    <a href="#" onclick="shareOnPlatform('linkedin')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fab fa-linkedin mr-2 text-blue-700"></i>LinkedIn
                                    </a>
                                    <a href="#" onclick="shareOnPlatform('whatsapp')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fab fa-whatsapp mr-2 text-green-500"></i>WhatsApp
                                    </a>
                                    <a href="#" onclick="shareOnPlatform('telegram')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fab fa-telegram mr-2 text-blue-500"></i>Telegram
                                    </a>
                                    <hr class="my-1">
                                    <a href="#" onclick="shareOnPlatform('email')"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-envelope mr-2 text-gray-600"></i>Email
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- URL Metadata -->
            <div class="border-t border-gray-200 pt-6 mt-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <p class="text-sm text-gray-900">{{ $url->created_at->format('M d, Y H:i') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <div class="flex flex-wrap gap-1">
                            @if($url->isExpired())
                                <span class="badge badge-warning">Expired</span>
                            @else
                                <span class="badge badge-success">Active</span>
                            @endif

                            @if($url->isPasswordProtected())
                                <span class="badge badge-info">Protected</span>
                            @endif
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Expires</label>
                        <p class="text-sm text-gray-900">{{ $url->expires_at ? $url->expires_at->format('M d, Y H:i') : 'Never' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Total Clicks</label>
                        <p class="text-lg font-bold text-blue-600">{{ number_format($url->click_count) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Clicks -->
        <div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-mouse-pointer text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">{{ number_format($analytics['total_clicks']) }}</div>
                <div class="text-blue-100 text-sm">Total Clicks</div>
            </div>
        </div>

        <!-- Unique Visitors -->
        <div class="card bg-gradient-to-r from-green-500 to-green-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">{{ number_format($analytics['unique_visitors']) }}</div>
                <div class="text-green-100 text-sm">Unique Visitors</div>
            </div>
        </div>

        <!-- Countries -->
        <div class="card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-globe text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">{{ count($analytics['countries']) }}</div>
                <div class="text-purple-100 text-sm">Countries</div>
            </div>
        </div>

        <!-- Device Types -->
        <div class="card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-mobile-alt text-xl"></i>
                </div>
                <div class="text-2xl font-bold mb-1">{{ count($analytics['devices']) }}</div>
                <div class="text-orange-100 text-sm">Device Types</div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Daily Clicks Chart -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-line mr-2 text-blue-600"></i>
                        Daily Clicks (Last 30 Days)
                    </h3>
                </div>
                <div class="card-body">
                    <div class="h-80">
                        <canvas id="dailyClicksChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Types Chart -->
        <div class="lg:col-span-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-mobile-alt mr-2 text-green-600"></i>
                        Device Types
                    </h3>
                </div>
                <div class="card-body">
                    <div class="h-80">
                        <canvas id="deviceChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Countries -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-globe mr-2 text-blue-600"></i>
                    Top Countries
                </h3>
            </div>
            <div class="card-body">
                @if(count($analytics['countries']) > 0)
                    <div class="space-y-4">
                        @foreach($analytics['countries'] as $country)
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-900">{{ $country['country'] }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="badge badge-primary">{{ $country['count'] }}</span>
                                    <span class="text-xs text-gray-500">({{ $country['percentage'] }}%)</span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $country['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-globe text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No country data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Browsers -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-browser mr-2 text-green-600"></i>
                    Top Browsers
                </h3>
            </div>
            <div class="card-body">
                @if(count($analytics['browsers']) > 0)
                    <div class="space-y-4">
                        @foreach($analytics['browsers'] as $browser)
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-900">{{ $browser['browser'] }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="badge badge-success">{{ $browser['count'] }}</span>
                                    <span class="text-xs text-gray-500">({{ $browser['percentage'] }}%)</span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $browser['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-browser text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No browser data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Platforms -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-desktop mr-2 text-purple-600"></i>
                    Top Platforms
                </h3>
            </div>
            <div class="card-body">
                @if(count($analytics['platforms']) > 0)
                    <div class="space-y-4">
                        @foreach($analytics['platforms'] as $platform)
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-900">{{ $platform['platform'] }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="badge badge-purple">{{ $platform['count'] }}</span>
                                    <span class="text-xs text-gray-500">({{ $platform['percentage'] }}%)</span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $platform['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-desktop text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No platform data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Referrers -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-external-link-alt mr-2 text-orange-600"></i>
                    Top Referrers
                </h3>
            </div>
            <div class="card-body">
                @if(count($analytics['referrers']) > 0)
                    <div class="space-y-4">
                        @foreach($analytics['referrers'] as $referrer)
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-900">{{ $referrer['referrer'] }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="badge badge-warning">{{ $referrer['count'] }}</span>
                                    <span class="text-xs text-gray-500">({{ $referrer['percentage'] }}%)</span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $referrer['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-external-link-alt text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No referrer data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Copy to clipboard functionality
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const button = document.getElementById('copyButton');
        const originalContent = button.innerHTML;

        // Show success feedback
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        button.classList.remove('btn-secondary');
        button.classList.add('btn-success');

        // Reset after 2 seconds
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('btn-success');
            button.classList.add('btn-secondary');
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        const button = document.getElementById('copyButton');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        button.classList.remove('btn-secondary');
        button.classList.add('btn-success');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('btn-success');
            button.classList.add('btn-secondary');
        }, 2000);
    });
}

// Social sharing functionality
function shareOnPlatform(platform) {
    const url = encodeURIComponent('{{ $url->short_url }}');
    const title = encodeURIComponent('{{ $url->title ?: "Check out this link" }}');
    const text = encodeURIComponent('{{ $url->title ?: "Check out this link" }} {{ $url->short_url }}');

    let shareUrl = '';

    switch(platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}&hashtags=minilinkat`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${text}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
            break;
        case 'email':
            shareUrl = `mailto:?subject=${title}&body=Check out this link: {{ $url->short_url }}`;
            break;
    }

    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

// Daily Clicks Chart
const dailyClicksCtx = document.getElementById('dailyClicksChart').getContext('2d');
const dailyClicksChart = new Chart(dailyClicksCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($analytics['daily_clicks'], 'date')) !!},
        datasets: [{
            label: 'Clicks',
            data: {!! json_encode(array_column($analytics['daily_clicks'], 'clicks')) !!},
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#3B82F6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6B7280'
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#F3F4F6'
                },
                ticks: {
                    stepSize: 1,
                    color: '#6B7280'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: '#1F2937',
                titleColor: '#F9FAFB',
                bodyColor: '#F9FAFB',
                borderColor: '#374151',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false
            }
        }
    }
});

// Device Types Chart
@if(count($analytics['devices']) > 0)
const deviceCtx = document.getElementById('deviceChart').getContext('2d');
const deviceChart = new Chart(deviceCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode(array_column($analytics['devices'], 'device')) !!},
        datasets: [{
            data: {!! json_encode(array_column($analytics['devices'], 'count')) !!},
            backgroundColor: [
                '#3B82F6',
                '#10B981',
                '#F59E0B',
                '#EF4444',
                '#8B5CF6',
                '#06B6D4',
                '#F97316'
            ],
            borderWidth: 0,
            hoverBorderWidth: 2,
            hoverBorderColor: '#ffffff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '60%',
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    color: '#374151',
                    font: {
                        size: 12
                    }
                }
            },
            tooltip: {
                backgroundColor: '#1F2937',
                titleColor: '#F9FAFB',
                bodyColor: '#F9FAFB',
                borderColor: '#374151',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true
            }
        }
    }
});
@endif
</script>
@endpush

@push('styles')
<style>
.badge-purple {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800;
}
</style>
@endpush
