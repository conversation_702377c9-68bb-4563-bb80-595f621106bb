@extends('layouts.dashboard-template')

@section('page_title', 'User Details - ' . $user->name . ' - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-user mr-3 text-blue-600"></i>User Details: {{ $user->name }}
@endsection
@section('page_description', 'View detailed information about this user')

@section('page_actions')
    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Users
    </a>
    <a href="{{ route('admin.users.edit', $user) }}" class="btn-outline">
        <i class="fas fa-edit mr-2"></i>Edit User
    </a>
    @if($user->role !== 'admin' && $user->id !== auth()->id())
        <form action="{{ route('admin.users.impersonate', $user) }}" method="POST" class="inline-block">
            @csrf
            <button type="submit" class="btn-primary" onclick="return confirm('Are you sure you want to impersonate this user?')">
                <i class="fas fa-user-secret mr-2"></i>Impersonate
            </button>
        </form>
    @endif
@endsection

@section('main_content')
    <!-- User Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- User Profile -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Profile Information</h3>
                </div>
                <div class="card-body">
                    <div class="flex items-start space-x-6">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-2xl">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        </div>
                        
                        <!-- User Details -->
                        <div class="flex-1">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Full Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $user->name }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email Address</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $user->email }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Role</label>
                                    <p class="mt-1">
                                        @if($user->role === 'admin')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                👑 Admin
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                👤 User
                                            </span>
                                        @endif
                                    </p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Account Status</label>
                                    <p class="mt-1">
                                        @if($user->is_active)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ✅ Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                ❌ Inactive
                                            </span>
                                        @endif
                                    </p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email Verification</label>
                                    <p class="mt-1">
                                        @if($user->email_verified_at)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ✔️ Verified
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                ⏳ Unverified
                                            </span>
                                        @endif
                                    </p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Member Since</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M d, Y') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="space-y-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Quick Stats</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total URLs</span>
                            <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['total_urls']) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total Clicks</span>
                            <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['total_clicks']) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Active URLs</span>
                            <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['active_urls']) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">This Month URLs</span>
                            <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['urls_this_month']) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">This Month Clicks</span>
                            <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['clicks_this_month']) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent URLs -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Recent URLs</h3>
                <span class="text-sm text-gray-500">Last 10 URLs created</span>
            </div>
        </div>
        <div class="card-body">
            @if($user->urls->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Short Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($user->urls as $url)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate max-w-xs" title="{{ $url->original_url }}">
                                        {{ $url->title ?: Str::limit($url->original_url, 50) }}
                                    </div>
                                    <div class="text-xs text-gray-500 truncate max-w-xs">
                                        {{ $url->original_url }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-mono text-blue-600">
                                        <a href="{{ route('urls.show', $url) }}" target="_blank" class="hover:underline">
                                            {{ $url->short_code }}
                                        </a>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ number_format($url->click_count) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $url->created_at->format('M d, Y') }}</div>
                                    <div class="text-xs text-gray-500">{{ $url->created_at->format('H:i') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($url->is_active)
                                        @if($url->expires_at && $url->expires_at < now())
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Expired
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        @endif
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Inactive
                                        </span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if($stats['total_urls'] > 10)
                    <div class="mt-4 text-center">
                        <a href="{{ route('admin.urls.index', ['user_id' => $user->id]) }}" class="btn-outline">
                            <i class="fas fa-external-link-alt mr-2"></i>View All URLs ({{ number_format($stats['total_urls']) }})
                        </a>
                    </div>
                @endif
            @else
                <div class="text-center py-8">
                    <i class="fas fa-link text-gray-400 text-3xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No URLs Created</h4>
                    <p class="text-gray-600">This user hasn't created any URLs yet.</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Account Actions -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Account Actions</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ route('admin.users.edit', $user) }}" class="btn-outline text-center">
                    <i class="fas fa-edit mr-2"></i>Edit User
                </a>
                
                @if(!$user->email_verified_at)
                    <button onclick="verifyEmail({{ $user->id }})" class="btn-outline text-center">
                        <i class="fas fa-shield-check mr-2"></i>Verify Email
                    </button>
                @endif
                
                @if($user->role !== 'admin' && $user->id !== auth()->id())
                    <form action="{{ route('admin.users.impersonate', $user) }}" method="POST" class="w-full">
                        @csrf
                        <button type="submit" class="btn-outline text-center w-full" onclick="return confirm('Are you sure you want to impersonate this user?')">
                            <i class="fas fa-user-secret mr-2"></i>Impersonate
                        </button>
                    </form>
                @endif
                
                @if($user->id !== auth()->id())
                    <button onclick="confirmDelete({{ $user->id }})" class="btn-outline text-center text-red-600 border-red-300 hover:bg-red-50">
                        <i class="fas fa-trash mr-2"></i>Delete User
                    </button>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function verifyEmail(userId) {
    if (confirm('Are you sure you want to mark this email as verified?')) {
        // Simple implementation - in real app you'd have a proper route
        alert('Email verification feature would be implemented here.');
    }
}

function confirmDelete(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = document.querySelector('meta[name="csrf-token"]').content;

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
