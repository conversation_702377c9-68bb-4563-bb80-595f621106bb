<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Url;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): View
    {
        $query = User::query();

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply role filter
        if ($request->filled('role')) {
            $query->where('role', $request->get('role'));
        }

        // Apply status filter
        if ($request->filled('status')) {
            switch ($request->get('status')) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'verified':
                    $query->whereNotNull('email_verified_at');
                    break;
                case 'unverified':
                    $query->whereNull('email_verified_at');
                    break;
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'name', 'email', 'role'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Handle pagination
        $perPage = $request->get('per_page', 25);
        $allowedPerPage = [10, 25, 50, 100];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 25;
        }

        $users = $query->withCount(['urls', 'urls as total_clicks' => function($query) {
            $query->selectRaw('sum(click_count)');
        }])->paginate($perPage)->withQueryString();

        // Get filter counts
        $filterCounts = [
            'all' => User::count(),
            'active' => User::where('is_active', true)->count(),
            'inactive' => User::where('is_active', false)->count(),
            'verified' => User::whereNotNull('email_verified_at')->count(),
            'unverified' => User::whereNull('email_verified_at')->count(),
            'admin' => User::where('role', 'admin')->count(),
            'user' => User::where('role', 'user')->count(),
        ];

        return view('admin.users.index', compact('users', 'filterCounts'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): View
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Password::defaults()],
            'role' => 'required|in:user,admin',
            'is_active' => 'boolean',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'is_active' => $request->boolean('is_active', true),
            'email_verified_at' => $request->boolean('email_verified') ? now() : null,
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user): View
    {
        $user->load(['urls' => function($query) {
            $query->withCount('analytics')->orderBy('created_at', 'desc')->limit(10);
        }]);

        $stats = [
            'total_urls' => $user->urls()->count(),
            'total_clicks' => $user->urls()->sum('click_count'),
            'active_urls' => $user->urls()->where('is_active', true)->count(),
            'expired_urls' => $user->urls()->where('expires_at', '<', now())->count(),
            'urls_this_month' => $user->urls()->where('created_at', '>=', now()->subMonth())->count(),
            'clicks_this_month' => $user->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->where('analytics.clicked_at', '>=', now()->subMonth())
                ->count(),
        ];

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user): View
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'role' => 'required|in:user,admin',
            'is_active' => 'boolean',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'is_active' => $request->boolean('is_active'),
        ];

        // Handle email verification
        if ($request->boolean('email_verified') && !$user->email_verified_at) {
            $updateData['email_verified_at'] = now();
        } elseif (!$request->boolean('email_verified') && $user->email_verified_at) {
            $updateData['email_verified_at'] = null;
        }

        // Handle password update
        if ($request->filled('password')) {
            $request->validate([
                'password' => ['confirmed', Password::defaults()],
            ]);
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user): RedirectResponse
    {
        // Prevent deleting the last admin
        if ($user->role === 'admin' && User::where('role', 'admin')->count() <= 1) {
            return redirect()->back()
                ->with('error', 'Cannot delete the last admin user.');
        }

        // Prevent self-deletion
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot delete your own account.');
        }

        $userName = $user->name;
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', "User '{$userName}' has been deleted successfully.");
    }

    /**
     * Bulk operations on users.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,verify,unverify,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $request->get('user_ids');
        $action = $request->get('action');
        $currentUserId = auth()->id();

        // Prevent actions on current user
        if (in_array($currentUserId, $userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot perform bulk actions on your own account.'
            ], 403);
        }

        $affectedCount = 0;

        switch ($action) {
            case 'activate':
                $affectedCount = User::whereIn('id', $userIds)->update(['is_active' => true]);
                break;
                
            case 'deactivate':
                $affectedCount = User::whereIn('id', $userIds)->update(['is_active' => false]);
                break;
                
            case 'verify':
                $affectedCount = User::whereIn('id', $userIds)
                    ->whereNull('email_verified_at')
                    ->update(['email_verified_at' => now()]);
                break;
                
            case 'unverify':
                $affectedCount = User::whereIn('id', $userIds)
                    ->update(['email_verified_at' => null]);
                break;
                
            case 'delete':
                // Prevent deleting all admins
                $adminCount = User::where('role', 'admin')->count();
                $adminToDelete = User::whereIn('id', $userIds)->where('role', 'admin')->count();
                
                if ($adminCount - $adminToDelete < 1) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot delete all admin users. At least one admin must remain.'
                    ], 403);
                }
                
                $affectedCount = User::whereIn('id', $userIds)->count();
                User::whereIn('id', $userIds)->delete();
                break;
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully {$action}d {$affectedCount} user(s).",
            'affected_count' => $affectedCount,
        ]);
    }

    /**
     * Generate API key for user.
     */
    public function generateApiKey(User $user): RedirectResponse
    {
        $apiKey = $user->generateApiKey();

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'API key generated successfully!')
            ->with('new_api_key', $apiKey);
    }

    /**
     * Revoke API key for user.
     */
    public function revokeApiKey(User $user): RedirectResponse
    {
        $user->update(['api_key' => null]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'API key revoked successfully!');
    }

    /**
     * Impersonate user (for admin testing).
     */
    public function impersonate(User $user): RedirectResponse
    {
        if ($user->role === 'admin') {
            return redirect()->back()
                ->with('error', 'Cannot impersonate admin users.');
        }

        session(['impersonating' => auth()->id()]);
        auth()->login($user);

        return redirect()->route('dashboard')
            ->with('info', "You are now impersonating {$user->name}. Click 'Stop Impersonating' to return to your admin account.");
    }

    /**
     * Stop impersonating user.
     */
    public function stopImpersonating(): RedirectResponse
    {
        if (!session('impersonating')) {
            return redirect()->route('admin.dashboard');
        }

        $adminId = session('impersonating');
        session()->forget('impersonating');
        
        $admin = User::find($adminId);
        auth()->login($admin);

        return redirect()->route('admin.dashboard')
            ->with('success', 'Stopped impersonating user.');
    }
}
