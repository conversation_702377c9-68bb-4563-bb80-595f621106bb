<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ApiController extends Controller
{
    /**
     * Return a successful JSON response.
     */
    protected function successResponse($data = null, string $message = 'Success', int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'status_code' => $statusCode,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error JSON response.
     */
    protected function errorResponse(string $message = 'Error', int $statusCode = 400, array $errors = []): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'status_code' => $statusCode,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a paginated JSON response.
     */
    protected function paginatedResponse(LengthAwarePaginator $paginator, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $paginator->items(),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
                'has_more_pages' => $paginator->hasMorePages(),
            ],
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ],
        ]);
    }

    /**
     * Get the authenticated API user.
     */
    protected function getApiUser(Request $request)
    {
        return $request->get('api_user');
    }

    /**
     * Validate pagination parameters.
     */
    protected function validatePagination(Request $request): array
    {
        $request->validate([
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
        ]);

        return [
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 15),
        ];
    }

    /**
     * Transform URL model for API response.
     */
    protected function transformUrl($url): array
    {
        return [
            'id' => $url->id,
            'title' => $url->title,
            'description' => $url->description,
            'original_url' => $url->original_url,
            'short_url' => $url->short_url,
            'short_code' => $url->short_code,
            'custom_alias' => $url->custom_alias,
            'click_count' => $url->click_count,
            'is_active' => $url->is_active,
            'expires_at' => $url->expires_at?->toISOString(),
            'created_at' => $url->created_at->toISOString(),
            'updated_at' => $url->updated_at->toISOString(),
            'qr_code_url' => $url->qr_code_path ? asset('storage/' . $url->qr_code_path) : null,
        ];
    }

    /**
     * Transform analytics model for API response.
     */
    protected function transformAnalytics($analytics): array
    {
        return [
            'id' => $analytics->id,
            'url_id' => $analytics->url_id,
            'ip_address' => $analytics->ip_address,
            'user_agent' => $analytics->user_agent,
            'referrer' => $analytics->referrer,
            'country' => $analytics->country,
            'city' => $analytics->city,
            'device_type' => $analytics->device_type,
            'browser' => $analytics->browser,
            'platform' => $analytics->platform,
            'clicked_at' => $analytics->clicked_at->toISOString(),
        ];
    }

    /**
     * Transform user model for API response.
     */
    protected function transformUser($user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'role' => $user->role,
            'is_active' => $user->is_active,
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'created_at' => $user->created_at->toISOString(),
            'updated_at' => $user->updated_at->toISOString(),
        ];
    }

    /**
     * Get API rate limit information.
     */
    protected function getRateLimitInfo(Request $request): array
    {
        $key = 'api_rate_limit:' . $this->getApiUser($request)->id;
        
        return [
            'limit' => 1000, // requests per hour
            'remaining' => 1000 - (cache()->get($key . ':count', 0)),
            'reset_at' => cache()->get($key . ':reset', now()->addHour())->toISOString(),
        ];
    }

    /**
     * Add rate limit headers to response.
     */
    protected function addRateLimitHeaders(JsonResponse $response, Request $request): JsonResponse
    {
        $rateLimitInfo = $this->getRateLimitInfo($request);
        
        $response->headers->set('X-RateLimit-Limit', $rateLimitInfo['limit']);
        $response->headers->set('X-RateLimit-Remaining', $rateLimitInfo['remaining']);
        $response->headers->set('X-RateLimit-Reset', $rateLimitInfo['reset_at']);
        
        return $response;
    }

    /**
     * Handle API exceptions.
     */
    protected function handleException(\Exception $e): JsonResponse
    {
        if (config('app.debug')) {
            return $this->errorResponse(
                'Internal server error: ' . $e->getMessage(),
                500,
                [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
        }

        return $this->errorResponse('Internal server error', 500);
    }

    /**
     * Validate URL ownership.
     */
    protected function validateUrlOwnership($url, $user): bool
    {
        return $url->user_id === $user->id;
    }

    /**
     * Get API version from request.
     */
    protected function getApiVersion(Request $request): string
    {
        return $request->header('Accept-Version', 'v1');
    }

    /**
     * Check if feature is enabled.
     */
    protected function isFeatureEnabled(string $feature): bool
    {
        $features = [
            'url_shortening' => true,
            'analytics' => true,
            'qr_codes' => true,
            'custom_domains' => false, // Will be enabled in Phase 8
        ];

        return $features[$feature] ?? false;
    }

    /**
     * Get API documentation info.
     */
    public function info(): JsonResponse
    {
        return $this->successResponse([
            'api_name' => 'Minilink.at API',
            'version' => 'v1',
            'description' => 'RESTful API for URL shortening, analytics, and management',
            'base_url' => config('app.url') . '/api/v1',
            'documentation_url' => config('app.url') . '/api/docs',
            'authentication' => [
                'type' => 'API Key',
                'header' => 'Authorization: Bearer YOUR_API_KEY',
                'alternative_header' => 'X-API-Key: YOUR_API_KEY',
            ],
            'rate_limits' => [
                'requests_per_hour' => 1000,
                'burst_limit' => 60,
            ],
            'features' => [
                'url_shortening' => $this->isFeatureEnabled('url_shortening'),
                'analytics' => $this->isFeatureEnabled('analytics'),
                'qr_codes' => $this->isFeatureEnabled('qr_codes'),
                'custom_domains' => $this->isFeatureEnabled('custom_domains'),
            ],
            'endpoints' => [
                'urls' => '/api/v1/urls',
                'analytics' => '/api/v1/analytics',
                'user' => '/api/v1/user',
                'qr_codes' => '/api/v1/qr-codes',
            ],
        ], 'API information retrieved successfully');
    }

    /**
     * Health check endpoint.
     */
    public function health(): JsonResponse
    {
        try {
            // Check database connection
            \DB::connection()->getPdo();
            
            return $this->successResponse([
                'status' => 'healthy',
                'timestamp' => now()->toISOString(),
                'version' => 'v1',
                'database' => 'connected',
                'cache' => cache()->get('health_check') !== null ? 'working' : 'not_tested',
            ], 'API is healthy');
        } catch (\Exception $e) {
            return $this->errorResponse('API health check failed', 503, [
                'database' => 'disconnected',
                'error' => $e->getMessage(),
            ]);
        }
    }
}
