<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UrlController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\QrCodeController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\UserUrlController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\CustomDomainController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\BulkOperationsController;
use App\Http\Controllers\AdvancedAnalyticsController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\Admin\SeoController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\UrlController as AdminUrlController;
use App\Http\Controllers\Admin\SettingsController;

// Public routes
Route::get('/', function () {
    return view('homepage-clean');
})->name('home');
Route::get('/home', function () {
    return redirect('/dashboard');
})->name('home.redirect');
Route::get('/api/docs', function () {
    return view('api.documentation');
})->name('api.docs');

// Legal and Support Pages
Route::get('/help-center', function () {
    return view('help-center');
})->name('help-center');

Route::get('/api-documentation', function () {
    return view('api-documentation');
})->name('api-documentation');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('terms-of-service');
})->name('terms-of-service');

// PayPal webhook (no auth required)
Route::post('/webhooks/paypal', [SubscriptionController::class, 'paypalWebhook'])->name('webhooks.paypal');

// SEO routes (public)
Route::get('/robots.txt', function () {
    $seoService = app(\App\Services\SeoService::class);
    return response($seoService->generateRobotsTxt())
        ->header('Content-Type', 'text/plain');
})->name('robots.txt');

Route::get('/sitemap.xml', function () {
    $seoService = app(\App\Services\SeoService::class);
    $urls = $seoService->getSitemapUrls();

    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    foreach ($urls as $url) {
        $xml .= "  <url>\n";
        $xml .= "    <loc>{$url['url']}</loc>\n";
        $xml .= "    <lastmod>{$url['lastmod']}</lastmod>\n";
        $xml .= "    <changefreq>{$url['changefreq']}</changefreq>\n";
        $xml .= "    <priority>{$url['priority']}</priority>\n";
        $xml .= "  </url>\n";
    }

    $xml .= '</urlset>';

    return response($xml)->header('Content-Type', 'application/xml');
})->name('sitemap.xml');

// URL shortening routes
Route::post('/shorten', [UrlController::class, 'store'])->name('urls.store');
Route::post('/bulk-shorten', [UrlController::class, 'bulkStore'])->name('urls.bulk-store');

// QR Code routes (public)
Route::get('/{code}/qr', [QrCodeController::class, 'show'])->name('qr.show');
Route::get('/{code}/qr/download', [QrCodeController::class, 'download'])->name('qr.download');

// Authentication routes (must be before catch-all routes)
Auth::routes(['verify' => false]);

// Manual authentication routes (fallback)
Route::get('/login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [App\Http\Controllers\Auth\LoginController::class, 'login']);
Route::post('/logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');
Route::get('/register', [App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [App\Http\Controllers\Auth\RegisterController::class, 'register']);

// Protected routes (must be before catch-all routes)
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/analytics', [DashboardController::class, 'analytics'])->name('dashboard.analytics');

    // Enhanced URL Management
    Route::get('/dashboard/urls', [UserUrlController::class, 'index'])->name('dashboard.urls.index');
    Route::post('/dashboard/urls/bulk-action', [UserUrlController::class, 'bulkAction'])->name('dashboard.urls.bulk-action');
    Route::get('/dashboard/urls/stats', [UserUrlController::class, 'getStats'])->name('dashboard.urls.stats');
    Route::get('/dashboard/exports/{filename}', [UserUrlController::class, 'downloadExport'])->name('dashboard.urls.download-export');

    // Profile Management
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [ProfileController::class, 'editPassword'])->name('profile.password');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');
    Route::get('/profile/api-keys', [ProfileController::class, 'apiKeys'])->name('profile.api-keys');
    Route::post('/profile/api-keys/generate', [ProfileController::class, 'generateApiKey'])->name('profile.generate-api-key');
    Route::delete('/profile/api-keys', [ProfileController::class, 'revokeApiKey'])->name('profile.revoke-api-key');
    Route::get('/profile/settings', [ProfileController::class, 'settings'])->name('profile.settings');
    Route::put('/profile/settings', [ProfileController::class, 'updateSettings'])->name('profile.update-settings');
    Route::get('/profile/delete-account', [ProfileController::class, 'deleteAccount'])->name('profile.delete-account');
    Route::delete('/profile/delete-account', [ProfileController::class, 'destroyAccount'])->name('profile.destroy-account');

    // Custom Domain Management
    Route::resource('custom-domains', CustomDomainController::class);
    Route::post('/custom-domains/{customDomain}/verify', [CustomDomainController::class, 'verify'])->name('custom-domains.verify');
    Route::post('/custom-domains/{customDomain}/regenerate-token', [CustomDomainController::class, 'regenerateToken'])->name('custom-domains.regenerate-token');
    Route::post('/custom-domains/{customDomain}/check-ssl', [CustomDomainController::class, 'checkSsl'])->name('custom-domains.check-ssl');
    Route::post('/custom-domains/{customDomain}/test', [CustomDomainController::class, 'test'])->name('custom-domains.test');
    Route::get('/custom-domains/{customDomain}/stats', [CustomDomainController::class, 'stats'])->name('custom-domains.stats');

    // Subscription Management
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/compare', [SubscriptionController::class, 'compare'])->name('subscriptions.compare');
    Route::get('/subscriptions/usage', [SubscriptionController::class, 'usage'])->name('subscriptions.usage');
    Route::get('/subscriptions/billing', [SubscriptionController::class, 'billing'])->name('subscriptions.billing');
    Route::get('/subscriptions/{subscription}', [SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::post('/subscriptions/{subscription}/upgrade', [SubscriptionController::class, 'upgrade'])->name('subscriptions.upgrade');
    Route::get('/subscriptions/{subscription}/payment', [SubscriptionController::class, 'payment'])->name('subscriptions.payment');
    Route::post('/subscriptions/{subscription}/process-payment', [SubscriptionController::class, 'processPayment'])->name('subscriptions.process-payment');

    // PayPal routes
    Route::post('/subscriptions/{subscription}/paypal-success', [SubscriptionController::class, 'paypalSuccess'])->name('subscriptions.paypal-success');
    Route::get('/subscriptions/payment-success', function() {
        return redirect()->route('subscriptions.index')->with('success', 'Payment completed successfully!');
    })->name('subscriptions.payment-success');
    Route::get('/subscriptions/payment-cancelled', [SubscriptionController::class, 'paypalCancel'])->name('subscriptions.payment-cancelled');
    Route::post('/subscriptions/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');

    // Bulk Operations
    Route::get('/bulk-operations', [BulkOperationsController::class, 'index'])->name('bulk-operations.index');
    Route::get('/bulk-operations/create', [BulkOperationsController::class, 'create'])->name('bulk-operations.create');
    Route::post('/bulk-operations', [BulkOperationsController::class, 'store'])->name('bulk-operations.store');
    Route::get('/bulk-operations/results', [BulkOperationsController::class, 'results'])->name('bulk-operations.results');
    Route::get('/bulk-operations/edit', [BulkOperationsController::class, 'edit'])->name('bulk-operations.edit');
    Route::post('/bulk-operations/update', [BulkOperationsController::class, 'update'])->name('bulk-operations.update');
    Route::post('/bulk-operations/export', [BulkOperationsController::class, 'export'])->name('bulk-operations.export');
    Route::get('/bulk-operations/download-template', [BulkOperationsController::class, 'downloadTemplate'])->name('bulk-operations.download-template');
    Route::get('/bulk-operations/download/{filename}', [BulkOperationsController::class, 'download'])->name('bulk-operations.download');

    // Advanced Analytics
    Route::get('/analytics/advanced', [AdvancedAnalyticsController::class, 'index'])->name('analytics.advanced.index');
    Route::get('/analytics/advanced/url/{url}', [AdvancedAnalyticsController::class, 'url'])->name('analytics.advanced.url');
    Route::get('/analytics/advanced/compare', [AdvancedAnalyticsController::class, 'compare'])->name('analytics.advanced.compare');
    Route::get('/analytics/advanced/realtime', [AdvancedAnalyticsController::class, 'realtime'])->name('analytics.advanced.realtime');
    Route::get('/analytics/advanced/geographic', [AdvancedAnalyticsController::class, 'geographic'])->name('analytics.advanced.geographic');
    Route::get('/analytics/advanced/devices', [AdvancedAnalyticsController::class, 'devices'])->name('analytics.advanced.devices');
    Route::get('/analytics/advanced/sources', [AdvancedAnalyticsController::class, 'sources'])->name('analytics.advanced.sources');
    Route::get('/analytics/advanced/trends', [AdvancedAnalyticsController::class, 'trends'])->name('analytics.advanced.trends');
    Route::post('/analytics/advanced/export', [AdvancedAnalyticsController::class, 'export'])->name('analytics.advanced.export');

    // Team Management
    Route::resource('teams', TeamController::class);
    Route::post('/teams/{team}/invite', [TeamController::class, 'invite'])->name('teams.invite');
    Route::put('/teams/{team}/members/{member}', [TeamController::class, 'updateMember'])->name('teams.update-member');
    Route::delete('/teams/{team}/members/{member}', [TeamController::class, 'removeMember'])->name('teams.remove-member');
    Route::post('/teams/{team}/leave', [TeamController::class, 'leave'])->name('teams.leave');
    Route::post('/teams/{team}/transfer-ownership', [TeamController::class, 'transferOwnership'])->name('teams.transfer-ownership');
    Route::get('/teams/{team}/analytics', [TeamController::class, 'analytics'])->name('teams.analytics');
    Route::get('/teams/{team}/settings', [TeamController::class, 'settings'])->name('teams.settings');
    Route::put('/teams/{team}/settings', [TeamController::class, 'updateSettings'])->name('teams.update-settings');
    Route::get('/teams/{team}/usage', [TeamController::class, 'usage'])->name('teams.usage');

    // Admin Panel Routes (requires admin middleware)
    Route::prefix('admin')->name('admin.')->group(function () {
        // Admin Dashboard
        Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
        Route::get('/system-info', [AdminDashboardController::class, 'systemInfo'])->name('system-info');

        // User Management
        Route::get('/users', [AdminUserController::class, 'index'])->name('users.index');
        Route::get('/users/create', [AdminUserController::class, 'create'])->name('users.create');
        Route::post('/users', [AdminUserController::class, 'store'])->name('users.store');
        Route::get('/users/{user}', [AdminUserController::class, 'show'])->name('users.show');
        Route::get('/users/{user}/edit', [AdminUserController::class, 'edit'])->name('users.edit');
        Route::put('/users/{user}', [AdminUserController::class, 'update'])->name('users.update');
        Route::delete('/users/{user}', [AdminUserController::class, 'destroy'])->name('users.destroy');
        Route::post('/users/bulk-action', [AdminUserController::class, 'bulkAction'])->name('users.bulk-action');
        Route::post('/users/{user}/generate-api-key', [AdminUserController::class, 'generateApiKey'])->name('users.generate-api-key');
        Route::delete('/users/{user}/revoke-api-key', [AdminUserController::class, 'revokeApiKey'])->name('users.revoke-api-key');
        Route::post('/users/{user}/impersonate', [AdminUserController::class, 'impersonate'])->name('users.impersonate');
        Route::post('/stop-impersonating', [AdminUserController::class, 'stopImpersonating'])->name('users.stop-impersonating');

        // URL Management
        Route::get('/urls', [AdminUrlController::class, 'index'])->name('urls.index');
        Route::get('/urls/{url}', [AdminUrlController::class, 'show'])->name('urls.show');
        Route::get('/urls/{url}/edit', [AdminUrlController::class, 'edit'])->name('urls.edit');
        Route::put('/urls/{url}', [AdminUrlController::class, 'update'])->name('urls.update');
        Route::delete('/urls/{url}', [AdminUrlController::class, 'destroy'])->name('urls.destroy');
        Route::post('/urls/bulk-action', [AdminUrlController::class, 'bulkAction'])->name('urls.bulk-action');
        Route::get('/urls/stats', [AdminUrlController::class, 'getStats'])->name('urls.stats');
        Route::get('/exports/{filename}', [AdminUrlController::class, 'downloadExport'])->name('urls.download-export');
        Route::post('/urls/{url}/moderate', [AdminUrlController::class, 'moderate'])->name('urls.moderate');

        // System Settings
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::put('/settings', [SettingsController::class, 'update'])->name('settings.update');
        Route::get('/settings/cache', [SettingsController::class, 'cache'])->name('settings.cache');
        Route::post('/settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');
        Route::get('/settings/maintenance', [SettingsController::class, 'maintenance'])->name('settings.maintenance');
        Route::post('/settings/toggle-maintenance', [SettingsController::class, 'toggleMaintenance'])->name('settings.toggle-maintenance');
        Route::get('/settings/backup', [SettingsController::class, 'backup'])->name('settings.backup');
        Route::post('/settings/create-backup', [SettingsController::class, 'createBackup'])->name('settings.create-backup');

        // SEO Management
        Route::prefix('seo')->name('seo.')->group(function () {
            Route::get('/', [SeoController::class, 'index'])->name('index');
            Route::get('/meta', [SeoController::class, 'meta'])->name('meta');
            Route::get('/social', [SeoController::class, 'social'])->name('social');
            Route::get('/analytics', [SeoController::class, 'analytics'])->name('analytics');
            Route::get('/verification', [SeoController::class, 'verification'])->name('verification');
            Route::put('/update', [SeoController::class, 'update'])->name('update');
            Route::get('/robots-txt', [SeoController::class, 'robotsTxt'])->name('robots-txt');
            Route::get('/sitemap', [SeoController::class, 'sitemap'])->name('sitemap');
            Route::get('/test', [SeoController::class, 'test'])->name('test');
            Route::get('/audit', [SeoController::class, 'audit'])->name('audit');
            Route::delete('/reset', [SeoController::class, 'reset'])->name('reset');
            Route::get('/export', [SeoController::class, 'export'])->name('export');
            Route::post('/import', [SeoController::class, 'import'])->name('import');
        });
    });

    // URL management
    Route::get('/my-links', [UrlController::class, 'manage'])->name('urls.manage');
    Route::get('/urls/create', [UrlController::class, 'create'])->name('urls.create');
    Route::get('/urls/{url}', [UrlController::class, 'show'])->name('urls.show');
    Route::get('/urls/{url}/edit', [UrlController::class, 'edit'])->name('urls.edit');
    Route::put('/urls/{url}', [UrlController::class, 'update'])->name('urls.update');
    Route::delete('/urls/{url}', [UrlController::class, 'destroy'])->name('urls.destroy');
    Route::get('/urls/{url}/analytics', [UrlController::class, 'analytics'])->name('urls.analytics');

    // QR Code management
    Route::get('/urls/{url}/qr/customize', [QrCodeController::class, 'customize'])->name('qr.customize');
    Route::post('/urls/{url}/qr/generate', [QrCodeController::class, 'generate'])->name('qr.generate');
    Route::post('/urls/{url}/qr/generate-logo', [QrCodeController::class, 'generateWithLogo'])->name('qr.generate-logo');
    Route::post('/urls/{url}/qr/generate-multiple', [QrCodeController::class, 'generateMultiple'])->name('qr.generate-multiple');
    Route::delete('/urls/{url}/qr', [QrCodeController::class, 'destroy'])->name('qr.destroy');

    // Advanced Analytics
    Route::get('/urls/{url}/analytics/advanced', [AnalyticsController::class, 'show'])->name('analytics.show');
    Route::get('/urls/{url}/analytics/filter', [AnalyticsController::class, 'filter'])->name('analytics.filter');
    Route::get('/urls/{url}/analytics/real-time', [AnalyticsController::class, 'realTime'])->name('analytics.real-time');
    Route::post('/urls/{url}/analytics/compare', [AnalyticsController::class, 'compare'])->name('analytics.compare');
    Route::get('/urls/{url}/analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');
});

// URL redirection routes (catch-all - MUST be last)
Route::get('/{code}', [RedirectController::class, 'redirect'])->name('redirect');
Route::get('/{code}/preview', [RedirectController::class, 'preview'])->name('urls.preview');
