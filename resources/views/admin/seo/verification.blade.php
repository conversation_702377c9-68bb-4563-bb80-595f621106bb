@extends('layouts.dashboard-template')

@section('page_title', 'Search Engine Verification - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-shield-check mr-3 text-blue-600"></i>Search Engine Verification
@endsection
@section('page_description', 'Verify your website with search engines and webmaster tools')

@section('page_actions')
    <a href="{{ route('admin.seo.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to SEO
    </a>
    <a href="{{ route('admin.seo.test') }}" class="btn-primary">
        <i class="fas fa-vial mr-2"></i>Test Verification
    </a>
@endsection

@section('main_content')
    <!-- Google Search Console -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-google mr-2 text-red-500"></i>Google Search Console
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Google Site Verification -->
                    <div>
                        <label for="google_site_verification" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search mr-1 text-blue-500"></i>Google Site Verification Code
                        </label>
                        <input type="text" 
                               id="google_site_verification" 
                               name="verification[google_site_verification]" 
                               class="input-field"
                               value="{{ $verificationSettings->where('key', 'google_site_verification')->first()->value ?? '' }}"
                               placeholder="abcdefghijklmnopqrstuvwxyz123456789">
                        <p class="mt-1 text-sm text-gray-500">
                            Meta tag verification code from Google Search Console (content value only)
                        </p>
                    </div>

                    <!-- Instructions -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">How to get your Google verification code:</h4>
                        <ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                            <li>Go to <a href="https://search.google.com/search-console" target="_blank" class="underline">Google Search Console</a></li>
                            <li>Add your property ({{ config('app.url') }})</li>
                            <li>Choose "HTML tag" verification method</li>
                            <li>Copy the content value from the meta tag (not the entire tag)</li>
                            <li>Paste it in the field above</li>
                        </ol>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Google Verification
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bing Webmaster Tools -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-microsoft mr-2 text-blue-600"></i>Bing Webmaster Tools
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Bing Site Verification -->
                    <div>
                        <label for="bing_site_verification" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search mr-1 text-blue-500"></i>Bing Site Verification Code
                        </label>
                        <input type="text" 
                               id="bing_site_verification" 
                               name="verification[bing_site_verification]" 
                               class="input-field"
                               value="{{ $verificationSettings->where('key', 'bing_site_verification')->first()->value ?? '' }}"
                               placeholder="123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ">
                        <p class="mt-1 text-sm text-gray-500">
                            Meta tag verification code from Bing Webmaster Tools
                        </p>
                    </div>

                    <!-- Instructions -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">How to get your Bing verification code:</h4>
                        <ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                            <li>Go to <a href="https://www.bing.com/webmasters" target="_blank" class="underline">Bing Webmaster Tools</a></li>
                            <li>Add your site ({{ config('app.url') }})</li>
                            <li>Choose "Meta tag" verification option</li>
                            <li>Copy the content value from the meta tag</li>
                            <li>Paste it in the field above</li>
                        </ol>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Bing Verification
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Yandex Webmaster -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-yandex mr-2 text-red-600"></i>Yandex Webmaster
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Yandex Verification -->
                    <div>
                        <label for="yandex_verification" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search mr-1 text-red-500"></i>Yandex Verification Code
                        </label>
                        <input type="text" 
                               id="yandex_verification" 
                               name="verification[yandex_verification]" 
                               class="input-field"
                               value="{{ $verificationSettings->where('key', 'yandex_verification')->first()->value ?? '' }}"
                               placeholder="1234567890abcdef">
                        <p class="mt-1 text-sm text-gray-500">
                            Meta tag verification code from Yandex Webmaster
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Yandex Verification
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Pinterest -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-pinterest mr-2 text-red-500"></i>Pinterest
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Pinterest Verification -->
                    <div>
                        <label for="pinterest_verification" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-thumbtack mr-1 text-red-500"></i>Pinterest Verification Code
                        </label>
                        <input type="text" 
                               id="pinterest_verification" 
                               name="verification[pinterest_verification]" 
                               class="input-field"
                               value="{{ $verificationSettings->where('key', 'pinterest_verification')->first()->value ?? '' }}"
                               placeholder="1234567890abcdef1234567890abcdef">
                        <p class="mt-1 text-sm text-gray-500">
                            Meta tag verification code from Pinterest Business
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Pinterest Verification
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Verification Status -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Verification Status</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($verificationSettings->where('key', 'google_site_verification')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Google Search Console</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($verificationSettings->where('key', 'bing_site_verification')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Bing Webmaster</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($verificationSettings->where('key', 'yandex_verification')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Yandex Webmaster</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($verificationSettings->where('key', 'pinterest_verification')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Pinterest</div>
                </div>
            </div>
        </div>
    </div>
@endsection
