@extends('layouts.modern')

@section('title', 'Subscription Plans')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-crown"></i> Subscription Plans</h1>
                    <p class="text-muted mb-0">Choose the perfect plan for your needs</p>
                </div>
                <div>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    @if($currentSubscription)
                        <a href="{{ route('subscriptions.usage') }}" class="btn btn-info">
                            <i class="fas fa-chart-pie"></i> Usage
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Current Subscription -->
    @if($currentSubscription)
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-info-circle"></i> Current Plan</h6>
                        <p class="mb-0">
                            You are currently on the <strong>{{ $currentSubscription->name }}</strong> plan
                            @if($currentSubscription->price > 0)
                                ({{ $currentSubscription->getFormattedPrice() }}/month)
                            @endif
                        </p>
                    </div>
                    @if($currentSubscription->price > 0)
                        <form action="{{ route('subscriptions.cancel') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                Cancel Subscription
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Subscription Plans -->
    <div class="row">
        @foreach($subscriptions as $subscription)
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 {{ $currentSubscription && $currentSubscription->id === $subscription->id ? 'border-primary' : '' }}">
                @if($subscription->is_popular)
                    <div class="card-header bg-primary text-white text-center">
                        <i class="fas fa-star"></i> Most Popular
                    </div>
                @endif
                
                <div class="card-body text-center">
                    <h4 class="card-title">{{ $subscription->name }}</h4>
                    <div class="mb-3">
                        @if($subscription->price == 0)
                            <h2 class="text-success">Free</h2>
                        @else
                            <h2 class="text-primary">
                                {{ $subscription->getFormattedPrice() }}
                                <small class="text-muted">/month</small>
                            </h2>
                        @endif
                    </div>
                    
                    <p class="text-muted">{{ $subscription->description }}</p>
                    
                    <!-- Features List -->
                    <ul class="list-unstyled text-start">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            {{ $subscription->url_limit ? number_format($subscription->url_limit) . ' URLs' : 'Unlimited URLs' }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            {{ $subscription->click_limit ? number_format($subscription->click_limit) . ' clicks' : 'Unlimited clicks' }}
                        </li>
                        @if($subscription->custom_domains)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Custom domains ({{ $subscription->custom_domains_limit ?? 5 }})
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">Custom domains</span>
                            </li>
                        @endif
                        @if($subscription->analytics)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Advanced analytics
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">Advanced analytics</span>
                            </li>
                        @endif
                        @if($subscription->api_access)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                API access
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">API access</span>
                            </li>
                        @endif
                        @if($subscription->bulk_operations)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Bulk operations
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">Bulk operations</span>
                            </li>
                        @endif
                        @if($subscription->password_protection)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Password protection
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">Password protection</span>
                            </li>
                        @endif
                        @if($subscription->team_features)
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Team collaboration
                            </li>
                        @else
                            <li class="mb-2">
                                <i class="fas fa-times text-muted"></i>
                                <span class="text-muted">Team collaboration</span>
                            </li>
                        @endif
                    </ul>
                </div>
                
                <div class="card-footer">
                    @if($currentSubscription && $currentSubscription->id === $subscription->id)
                        <button class="btn btn-outline-primary w-100" disabled>
                            <i class="fas fa-check"></i> Current Plan
                        </button>
                    @else
                        <form action="{{ route('subscriptions.upgrade', $subscription) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary w-100">
                                @if($subscription->price == 0)
                                    <i class="fas fa-download"></i> Downgrade to Free
                                @else
                                    <i class="fas fa-arrow-up"></i> Upgrade to {{ $subscription->name }}
                                @endif
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Features Comparison -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Feature Comparison</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    @foreach($subscriptions as $subscription)
                                        <th class="text-center">{{ $subscription->name }}</th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Monthly URLs</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            {{ $subscription->url_limit ? number_format($subscription->url_limit) : 'Unlimited' }}
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Monthly Clicks</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            {{ $subscription->click_limit ? number_format($subscription->click_limit) : 'Unlimited' }}
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Custom Domains</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->custom_domains)
                                                <i class="fas fa-check text-success"></i>
                                                ({{ $subscription->custom_domains_limit ?? 5 }})
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Advanced Analytics</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->analytics)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>API Access</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->api_access)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Bulk Operations</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->bulk_operations)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Team Features</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->team_features)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                                <tr>
                                    <td><strong>Priority Support</strong></td>
                                    @foreach($subscriptions as $subscription)
                                        <td class="text-center">
                                            @if($subscription->priority_support)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle"></i> Frequently Asked Questions</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Can I change my plan anytime?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, you can upgrade your plan at any time. Downgrades take effect at the end of your current billing cycle.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What happens if I exceed my limits?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    If you exceed your URL or click limits, you'll be prompted to upgrade your plan. Your existing URLs will continue to work.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Do you offer refunds?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We offer a 30-day money-back guarantee for all paid plans. Contact support for assistance.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
