@extends('layouts.modern')

@section('title', 'Bulk Edit URLs - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Bulk Edit URLs</h1>
                <p class="text-gray-600 mt-1">Select and edit multiple URLs at once</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Operations
                </a>
                <a href="{{ route('dashboard') }}" class="btn-outline">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Bar -->
    <div class="card mb-6" id="bulkActionsBar" style="display: none;">
        <div class="card-body">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center mb-4 md:mb-0">
                    <span id="selectedCount" class="text-sm font-medium text-gray-900 mr-4">0 URLs selected</span>
                    <button onclick="selectAll()" class="text-blue-600 hover:text-blue-700 text-sm mr-4">Select All</button>
                    <button onclick="clearSelection()" class="text-gray-600 hover:text-gray-700 text-sm">Clear Selection</button>
                </div>
                <div class="flex space-x-3">
                    <button onclick="showBulkEditModal()" class="btn-primary">
                        <i class="fas fa-edit mr-2"></i>Edit Selected
                    </button>
                    <button onclick="bulkDelete()" class="btn-danger">
                        <i class="fas fa-trash mr-2"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" id="search" placeholder="Search URLs..." class="input-field">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" class="input-field">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div>
                    <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <select id="dateRange" class="input-field">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button onclick="applyFilters()" class="btn-primary w-full">
                        <i class="fas fa-filter mr-2"></i>Apply Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- URLs Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Your URLs</h3>
                <div class="text-sm text-gray-600">
                    {{ $urls->total() }} total URLs
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($urls->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left">
                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()" 
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    URL Details
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Short URL
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Clicks
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Created
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($urls as $url)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="url-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" 
                                           value="{{ $url->id }}" onchange="updateSelection()">
                                </td>
                                <td class="px-6 py-4">
                                    <div class="max-w-xs">
                                        <div class="text-sm font-medium text-gray-900 truncate">
                                            {{ $url->title ?: 'Untitled' }}
                                        </div>
                                        <div class="text-sm text-gray-500 truncate">
                                            {{ $url->original_url }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm font-mono text-blue-600">{{ $url->short_url }}</span>
                                        <button onclick="copyToClipboard('{{ $url->short_url }}')" 
                                                class="ml-2 text-gray-400 hover:text-gray-600">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ number_format($url->click_count) }}
                                </td>
                                <td class="px-6 py-4">
                                    @if($url->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ $url->created_at->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('urls.edit', $url) }}" 
                                           class="text-blue-600 hover:text-blue-700">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('urls.show', $url) }}" 
                                           class="text-green-600 hover:text-green-700">
                                            <i class="fas fa-chart-bar"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $urls->links() }}
                </div>
            @else
                <div class="p-8 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-link text-gray-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No URLs Found</h3>
                    <p class="text-gray-600 mb-4">You don't have any URLs yet or they don't match your filters.</p>
                    <a href="{{ route('urls.create') }}" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>Create Your First URL
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Bulk Edit Modal -->
<div id="bulkEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Bulk Edit URLs</h3>
                <button onclick="hideBulkEditModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="bulkEditForm">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Update Status</label>
                        <select name="status" class="input-field">
                            <option value="">Don't change</option>
                            <option value="1">Set to Active</option>
                            <option value="0">Set to Inactive</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Add Expiration Date</label>
                        <input type="datetime-local" name="expires_at" class="input-field">
                        <p class="text-xs text-gray-500 mt-1">Leave empty to not change expiration</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Add Password Protection</label>
                        <input type="password" name="password" class="input-field" placeholder="Leave empty to not change">
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideBulkEditModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Update URLs
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let selectedUrls = [];

function updateSelection() {
    const checkboxes = document.querySelectorAll('.url-checkbox:checked');
    selectedUrls = Array.from(checkboxes).map(cb => cb.value);
    
    const count = selectedUrls.length;
    document.getElementById('selectedCount').textContent = `${count} URL${count !== 1 ? 's' : ''} selected`;
    
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    if (count > 0) {
        bulkActionsBar.style.display = 'block';
    } else {
        bulkActionsBar.style.display = 'none';
    }
    
    // Update select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const allCheckboxes = document.querySelectorAll('.url-checkbox');
    selectAllCheckbox.checked = count === allCheckboxes.length;
    selectAllCheckbox.indeterminate = count > 0 && count < allCheckboxes.length;
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.url-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelection();
}

function selectAll() {
    document.querySelectorAll('.url-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

function clearSelection() {
    document.querySelectorAll('.url-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelection();
}

function showBulkEditModal() {
    if (selectedUrls.length === 0) {
        alert('Please select URLs to edit.');
        return;
    }
    document.getElementById('bulkEditModal').classList.remove('hidden');
}

function hideBulkEditModal() {
    document.getElementById('bulkEditModal').classList.add('hidden');
}

function bulkDelete() {
    if (selectedUrls.length === 0) {
        alert('Please select URLs to delete.');
        return;
    }
    
    if (confirm(`Are you sure you want to delete ${selectedUrls.length} URL${selectedUrls.length !== 1 ? 's' : ''}? This action cannot be undone.`)) {
        // Implement bulk delete
        console.log('Bulk delete:', selectedUrls);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-600');
        }, 2000);
    });
}

function applyFilters() {
    // Implement filtering logic
    console.log('Apply filters');
}

// Bulk edit form submission
document.getElementById('bulkEditForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        url_ids: selectedUrls,
        status: formData.get('status'),
        expires_at: formData.get('expires_at'),
        password: formData.get('password')
    };
    
    // Implement bulk update
    console.log('Bulk update:', data);
    hideBulkEditModal();
});

// Close modal when clicking outside
document.getElementById('bulkEditModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideBulkEditModal();
    }
});
</script>
@endpush
