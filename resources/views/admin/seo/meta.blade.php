@extends('layouts.dashboard-template')

@section('page_title', 'Meta Tags Management - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-tags mr-3 text-blue-600"></i>Meta Tags Management
@endsection
@section('page_description', 'Configure meta tags for better search engine optimization')

@section('page_actions')
    <a href="{{ route('admin.seo.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to SEO
    </a>
    <a href="{{ route('admin.seo.test') }}" class="btn-primary">
        <i class="fas fa-vial mr-2"></i>Test Meta Tags
    </a>
@endsection

@section('main_content')
    <!-- Meta Tags Configuration -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Meta Tags Configuration</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Site Title -->
                    <div>
                        <label for="site_title" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-heading mr-1 text-blue-500"></i>Site Title
                        </label>
                        <input type="text" 
                               id="site_title" 
                               name="meta[site_title]" 
                               class="input-field"
                               value="{{ $metaSettings->where('key', 'site_title')->first()->value ?? 'Minilink.at - Professional URL Shortener' }}"
                               placeholder="Your site title">
                        <p class="mt-1 text-sm text-gray-500">
                            The main title of your website (appears in browser tabs and search results)
                        </p>
                    </div>

                    <!-- Site Description -->
                    <div>
                        <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-align-left mr-1 text-green-500"></i>Site Description
                        </label>
                        <textarea id="site_description" 
                                  name="meta[site_description]" 
                                  rows="3" 
                                  class="input-field"
                                  placeholder="Brief description of your website">{{ $metaSettings->where('key', 'site_description')->first()->value ?? 'Professional URL shortening service with advanced analytics and custom domains.' }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">
                            Brief description that appears in search engine results (150-160 characters recommended)
                        </p>
                    </div>

                    <!-- Keywords -->
                    <div>
                        <label for="site_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tags mr-1 text-purple-500"></i>Keywords
                        </label>
                        <input type="text" 
                               id="site_keywords" 
                               name="meta[site_keywords]" 
                               class="input-field"
                               value="{{ $metaSettings->where('key', 'site_keywords')->first()->value ?? 'url shortener, link management, analytics, custom domains' }}"
                               placeholder="keyword1, keyword2, keyword3">
                        <p class="mt-1 text-sm text-gray-500">
                            Comma-separated keywords relevant to your website
                        </p>
                    </div>

                    <!-- Author -->
                    <div>
                        <label for="site_author" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-1 text-orange-500"></i>Author
                        </label>
                        <input type="text" 
                               id="site_author" 
                               name="meta[site_author]" 
                               class="input-field"
                               value="{{ $metaSettings->where('key', 'site_author')->first()->value ?? 'Minilink.at Team' }}"
                               placeholder="Website author or company name">
                    </div>

                    <!-- Robots -->
                    <div>
                        <label for="robots" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-robot mr-1 text-red-500"></i>Robots Meta Tag
                        </label>
                        <select id="robots" name="meta[robots]" class="input-field">
                            <option value="index, follow" {{ ($metaSettings->where('key', 'robots')->first()->value ?? 'index, follow') === 'index, follow' ? 'selected' : '' }}>
                                Index, Follow (Recommended)
                            </option>
                            <option value="index, nofollow" {{ ($metaSettings->where('key', 'robots')->first()->value ?? '') === 'index, nofollow' ? 'selected' : '' }}>
                                Index, No Follow
                            </option>
                            <option value="noindex, follow" {{ ($metaSettings->where('key', 'robots')->first()->value ?? '') === 'noindex, follow' ? 'selected' : '' }}>
                                No Index, Follow
                            </option>
                            <option value="noindex, nofollow" {{ ($metaSettings->where('key', 'robots')->first()->value ?? '') === 'noindex, nofollow' ? 'selected' : '' }}>
                                No Index, No Follow
                            </option>
                        </select>
                        <p class="mt-1 text-sm text-gray-500">
                            Controls how search engines crawl and index your website
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Meta Tags
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Meta Tags Preview -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Preview</h3>
        </div>
        <div class="card-body">
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">How your site appears in search results:</h4>
                <div class="bg-white p-4 rounded border">
                    <h3 class="text-lg text-blue-600 hover:underline cursor-pointer" id="preview-title">
                        {{ $metaSettings->where('key', 'site_title')->first()->value ?? 'Minilink.at - Professional URL Shortener' }}
                    </h3>
                    <p class="text-green-600 text-sm">{{ config('app.url') }}</p>
                    <p class="text-gray-600 text-sm mt-1" id="preview-description">
                        {{ $metaSettings->where('key', 'site_description')->first()->value ?? 'Professional URL shortening service with advanced analytics and custom domains.' }}
                    </p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Live preview updates
document.getElementById('site_title').addEventListener('input', function() {
    document.getElementById('preview-title').textContent = this.value || 'Minilink.at - Professional URL Shortener';
});

document.getElementById('site_description').addEventListener('input', function() {
    document.getElementById('preview-description').textContent = this.value || 'Professional URL shortening service with advanced analytics and custom domains.';
});
</script>
@endpush
