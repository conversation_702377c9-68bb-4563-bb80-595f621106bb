<!-- Common Settings for Bulk Operations -->

<!-- Custom Domain -->
@if($customDomains->count() > 0)
<div>
    <label for="custom_domain_id" class="block text-sm font-medium text-gray-700 mb-2">
        Custom Domain (Optional)
    </label>
    <select id="custom_domain_id" 
            name="custom_domain_id" 
            class="input-field @error('custom_domain_id') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
        <option value="">Use default domain ({{ config('app.url') }})</option>
        @foreach($customDomains as $domain)
            <option value="{{ $domain->id }}" {{ old('custom_domain_id') == $domain->id ? 'selected' : '' }}>
                {{ $domain->domain }}
            </option>
        @endforeach
    </select>
    @error('custom_domain_id')
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
    <p class="mt-1 text-sm text-gray-500">
        All URLs will use the selected custom domain
    </p>
</div>
@endif

<!-- URL Prefix -->
<div>
    <label for="url_prefix" class="block text-sm font-medium text-gray-700 mb-2">
        URL Prefix (Optional)
    </label>
    <input type="text" 
           id="url_prefix" 
           name="url_prefix" 
           class="input-field @error('url_prefix') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
           value="{{ old('url_prefix') }}"
           placeholder="e.g., campaign2024-"
           maxlength="20">
    @error('url_prefix')
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
    <p class="mt-1 text-sm text-gray-500">
        Add a prefix to all generated short codes (letters, numbers, and hyphens only)
    </p>
</div>

<!-- Auto-generate Titles -->
<div>
    <label class="flex items-center">
        <input type="checkbox" 
               name="auto_generate_titles" 
               value="1"
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
               {{ old('auto_generate_titles') ? 'checked' : '' }}>
        <span class="ml-2 text-sm text-gray-700">
            Auto-generate titles from page content
        </span>
    </label>
    <p class="mt-1 text-sm text-gray-500 ml-6">
        Automatically fetch and use page titles for URLs without titles
    </p>
</div>

<!-- Default Settings -->
<div class="border-t border-gray-200 pt-6">
    <h4 class="text-lg font-medium text-gray-900 mb-4">Default Settings</h4>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Default Status -->
        <div>
            <label class="flex items-center">
                <input type="checkbox" 
                       name="default_active" 
                       value="1"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {{ old('default_active', true) ? 'checked' : '' }}>
                <span class="ml-2 text-sm text-gray-700">
                    URLs are active by default
                </span>
            </label>
        </div>

        <!-- Track Clicks -->
        <div>
            <label class="flex items-center">
                <input type="checkbox" 
                       name="track_clicks" 
                       value="1"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {{ old('track_clicks', true) ? 'checked' : '' }}>
                <span class="ml-2 text-sm text-gray-700">
                    Enable click tracking
                </span>
            </label>
        </div>
    </div>
</div>

<!-- Advanced Options -->
<div class="border-t border-gray-200 pt-6">
    <h4 class="text-lg font-medium text-gray-900 mb-4">Advanced Options</h4>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Expiration Date -->
        <div>
            <label for="default_expires_at" class="block text-sm font-medium text-gray-700 mb-2">
                Default Expiration Date (Optional)
            </label>
            <input type="datetime-local" 
                   id="default_expires_at" 
                   name="default_expires_at" 
                   class="input-field @error('default_expires_at') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                   value="{{ old('default_expires_at') }}">
            @error('default_expires_at')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
            <p class="mt-1 text-sm text-gray-500">
                Set expiration date for all URLs (leave empty for no expiration)
            </p>
        </div>

        <!-- Password Protection -->
        <div>
            <label for="default_password" class="block text-sm font-medium text-gray-700 mb-2">
                Default Password (Optional)
            </label>
            <input type="password" 
                   id="default_password" 
                   name="default_password" 
                   class="input-field @error('default_password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                   placeholder="Leave empty for no password">
            @error('default_password')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
            <p class="mt-1 text-sm text-gray-500">
                Protect all URLs with the same password
            </p>
        </div>
    </div>
</div>

<!-- Processing Options -->
<div class="border-t border-gray-200 pt-6">
    <h4 class="text-lg font-medium text-gray-900 mb-4">Processing Options</h4>
    
    <div class="space-y-4">
        <!-- Skip Duplicates -->
        <div>
            <label class="flex items-center">
                <input type="checkbox" 
                       name="skip_duplicates" 
                       value="1"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {{ old('skip_duplicates', true) ? 'checked' : '' }}>
                <span class="ml-2 text-sm text-gray-700">
                    Skip duplicate URLs
                </span>
            </label>
            <p class="mt-1 text-sm text-gray-500 ml-6">
                Don't create short URLs for URLs that already exist in your account
            </p>
        </div>

        <!-- Continue on Error -->
        <div>
            <label class="flex items-center">
                <input type="checkbox" 
                       name="continue_on_error" 
                       value="1"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {{ old('continue_on_error', true) ? 'checked' : '' }}>
                <span class="ml-2 text-sm text-gray-700">
                    Continue processing if some URLs fail
                </span>
            </label>
            <p class="mt-1 text-sm text-gray-500 ml-6">
                Process all valid URLs even if some fail validation
            </p>
        </div>

        <!-- Email Notification -->
        <div>
            <label class="flex items-center">
                <input type="checkbox" 
                       name="email_notification" 
                       value="1"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {{ old('email_notification', true) ? 'checked' : '' }}>
                <span class="ml-2 text-sm text-gray-700">
                    Send email notification when complete
                </span>
            </label>
            <p class="mt-1 text-sm text-gray-500 ml-6">
                Receive an email with the results when processing is finished
            </p>
        </div>
    </div>
</div>
