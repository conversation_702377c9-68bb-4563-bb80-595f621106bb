<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- SEO Meta Tags -->
    <title>Minilink.at - Professional URL Shortener | Shorten Links, Track Clicks & Boost Marketing</title>
    <meta name="description" content="Professional URL shortener for businesses. Create short links, track clicks, decode shortened URLs, and boost your marketing campaigns. Best bio short link for affiliate marketing with advanced analytics.">
    <meta name="keywords" content="url shortener, shortened url, spotify url shortener, facebook url shortener, fb url shortener, url shortener api, decode shortened url, hubspot url shortener, best bio short link for affiliate marketing, shorten amazon links, shorten spotify link, dropbox short link, short links on facebook">
    <meta name="author" content="Minilink.at">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Minilink.at - Professional URL Shortener">
    <meta property="og:description" content="Create short links, track clicks, and boost your marketing campaigns with our professional URL shortener service.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ config('app.url') }}">
    <meta property="og:site_name" content="Minilink.at">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Minilink.at - Professional URL Shortener">
    <meta name="twitter:description" content="Create short links, track clicks, and boost your marketing campaigns with our professional URL shortener service.">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="{{ config('app.url') }}">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')

    <style>
        /* Custom homepage styles that extend Tailwind */
        .hero-gradient {
            background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
        }

        /* Card Component */
        .card {
            @apply bg-white border border-gray-200 rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
        }

        /* Button Component */
        .btn-primary {
            @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5;
        }

        /* FAQ Functionality */
        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .faq-answer.active {
            max-height: 200px;
        }
        .faq-icon {
            transition: transform 0.3s ease;
        }
        .faq-icon.active {
            transform: rotate(180deg);
        }

        /* Advanced Options */
        .advanced-options {
            display: none;
            animation: slideDown 0.3s ease-out;
        }
        .advanced-options.active {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .text-gray-400 { color: #9ca3af; }
        .text-gray-500 { color: #6b7280; }
        .hidden { display: none; }
    </style>
</head>
<body class="h-full bg-white font-sans antialiased">
    <div id="app" class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="{{ url('/') }}" class="flex items-center space-x-3">
                            <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                                <i class="fas fa-link text-white text-sm"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900">Minilink.at</span>
                        </a>
                    </div>
                
                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#features" class="text-gray-600 hover:text-blue-600 transition-colors">Features</a>
                        <a href="#stats" class="text-gray-600 hover:text-blue-600 transition-colors">Stats</a>
                        <a href="#testimonials" class="text-gray-600 hover:text-blue-600 transition-colors">Reviews</a>
                        <a href="#faq" class="text-gray-600 hover:text-blue-600 transition-colors">FAQ</a>
                        <a href="#blog" class="text-gray-600 hover:text-blue-600 transition-colors">Blog</a>
                    </div>

                    <!-- Auth Links -->
                    <div class="flex items-center space-x-4">
                        @auth
                            <a href="{{ url('/dashboard') }}" class="btn-primary">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="text-gray-600 hover:text-blue-600 transition-colors">Log in</a>
                            <a href="{{ route('register') }}" class="btn-primary">Get Started</a>
                        @endauth
                    </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient py-24 text-center relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-8">
                Professional <span class="text-blue-600">URL Shortener</span><br>
                for Modern Businesses
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Create short links, track clicks, and boost your marketing campaigns. The best bio short link solution for affiliate marketing with advanced analytics.
            </p>

            <!-- URL Shortener Form -->
            <form id="shortenForm" class="mb-6">
                <div class="flex gap-4 p-2 bg-white rounded-xl shadow-lg max-w-2xl mx-auto mb-8">
                    <input type="url"
                           id="longUrl"
                           name="url"
                           placeholder="Paste your long URL here (Amazon, Spotify, Facebook URLs)"
                           class="flex-1 px-6 py-4 border-0 rounded-lg text-base outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                    <button type="submit" class="btn-primary">Shorten URL</button>
                </div>
            </form>

            <!-- Advanced Options Toggle -->
            <div class="text-center">
                <button type="button"
                        class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm bg-blue-50 hover:bg-blue-100 px-4 py-2 rounded-lg transition-all duration-200"
                        onclick="toggleAdvanced()">
                    <i class="fas fa-sliders-h"></i>
                    <span>Advanced Options</span>
                    <i class="fas fa-chevron-down text-xs transition-transform duration-300" id="advancedChevron"></i>
                </button>
            </div>

            <!-- Advanced Options Panel -->
            <div id="advancedOptions" class="advanced-options max-w-2xl mx-auto mt-6 bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-6 shadow-lg">
                <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                    <i class="fas fa-cogs text-blue-600"></i>
                    Customize Your Short Link
                </h4>

                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-edit text-gray-400 mr-2"></i>
                            Custom Alias
                        </label>
                        <input type="text"
                               name="custom_alias"
                               placeholder="my-awesome-link"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200">
                        <p class="text-xs text-gray-500 mt-1">Create a memorable custom ending for your URL</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar-alt text-gray-400 mr-2"></i>
                            Expiration Date
                        </label>
                        <input type="date"
                               name="expires_at"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                               min="{{ date('Y-m-d') }}">
                        <p class="text-xs text-gray-500 mt-1">Set when this link should stop working</p>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag text-gray-400 mr-2"></i>
                        Description
                    </label>
                    <input type="text"
                           name="description"
                           placeholder="Marketing campaign for Q4 2024"
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200">
                    <p class="text-xs text-gray-500 mt-1">Add a note to help you remember what this link is for</p>
                </div>
            </div>

            <p class="text-sm text-gray-500 mb-8">
                Free forever • No registration required • Supports all major platforms
            </p>

            <!-- Trust Indicators -->
            <div class="flex justify-center items-center gap-6 flex-wrap mt-8">
                <div class="flex flex-col items-center gap-3 p-4 rounded-xl hover:transform hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-shield-alt text-lg"></i>
                    </div>
                    <span class="text-sm font-semibold text-gray-700">SSL Secured</span>
                </div>
                <div class="flex flex-col items-center gap-3 p-4 rounded-xl hover:transform hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-chart-line text-lg"></i>
                    </div>
                    <span class="text-sm font-semibold text-gray-700">Real-time Analytics</span>
                </div>
                <div class="flex flex-col items-center gap-3 p-4 rounded-xl hover:transform hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-globe text-lg"></i>
                    </div>
                    <span class="text-sm font-semibold text-gray-700">Custom Domains</span>
                </div>
                <div class="flex flex-col items-center gap-3 p-4 rounded-xl hover:transform hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-code text-lg"></i>
                    </div>
                    <span class="text-sm font-semibold text-gray-700">URL Shortener API</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section id="stats" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">1M+</div>
                    <div class="text-gray-600 font-medium">Links Created</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">50M+</div>
                    <div class="text-gray-600 font-medium">Clicks Tracked</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">10K+</div>
                    <div class="text-gray-600 font-medium">Happy Users</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">99.9%</div>
                    <div class="text-gray-600 font-medium">Uptime</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Why Choose Our URL Shortener?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    From shortened URLs to advanced analytics, we provide everything you need to optimize your link sharing strategy.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:border-blue-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-link text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                            Shorten Any URL
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-4">
                            Transform long Amazon links, Spotify URLs, Facebook posts, and any lengthy URL into clean, professional short links that enhance your brand.
                        </p>
                        <div class="flex items-center text-blue-600 font-medium text-sm group-hover:text-blue-700 transition-colors duration-300">
                            <span>Learn more</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:border-green-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors duration-300">
                            Advanced Analytics
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-4">
                            Track clicks, decode shortened URL performance, and gain detailed insights about your link engagement with real-time analytics dashboard.
                        </p>
                        <div class="flex items-center text-green-600 font-medium text-sm group-hover:text-green-700 transition-colors duration-300">
                            <span>View analytics</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:border-purple-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-globe text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                            Custom Domains
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-4">
                            Use your own domain for branded short links that build trust, improve click-through rates, and strengthen your brand identity.
                        </p>
                        <div class="flex items-center text-purple-600 font-medium text-sm group-hover:text-purple-700 transition-colors duration-300">
                            <span>Setup domain</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:border-orange-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-code text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors duration-300">
                            Powerful API
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-4">
                            Integrate our URL shortener API into your applications with comprehensive documentation, SDKs, and developer-friendly tools.
                        </p>
                        <div class="flex items-center text-orange-600 font-medium text-sm group-hover:text-orange-700 transition-colors duration-300">
                            <span>View API docs</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="group relative bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200 rounded-2xl p-8 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-100 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Special Badge -->
                    <div class="absolute top-4 right-4 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                        <i class="fas fa-star mr-1"></i>
                        POPULAR
                    </div>

                    <!-- Background Icon -->
                    <div class="absolute bottom-4 right-4 opacity-10">
                        <i class="fas fa-mobile-alt text-indigo-500 text-6xl"></i>
                    </div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-qrcode text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-indigo-600 transition-colors duration-300">
                            QR Code Generation
                        </h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            Automatically generate QR codes for your short links, perfect for offline marketing, print materials, and contactless sharing.
                        </p>
                        <div class="flex items-center text-indigo-600 font-medium text-sm group-hover:text-indigo-700 transition-colors duration-300">
                            <span>Generate QR</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="group relative bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-200 rounded-2xl p-8 hover:border-emerald-300 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-100 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500"></div>

                    <!-- Special Badge -->
                    <div class="absolute top-4 right-4 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                        <i class="fas fa-shield-check mr-1"></i>
                        SECURE
                    </div>

                    <!-- Background Icon -->
                    <div class="absolute bottom-4 right-4 opacity-10">
                        <i class="fas fa-lock text-emerald-500 text-6xl"></i>
                    </div>

                    <!-- Icon -->
                    <div class="relative z-10 w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors duration-300">
                            Enterprise Security
                        </h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            SSL encryption, malware protection, and enterprise-grade security features to keep your links safe and your users protected.
                        </p>
                        <div class="flex items-center text-emerald-600 font-medium text-sm group-hover:text-emerald-700 transition-colors duration-300">
                            <span>Security details</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What Our Users Say</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Trusted by thousands of businesses worldwide</p>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:shadow-2xl hover:border-blue-300 transition-all duration-500 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-700"></div>

                    <!-- Quote Icon -->
                    <div class="relative z-10 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-quote-left text-white text-xl"></i>
                        </div>
                    </div>

                    <!-- Rating -->
                    <div class="relative z-10 flex items-center mb-6">
                        <div class="flex text-yellow-400 mr-3">
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                        </div>
                        <span class="text-gray-600 font-semibold text-lg">5.0</span>
                        <span class="text-gray-400 text-sm ml-2">(Verified Review)</span>
                    </div>

                    <!-- Quote -->
                    <blockquote class="relative z-10 text-gray-700 mb-8 leading-relaxed text-lg font-medium">
                        "Best URL shortener for affiliate marketing! The analytics are incredible and help me track which Amazon links perform best. ROI increased by 40%!"
                    </blockquote>

                    <!-- Author -->
                    <div class="relative z-10 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-14 h-14 rounded-full overflow-hidden shadow-lg ring-2 ring-blue-100">
                                <img src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=3b82f6&color=ffffff&size=150&bold=true"
                                     alt="Sarah Johnson"
                                     class="w-full h-full object-cover">
                            </div>
                            <div class="ml-4">
                                <p class="font-bold text-gray-900 text-lg">Sarah Johnson</p>
                                <p class="text-gray-500 font-medium">Affiliate Marketing Expert</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-check-circle text-blue-500 text-sm mr-1"></i>
                                    <span class="text-xs text-gray-400">Verified Customer</span>
                                </div>
                            </div>
                        </div>

                        <!-- Company Logo Placeholder -->
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-700"></div>

                    <!-- Quote Icon -->
                    <div class="relative z-10 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-quote-left text-white text-xl"></i>
                        </div>
                    </div>

                    <!-- Rating -->
                    <div class="relative z-10 flex items-center mb-6">
                        <div class="flex text-yellow-400 mr-3">
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                        </div>
                        <span class="text-gray-600 font-semibold text-lg">5.0</span>
                        <span class="text-gray-400 text-sm ml-2">(Verified Review)</span>
                    </div>

                    <!-- Quote -->
                    <blockquote class="relative z-10 text-gray-700 mb-8 leading-relaxed text-lg font-medium">
                        "Perfect for our social media campaigns. We can shorten Spotify links, Facebook URLs, and track everything in one place. Game changer!"
                    </blockquote>

                    <!-- Author -->
                    <div class="relative z-10 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-14 h-14 rounded-full overflow-hidden shadow-lg ring-2 ring-green-100">
                                <img src="https://ui-avatars.com/api/?name=Mike+Chen&background=10b981&color=ffffff&size=150&bold=true"
                                     alt="Mike Chen"
                                     class="w-full h-full object-cover">
                            </div>
                            <div class="ml-4">
                                <p class="font-bold text-gray-900 text-lg">Mike Chen</p>
                                <p class="text-gray-500 font-medium">Digital Marketing Manager</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-check-circle text-green-500 text-sm mr-1"></i>
                                    <span class="text-xs text-gray-400">Verified Customer</span>
                                </div>
                            </div>
                        </div>

                        <!-- Company Logo Placeholder -->
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-8 hover:shadow-2xl hover:border-purple-300 transition-all duration-500 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-50 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-700"></div>

                    <!-- Quote Icon -->
                    <div class="relative z-10 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-quote-left text-white text-xl"></i>
                        </div>
                    </div>

                    <!-- Rating -->
                    <div class="relative z-10 flex items-center mb-6">
                        <div class="flex text-yellow-400 mr-3">
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                            <i class="fas fa-star text-lg"></i>
                        </div>
                        <span class="text-gray-600 font-semibold text-lg">5.0</span>
                        <span class="text-gray-400 text-sm ml-2">(Verified Review)</span>
                    </div>

                    <!-- Quote -->
                    <blockquote class="relative z-10 text-gray-700 mb-8 leading-relaxed text-lg font-medium">
                        "The API integration is seamless. We use it to automatically shorten all our Dropbox links and Amazon product URLs. Developer-friendly!"
                    </blockquote>

                    <!-- Author -->
                    <div class="relative z-10 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-14 h-14 rounded-full overflow-hidden shadow-lg ring-2 ring-purple-100">
                                <img src="https://ui-avatars.com/api/?name=Alex+Rodriguez&background=8b5cf6&color=ffffff&size=150&bold=true"
                                     alt="Alex Rodriguez"
                                     class="w-full h-full object-cover">
                            </div>
                            <div class="ml-4">
                                <p class="font-bold text-gray-900 text-lg">Alex Rodriguez</p>
                                <p class="text-gray-500 font-medium">Senior Software Developer</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-check-circle text-purple-500 text-sm mr-1"></i>
                                    <span class="text-xs text-gray-400">Verified Customer</span>
                                </div>
                            </div>
                        </div>

                        <!-- Company Logo Placeholder -->
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-code text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to know about our URL shortener</p>
            </div>

            <!-- FAQ Grid -->
            <div class="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- FAQ Item 1 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(1)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">How do I shorten Amazon links?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-1"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-1">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Simply paste your Amazon product URL into our shortener. Our service automatically creates a clean, professional short link that's perfect for affiliate marketing and social sharing.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(2)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">Can I decode shortened URLs to see the original link?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-2"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-2">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Yes! Our platform provides full transparency. You can decode any shortened URL to view the original destination, ensuring trust and security for your audience.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(3)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">Does the URL shortener work with Spotify and Facebook links?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-3"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-3">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Absolutely! Our service works with all major platforms including Spotify, Facebook, Instagram, YouTube, Dropbox, and any other website. Perfect for social media marketing.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(4)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">Is there a URL shortener API available?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-4"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-4">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Yes, we provide a robust REST API that allows you to integrate URL shortening into your applications, automate link creation, and manage your shortened URLs programmatically.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- FAQ Item 5 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(5)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">What makes this the best bio short link service for affiliate marketing?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-5"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-5">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Our advanced analytics, custom domains, click tracking, and professional appearance make it ideal for affiliate marketers who need reliable, trustworthy short links that convert.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 6 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(6)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">Can I use short links in Bullhorn automation texts?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-6"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-6">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Yes! Our short links are perfect for SMS marketing and automation platforms like Bullhorn. They save character space and provide clean, professional appearance in text messages.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 7 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(7)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">How reliable are the shortened URLs?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-7"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-7">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">We guarantee 99.9% uptime with enterprise-grade infrastructure. Your short links will always work, ensuring your marketing campaigns and affiliate links never fail.</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 8 -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                        <button class="w-full px-6 py-5 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center" onclick="toggleFaq(8)">
                            <h3 class="text-lg font-semibold text-gray-900 pr-4">Can I track shortened URL not allowing login issues?</h3>
                            <i class="fas fa-chevron-down text-gray-400 faq-icon transition-transform duration-300" id="icon-8"></i>
                        </button>
                        <div class="faq-answer bg-white" id="answer-8">
                            <div class="px-6 py-5 border-t border-gray-100">
                                <p class="text-gray-600 leading-relaxed">Yes! Our analytics dashboard helps you identify and troubleshoot access issues with your shortened URLs. You can monitor click patterns, detect login problems, and ensure your links work properly across all platforms including social media and email campaigns.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Latest Blog Posts</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Tips, tricks, and insights for better link management</p>
            </div>

            <!-- Blog Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Blog Post 1 -->
                <article class="group relative bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer">
                    <!-- Featured Image -->
                    <div class="relative h-56 bg-gradient-to-br from-blue-500 to-blue-600 overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <i class="fab fa-amazon text-white text-2xl"></i>
                        </div>

                        <!-- Category Badge -->
                        <div class="absolute top-4 left-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-blue-600">
                                <i class="fas fa-tag mr-1"></i>
                                Affiliate Marketing
                            </span>
                        </div>

                        <!-- Gradient Overlay -->
                        <div class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black to-transparent"></div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <!-- Meta Info -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center text-sm text-gray-500">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-blue-600 text-xs"></i>
                                </div>
                                <span>Marketing Team</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Dec 15, 2024</span>
                            </div>
                        </div>

                        <!-- Title -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                            How to Shorten Amazon Links for Better Conversions
                        </h3>

                        <!-- Excerpt -->
                        <p class="text-gray-600 leading-relaxed mb-6 line-clamp-3">
                            Learn the best practices for shortening Amazon affiliate links to improve click-through rates and boost your earnings. Discover advanced techniques used by top marketers.
                        </p>

                        <!-- Stats -->
                        <div class="flex items-center justify-between mb-6 pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <span>2.4k views</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>5 min read</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-gray-300"></i>
                            </div>
                        </div>

                        <!-- Read More Button -->
                        <a href="#" class="inline-flex items-center justify-center w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-300 group-hover:shadow-lg">
                            <span>Read Full Article</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>
                    </div>
                </article>

                <!-- Blog Post 2 -->
                <article class="group relative bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer">
                    <!-- Featured Image -->
                    <div class="relative h-56 bg-gradient-to-br from-green-500 to-green-600 overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-chart-line text-white text-2xl"></i>
                        </div>

                        <!-- Category Badge -->
                        <div class="absolute top-4 left-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-green-600">
                                <i class="fas fa-analytics mr-1"></i>
                                Analytics
                            </span>
                        </div>

                        <!-- Gradient Overlay -->
                        <div class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black to-transparent"></div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <!-- Meta Info -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center text-sm text-gray-500">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-green-600 text-xs"></i>
                                </div>
                                <span>Data Team</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Dec 12, 2024</span>
                            </div>
                        </div>

                        <!-- Title -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors duration-300 line-clamp-2">
                            URL Shortener Analytics: Track Your Success
                        </h3>

                        <!-- Excerpt -->
                        <p class="text-gray-600 leading-relaxed mb-6 line-clamp-3">
                            Discover how to use analytics to decode shortened URL performance and optimize your marketing campaigns with data-driven insights and advanced tracking.
                        </p>

                        <!-- Stats -->
                        <div class="flex items-center justify-between mb-6 pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <span>3.1k views</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>7 min read</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>

                        <!-- Read More Button -->
                        <a href="#" class="inline-flex items-center justify-center w-full px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-xl transition-all duration-300 group-hover:shadow-lg">
                            <span>Read Full Article</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>
                    </div>
                </article>

                <!-- Blog Post 3 -->
                <article class="group relative bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer">
                    <!-- Featured Image -->
                    <div class="relative h-56 bg-gradient-to-br from-purple-500 to-purple-600 overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-mobile-alt text-white text-2xl"></i>
                        </div>

                        <!-- Category Badge -->
                        <div class="absolute top-4 left-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-purple-600">
                                <i class="fas fa-share-alt mr-1"></i>
                                Social Media
                            </span>
                        </div>

                        <!-- Gradient Overlay -->
                        <div class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black to-transparent"></div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <!-- Meta Info -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center text-sm text-gray-500">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-purple-600 text-xs"></i>
                                </div>
                                <span>Social Team</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Dec 10, 2024</span>
                            </div>
                        </div>

                        <!-- Title -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors duration-300 line-clamp-2">
                            Best Bio Short Link Strategies for Social Media
                        </h3>

                        <!-- Excerpt -->
                        <p class="text-gray-600 leading-relaxed mb-6 line-clamp-3">
                            Maximize your social media bio effectiveness with strategic short link placement and tracking. Learn proven techniques to boost engagement.
                        </p>

                        <!-- Stats -->
                        <div class="flex items-center justify-between mb-6 pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <span>1.8k views</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>4 min read</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-gray-300"></i>
                            </div>
                        </div>

                        <!-- Read More Button -->
                        <a href="#" class="inline-flex items-center justify-center w-full px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-xl transition-all duration-300 group-hover:shadow-lg">
                            <span>Read Full Article</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>
                    </div>
                </article>
            </div>
        </div>
    </section>

    @include('partials.footer')

    <!-- JavaScript -->
    <script>
        // Toggle Advanced Options
        function toggleAdvanced() {
            const options = document.getElementById('advancedOptions');
            const chevron = document.getElementById('advancedChevron');

            options.classList.toggle('active');

            if (options.classList.contains('active')) {
                chevron.style.transform = 'rotate(180deg)';
            } else {
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        // Toggle FAQ Items
        function toggleFaq(id) {
            const answer = document.getElementById('answer-' + id);
            const icon = document.getElementById('icon-' + id);

            // Close all other FAQs
            for (let i = 1; i <= 8; i++) {
                if (i !== id) {
                    document.getElementById('answer-' + i).classList.remove('active');
                    document.getElementById('icon-' + i).classList.remove('active');
                }
            }

            // Toggle current FAQ
            answer.classList.toggle('active');
            icon.classList.toggle('active');
        }

        // Handle URL Shortening Form
        document.getElementById('shortenForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = e.target;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;

            // Get advanced options if they exist
            const advancedOptions = document.getElementById('advancedOptions');
            if (advancedOptions.classList.contains('active')) {
                const customAlias = document.querySelector('input[name="custom_alias"]').value;
                const expiresAt = document.querySelector('input[name="expires_at"]').value;
                const description = document.querySelector('input[name="description"]').value;

                if (customAlias) formData.append('custom_alias', customAlias);
                if (expiresAt) formData.append('expires_at', expiresAt);
                if (description) formData.append('description', description);
            }

            // Add CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                formData.append('_token', csrfToken.getAttribute('content'));
            } else {
                console.error('CSRF token not found');
                alert('Security token not found. Please refresh the page and try again.');
                return;
            }

            // Show loading state
            submitBtn.textContent = 'Shortening...';
            submitBtn.disabled = true;

            // Make AJAX request
            fetch('/shorten', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            })
            .then(response => {
                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Server returned non-JSON response');
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message with shortened URL
                    showResult(data.data);
                } else {
                    throw new Error(data.message || 'Failed to shorten URL');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalBtnText;
                submitBtn.disabled = false;
            });
        });

        // Show shortened URL result
        function showResult(data) {
            const resultHtml = `
                <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 0.75rem; padding: 1.5rem; margin-top: 1rem; max-width: 42rem; margin-left: auto; margin-right: auto;">
                    <h3 style="color: #0c4a6e; margin: 0 0 1rem 0; font-weight: 600;">✅ URL Shortened Successfully!</h3>
                    <div style="background: white; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Your Short URL:</label>
                        <div style="display: flex; gap: 0.5rem;">
                            <input type="text" value="${data.short_url}" readonly
                                   style="flex: 1; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; background: #f9fafb;"
                                   onclick="this.select()">
                            <button onclick="copyToClipboard('${data.short_url}')"
                                    style="padding: 0.75rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                                Copy
                            </button>
                        </div>
                    </div>
                    <div style="display: flex; gap: 1rem; font-size: 0.875rem; color: #6b7280;">
                        <span>📊 Clicks: 0</span>
                        <span>📅 Created: ${new Date(data.created_at).toLocaleDateString()}</span>
                        ${data.qr_code_url ? '<a href="' + data.qr_code_url + '" target="_blank" style="color: #2563eb;">📱 QR Code</a>' : ''}
                    </div>
                </div>
            `;

            // Insert result after the form
            const form = document.getElementById('shortenForm');
            const existingResult = document.querySelector('.url-result');
            if (existingResult) {
                existingResult.remove();
            }

            const resultDiv = document.createElement('div');
            resultDiv.className = 'url-result';
            resultDiv.innerHTML = resultHtml;
            form.parentNode.insertBefore(resultDiv, form.nextSibling);
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show temporary success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#16a34a';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#2563eb';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Failed to copy to clipboard');
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    </div> <!-- End of #app -->
</body>
</html>
