@extends('layouts.modern')

@section('title', 'Change Password - Minilink.at')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-key mr-3 text-yellow-600"></i>
                    Change Password
                </h1>
                <p class="text-gray-600 mt-1">Update your account password for better security</p>
            </div>
            <div>
                <a href="{{ route('profile.show') }}" class="btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Profile
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-lock mr-2 text-yellow-600"></i>
                        Update Password
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('profile.update-password') }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Current Password -->
                        <div class="space-y-2">
                            <label for="current_password" class="block text-sm font-medium text-gray-700">
                                Current Password
                            </label>
                            <div class="relative">
                                <input type="password"
                                       class="form-input @error('current_password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                       id="current_password"
                                       name="current_password"
                                       placeholder="Enter your current password"
                                       required>
                                <button type="button"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="current_password_icon"></i>
                                </button>
                            </div>
                            @error('current_password')
                                <p class="text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="text-sm text-gray-500">Enter your current password to confirm your identity.</p>
                        </div>

                        <!-- New Password -->
                        <div class="space-y-2">
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                New Password
                            </label>
                            <div class="relative">
                                <input type="password"
                                       class="form-input @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                       id="password"
                                       name="password"
                                       placeholder="Enter your new password"
                                       required>
                                <button type="button"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        onclick="togglePassword('password')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password_icon"></i>
                                </button>
                            </div>
                            @error('password')
                                <p class="text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <div class="text-sm text-gray-500">
                                Password must be at least 8 characters long and contain a mix of letters, numbers, and symbols.
                            </div>

                            <!-- Password Strength Indicator -->
                            <div class="mt-2">
                                <div class="flex space-x-1" id="password-strength">
                                    <div class="h-2 w-full bg-gray-200 rounded-full">
                                        <div class="h-2 bg-red-500 rounded-full transition-all duration-300" style="width: 0%" id="strength-bar"></div>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1" id="strength-text">Password strength: Weak</p>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="space-y-2">
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                                Confirm New Password
                            </label>
                            <div class="relative">
                                <input type="password"
                                       class="form-input"
                                       id="password_confirmation"
                                       name="password_confirmation"
                                       placeholder="Confirm your new password"
                                       required>
                                <button type="button"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        onclick="togglePassword('password_confirmation')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password_confirmation_icon"></i>
                                </button>
                            </div>
                            <p class="text-sm text-gray-500">Re-enter your new password to confirm.</p>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save mr-2"></i>Update Password
                            </button>
                            <a href="{{ route('profile.show') }}" class="btn-outline">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Password Security Tips -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                        Security Tips
                    </h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Use a strong password</h4>
                                <p class="text-sm text-gray-600">At least 8 characters with letters, numbers, and symbols.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Make it unique</h4>
                                <p class="text-sm text-gray-600">Don't reuse passwords from other accounts.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Use a password manager</h4>
                                <p class="text-sm text-gray-600">Generate and store secure passwords safely.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Enable 2FA</h4>
                                <p class="text-sm text-gray-600">Add an extra layer of security.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Change regularly</h4>
                                <p class="text-sm text-gray-600">Update periodically, especially if compromised.</p>
                            </div>
                        </div>
                    </div>

                    <div class="pt-4 border-t border-gray-200">
                        <a href="#" class="w-full btn-primary text-center">
                            <i class="fas fa-mobile-alt mr-2"></i>Enable Two-Factor Authentication
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-history mr-2 text-blue-600"></i>
                        Recent Activity
                    </h3>
                </div>
                <div class="card-body">
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">Last login: {{ auth()->user()->updated_at->diffForHumans() }}</span>
                        </div>
                        <div class="flex items-center space-x-3 text-sm">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-gray-600">Account created: {{ auth()->user()->created_at->diffForHumans() }}</span>
                        </div>
                        <div class="flex items-center space-x-3 text-sm">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            <span class="text-gray-600">Password last changed: Recently</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Password visibility toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    updateStrengthIndicator(strength);
});

function calculatePasswordStrength(password) {
    let score = 0;

    if (password.length >= 8) score++;
    if (password.match(/[a-z]/)) score++;
    if (password.match(/[A-Z]/)) score++;
    if (password.match(/[0-9]/)) score++;
    if (password.match(/[^a-zA-Z0-9]/)) score++;

    return score;
}

function updateStrengthIndicator(strength) {
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');

    const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const strengthColors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];
    const widths = [20, 40, 60, 80, 100];

    if (strength === 0) {
        strengthBar.style.width = '0%';
        strengthBar.className = 'h-2 bg-red-500 rounded-full transition-all duration-300';
        strengthText.textContent = 'Password strength: Very Weak';
        strengthText.className = 'text-xs text-red-500 mt-1';
        return;
    }

    const strengthLevel = Math.min(strength, 5);
    strengthBar.style.width = widths[strengthLevel - 1] + '%';
    strengthBar.className = `h-2 ${strengthColors[strengthLevel - 1]} rounded-full transition-all duration-300`;
    strengthText.textContent = `Password strength: ${strengthTexts[strengthLevel - 1]}`;

    const textColors = ['text-red-500', 'text-orange-500', 'text-yellow-500', 'text-blue-500', 'text-green-500'];
    strengthText.className = `text-xs ${textColors[strengthLevel - 1]} mt-1`;
}

// Password confirmation validation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmation = this.value;

    if (confirmation && password !== confirmation) {
        this.classList.add('border-red-300');
        this.classList.remove('border-gray-300');
    } else {
        this.classList.remove('border-red-300');
        this.classList.add('border-gray-300');
    }
});
</script>
@endpush
