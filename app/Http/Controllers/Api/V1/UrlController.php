<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Url;
use App\Services\UrlShortenerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class UrlController extends ApiController
{
    protected UrlShortenerService $urlShortenerService;

    public function __construct(UrlShortenerService $urlShortenerService)
    {
        $this->urlShortenerService = $urlShortenerService;
    }

    /**
     * Get all URLs for the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $pagination = $this->validatePagination($request);

            $query = $user->urls();

            // Apply search filter
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('original_url', 'like', "%{$search}%")
                      ->orWhere('custom_alias', 'like', "%{$search}%");
                });
            }

            // Apply status filter
            if ($request->filled('status')) {
                switch ($request->get('status')) {
                    case 'active':
                        $query->where('is_active', true);
                        break;
                    case 'inactive':
                        $query->where('is_active', false);
                        break;
                    case 'expired':
                        $query->where('expires_at', '<', now());
                        break;
                }
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            $allowedSorts = ['created_at', 'title', 'click_count', 'original_url'];
            if (in_array($sortBy, $allowedSorts)) {
                $query->orderBy($sortBy, $sortOrder);
            }

            $urls = $query->paginate($pagination['per_page'], ['*'], 'page', $pagination['page']);

            $transformedUrls = $urls->getCollection()->map(function ($url) {
                return $this->transformUrl($url);
            });

            $urls->setCollection($transformedUrls);

            $response = $this->paginatedResponse($urls, 'URLs retrieved successfully');
            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create a new short URL.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'original_url' => 'required|url|max:2048',
                'title' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:500',
                'custom_alias' => 'nullable|string|max:50|unique:urls,custom_alias',
                'expires_at' => 'nullable|date|after:now',
                'password' => 'nullable|string|min:4|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $user = $this->getApiUser($request);

            // Check user's URL limit (if any)
            $maxUrls = $user->subscription?->url_limit;
            if ($maxUrls && $user->urls()->count() >= $maxUrls) {
                return $this->errorResponse(
                    'URL limit reached. Upgrade your subscription to create more URLs.',
                    403
                );
            }

            $urlData = [
                'original_url' => $request->original_url,
                'title' => $request->title,
                'description' => $request->description,
                'custom_alias' => $request->custom_alias,
                'expires_at' => $request->expires_at,
                'password' => $request->password,
                'user_id' => $user->id,
            ];

            $url = $this->urlShortenerService->createShortUrl($urlData);

            $response = $this->successResponse(
                $this->transformUrl($url),
                'URL created successfully',
                201
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get a specific URL.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $url = Url::where('id', $id)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $response = $this->successResponse(
                $this->transformUrl($url),
                'URL retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update a specific URL.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $url = Url::where('id', $id)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:500',
                'custom_alias' => 'nullable|string|max:50|unique:urls,custom_alias,' . $url->id,
                'expires_at' => 'nullable|date|after:now',
                'is_active' => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $updateData = $request->only(['title', 'description', 'custom_alias', 'expires_at']);
            
            if ($request->has('is_active')) {
                $updateData['is_active'] = $request->boolean('is_active');
            }

            $url->update($updateData);

            $response = $this->successResponse(
                $this->transformUrl($url->fresh()),
                'URL updated successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete a specific URL.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $url = Url::where('id', $id)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $url->delete();

            $response = $this->successResponse(
                null,
                'URL deleted successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get URL statistics.
     */
    public function stats(Request $request, string $id): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $url = Url::where('id', $id)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $period = $request->get('period', 'week'); // week, month, year, all
            
            $startDate = match($period) {
                'week' => now()->subWeek(),
                'month' => now()->subMonth(),
                'year' => now()->subYear(),
                'all' => null,
                default => now()->subWeek(),
            };

            $analyticsQuery = $url->analytics();
            if ($startDate) {
                $analyticsQuery->where('clicked_at', '>=', $startDate);
            }

            $stats = [
                'url_id' => $url->id,
                'period' => $period,
                'total_clicks' => $analyticsQuery->count(),
                'unique_clicks' => $analyticsQuery->distinct('ip_address')->count(),
                'top_countries' => $analyticsQuery
                    ->selectRaw('country, COUNT(*) as count')
                    ->whereNotNull('country')
                    ->groupBy('country')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
                'top_referrers' => $analyticsQuery
                    ->selectRaw('referrer, COUNT(*) as count')
                    ->whereNotNull('referrer')
                    ->where('referrer', '!=', '')
                    ->groupBy('referrer')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
                'devices' => $analyticsQuery
                    ->selectRaw('device_type, COUNT(*) as count')
                    ->whereNotNull('device_type')
                    ->groupBy('device_type')
                    ->orderBy('count', 'desc')
                    ->get(),
                'browsers' => $analyticsQuery
                    ->selectRaw('browser, COUNT(*) as count')
                    ->whereNotNull('browser')
                    ->groupBy('browser')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
            ];

            $response = $this->successResponse(
                $stats,
                'URL statistics retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Bulk operations on URLs.
     */
    public function bulk(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:delete,activate,deactivate',
                'url_ids' => 'required|array|min:1|max:100',
                'url_ids.*' => 'integer|exists:urls,id',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $user = $this->getApiUser($request);
            $urlIds = $request->get('url_ids');
            $action = $request->get('action');

            // Verify user owns all URLs
            $urls = Url::whereIn('id', $urlIds)
                       ->where('user_id', $user->id)
                       ->get();

            if ($urls->count() !== count($urlIds)) {
                return $this->errorResponse(
                    'You can only perform actions on your own URLs',
                    403
                );
            }

            $affectedCount = 0;

            switch ($action) {
                case 'delete':
                    $affectedCount = $urls->count();
                    Url::whereIn('id', $urlIds)->delete();
                    break;
                    
                case 'activate':
                    $affectedCount = Url::whereIn('id', $urlIds)
                                       ->update(['is_active' => true]);
                    break;
                    
                case 'deactivate':
                    $affectedCount = Url::whereIn('id', $urlIds)
                                       ->update(['is_active' => false]);
                    break;
            }

            $response = $this->successResponse([
                'action' => $action,
                'affected_count' => $affectedCount,
                'url_ids' => $urlIds,
            ], "Successfully {$action}d {$affectedCount} URL(s)");

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
