<?php

namespace App\Services;

use App\Models\CustomDomain;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DnsVerificationService
{
    /**
     * Verify domain ownership through DNS records.
     */
    public function verifyDomain(CustomDomain $customDomain): array
    {
        try {
            $domain = $customDomain->domain;
            $verificationToken = $customDomain->verification_token;

            // Check for TXT record verification
            $txtVerification = $this->verifyTxtRecord($domain, $verificationToken);
            
            if ($txtVerification['verified']) {
                // Also check if domain points to our server
                $cnameVerification = $this->verifyCnameRecord($domain);
                
                return [
                    'verified' => true,
                    'method' => 'TXT + CNAME',
                    'message' => 'Domain verified successfully via DNS records.',
                    'details' => [
                        'txt_record' => $txtVerification,
                        'cname_record' => $cnameVerification,
                    ],
                ];
            }

            return [
                'verified' => false,
                'message' => 'DNS verification failed. Please ensure the TXT record is correctly configured.',
                'details' => $txtVerification,
            ];

        } catch (\Exception $e) {
            Log::error('DNS verification failed', [
                'domain' => $customDomain->domain,
                'error' => $e->getMessage(),
            ]);

            return [
                'verified' => false,
                'message' => 'DNS verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify TXT record for domain ownership.
     */
    private function verifyTxtRecord(string $domain, string $expectedToken): array
    {
        try {
            $txtRecords = $this->getTxtRecords($domain, '_minilink-verification');
            
            foreach ($txtRecords as $record) {
                if (trim($record) === $expectedToken) {
                    return [
                        'verified' => true,
                        'found_records' => $txtRecords,
                        'expected_token' => $expectedToken,
                    ];
                }
            }

            return [
                'verified' => false,
                'found_records' => $txtRecords,
                'expected_token' => $expectedToken,
                'message' => 'Verification token not found in TXT records.',
            ];

        } catch (\Exception $e) {
            return [
                'verified' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to retrieve TXT records.',
            ];
        }
    }

    /**
     * Verify CNAME record points to our domain.
     */
    private function verifyCnameRecord(string $domain): array
    {
        try {
            $appDomain = parse_url(config('app.url'), PHP_URL_HOST);
            $cnameRecords = $this->getCnameRecords($domain);
            
            foreach ($cnameRecords as $record) {
                if (str_contains($record, $appDomain)) {
                    return [
                        'verified' => true,
                        'found_records' => $cnameRecords,
                        'expected_target' => $appDomain,
                    ];
                }
            }

            // Also check A record as fallback
            $aRecords = $this->getARecords($domain);
            $appIp = gethostbyname($appDomain);
            
            foreach ($aRecords as $record) {
                if ($record === $appIp) {
                    return [
                        'verified' => true,
                        'method' => 'A_RECORD',
                        'found_records' => $aRecords,
                        'expected_ip' => $appIp,
                    ];
                }
            }

            return [
                'verified' => false,
                'cname_records' => $cnameRecords,
                'a_records' => $aRecords,
                'expected_target' => $appDomain,
                'expected_ip' => $appIp,
            ];

        } catch (\Exception $e) {
            return [
                'verified' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get TXT records for a domain.
     */
    private function getTxtRecords(string $domain, string $subdomain = ''): array
    {
        $fullDomain = $subdomain ? "{$subdomain}.{$domain}" : $domain;
        
        try {
            $records = dns_get_record($fullDomain, DNS_TXT);
            return array_column($records, 'txt');
        } catch (\Exception $e) {
            // Fallback to external DNS service
            return $this->getRecordsViaApi($fullDomain, 'TXT');
        }
    }

    /**
     * Get CNAME records for a domain.
     */
    private function getCnameRecords(string $domain): array
    {
        try {
            $records = dns_get_record($domain, DNS_CNAME);
            return array_column($records, 'target');
        } catch (\Exception $e) {
            return $this->getRecordsViaApi($domain, 'CNAME');
        }
    }

    /**
     * Get A records for a domain.
     */
    private function getARecords(string $domain): array
    {
        try {
            $records = dns_get_record($domain, DNS_A);
            return array_column($records, 'ip');
        } catch (\Exception $e) {
            return $this->getRecordsViaApi($domain, 'A');
        }
    }

    /**
     * Get DNS records via external API (fallback).
     */
    private function getRecordsViaApi(string $domain, string $type): array
    {
        try {
            // Using Google DNS-over-HTTPS as fallback
            $response = Http::timeout(10)->get('https://dns.google/resolve', [
                'name' => $domain,
                'type' => $type,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['Answer'])) {
                    return array_column($data['Answer'], 'data');
                }
            }

            return [];
        } catch (\Exception $e) {
            Log::warning('DNS API fallback failed', [
                'domain' => $domain,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            return [];
        }
    }

    /**
     * Check SSL certificate for domain.
     */
    public function checkSslCertificate(string $domain): array
    {
        try {
            $context = stream_context_create([
                'ssl' => [
                    'capture_peer_cert' => true,
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                ],
            ]);

            $socket = stream_socket_client(
                "ssl://{$domain}:443",
                $errno,
                $errstr,
                10,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$socket) {
                return [
                    'valid' => false,
                    'message' => "Failed to connect: {$errstr}",
                ];
            }

            $cert = stream_context_get_params($socket)['options']['ssl']['peer_certificate'];
            $certInfo = openssl_x509_parse($cert);

            fclose($socket);

            $expiresAt = date('Y-m-d H:i:s', $certInfo['validTo_time_t']);
            $isValid = $certInfo['validTo_time_t'] > time();

            return [
                'valid' => $isValid,
                'expires_at' => $expiresAt,
                'issuer' => $certInfo['issuer']['CN'] ?? 'Unknown',
                'subject' => $certInfo['subject']['CN'] ?? 'Unknown',
                'message' => $isValid ? 'SSL certificate is valid' : 'SSL certificate has expired',
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'SSL check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Test complete domain configuration.
     */
    public function testDomainConfiguration(CustomDomain $customDomain): array
    {
        $errors = [];
        $domain = $customDomain->domain;

        // Test DNS resolution
        $dnsTest = $this->verifyCnameRecord($domain);
        if (!$dnsTest['verified']) {
            $errors[] = 'DNS resolution failed - domain does not point to our servers';
        }

        // Test HTTP connectivity
        try {
            $response = Http::timeout(10)->get("http://{$domain}");
            if (!$response->successful()) {
                $errors[] = 'HTTP connectivity test failed';
            }
        } catch (\Exception $e) {
            $errors[] = 'HTTP connectivity test failed: ' . $e->getMessage();
        }

        // Test HTTPS if SSL is enabled
        if ($customDomain->ssl_enabled) {
            try {
                $response = Http::timeout(10)->get("https://{$domain}");
                if (!$response->successful()) {
                    $errors[] = 'HTTPS connectivity test failed';
                }
            } catch (\Exception $e) {
                $errors[] = 'HTTPS connectivity test failed: ' . $e->getMessage();
            }
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
            'domain' => $domain,
            'tested_at' => now()->toISOString(),
        ];
    }

    /**
     * Get domain health status.
     */
    public function getDomainHealth(CustomDomain $customDomain): array
    {
        $health = [
            'overall' => 'healthy',
            'checks' => [],
        ];

        // DNS Health
        $dnsCheck = $this->verifyCnameRecord($customDomain->domain);
        $health['checks']['dns'] = [
            'status' => $dnsCheck['verified'] ? 'healthy' : 'unhealthy',
            'message' => $dnsCheck['verified'] ? 'DNS correctly configured' : 'DNS configuration issues',
        ];

        // SSL Health
        if ($customDomain->ssl_enabled) {
            $sslCheck = $this->checkSslCertificate($customDomain->domain);
            $health['checks']['ssl'] = [
                'status' => $sslCheck['valid'] ? 'healthy' : 'unhealthy',
                'message' => $sslCheck['message'],
                'expires_at' => $sslCheck['expires_at'] ?? null,
            ];

            // Check if SSL expires soon (within 30 days)
            if ($sslCheck['valid'] && isset($sslCheck['expires_at'])) {
                $expiresAt = strtotime($sslCheck['expires_at']);
                $daysUntilExpiry = ($expiresAt - time()) / (24 * 60 * 60);
                
                if ($daysUntilExpiry <= 30) {
                    $health['checks']['ssl']['status'] = 'warning';
                    $health['checks']['ssl']['message'] = "SSL certificate expires in {$daysUntilExpiry} days";
                }
            }
        }

        // Determine overall health
        $unhealthyChecks = array_filter($health['checks'], function($check) {
            return $check['status'] === 'unhealthy';
        });

        $warningChecks = array_filter($health['checks'], function($check) {
            return $check['status'] === 'warning';
        });

        if (!empty($unhealthyChecks)) {
            $health['overall'] = 'unhealthy';
        } elseif (!empty($warningChecks)) {
            $health['overall'] = 'warning';
        }

        return $health;
    }
}
