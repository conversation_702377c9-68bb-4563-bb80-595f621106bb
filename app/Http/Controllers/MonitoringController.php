<?php

namespace App\Http\Controllers;

use App\Services\PerformanceOptimizationService;
use App\Services\SecurityService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MonitoringController extends Controller
{
    protected PerformanceOptimizationService $performanceService;
    protected SecurityService $securityService;

    public function __construct(
        PerformanceOptimizationService $performanceService,
        SecurityService $securityService
    ) {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->performanceService = $performanceService;
        $this->securityService = $securityService;
    }

    /**
     * Show monitoring dashboard.
     */
    public function index(): View
    {
        $systemHealth = $this->getSystemHealth();
        $performanceMetrics = $this->performanceService->getPerformanceMetrics();
        $securityStatus = $this->getSecurityStatus();
        $recentAlerts = $this->getRecentAlerts();

        return view('admin.monitoring.index', compact(
            'systemHealth',
            'performanceMetrics',
            'securityStatus',
            'recentAlerts'
        ));
    }

    /**
     * Get system health status.
     */
    public function health(): JsonResponse
    {
        $health = $this->getSystemHealth();
        
        return response()->json([
            'status' => $health['overall_status'],
            'timestamp' => now()->toISOString(),
            'details' => $health,
        ]);
    }

    /**
     * Get performance metrics.
     */
    public function performance(): JsonResponse
    {
        $metrics = $this->performanceService->getPerformanceMetrics();
        
        return response()->json([
            'success' => true,
            'data' => $metrics,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get security status.
     */
    public function security(): JsonResponse
    {
        $security = $this->getSecurityStatus();
        
        return response()->json([
            'success' => true,
            'data' => $security,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Optimize system performance.
     */
    public function optimize(Request $request): JsonResponse
    {
        try {
            $results = [];
            
            if ($request->get('database', false)) {
                $dbOptimizations = $this->performanceService->optimizeDatabase();
                $results['database'] = $dbOptimizations;
            }
            
            if ($request->get('cache', false)) {
                $cacheOptimizations = $this->performanceService->implementCaching();
                $results['cache'] = $cacheOptimizations;
            }
            
            if ($request->get('application', false)) {
                $appOptimizations = $this->performanceService->optimizeApplication();
                $results['application'] = $appOptimizations;
            }
            
            Log::info('System optimization performed', [
                'user_id' => auth()->id(),
                'optimizations' => $results,
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'System optimization completed',
                'results' => $results,
            ]);
            
        } catch (\Exception $e) {
            Log::error('System optimization failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Optimization failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear system caches.
     */
    public function clearCache(): JsonResponse
    {
        try {
            $results = $this->performanceService->clearAllCaches();
            
            Log::info('System caches cleared', [
                'user_id' => auth()->id(),
                'results' => $results,
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'All caches cleared successfully',
                'results' => $results,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Cache clearing failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Cache clearing failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get application logs.
     */
    public function logs(Request $request): JsonResponse
    {
        $level = $request->get('level', 'all');
        $limit = min($request->get('limit', 100), 1000);
        
        try {
            $logs = $this->getApplicationLogs($level, $limit);
            
            return response()->json([
                'success' => true,
                'data' => $logs,
                'total' => count($logs),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve logs: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate system report.
     */
    public function report(): JsonResponse
    {
        try {
            $report = [
                'generated_at' => now()->toISOString(),
                'system_health' => $this->getSystemHealth(),
                'performance' => $this->performanceService->generatePerformanceReport(),
                'security' => $this->securityService->generateSecurityReport(),
                'database_stats' => $this->getDatabaseStats(),
                'application_stats' => $this->getApplicationStats(),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get system health status.
     */
    private function getSystemHealth(): array
    {
        $health = [
            'overall_status' => 'healthy',
            'checks' => [],
        ];

        // Database health
        try {
            DB::connection()->getPdo();
            $health['checks']['database'] = [
                'status' => 'healthy',
                'message' => 'Database connection successful',
                'response_time' => $this->measureDatabaseResponseTime(),
            ];
        } catch (\Exception $e) {
            $health['checks']['database'] = [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
            $health['overall_status'] = 'unhealthy';
        }

        // Cache health
        try {
            Cache::put('health_check', 'test', 10);
            $value = Cache::get('health_check');
            
            $health['checks']['cache'] = [
                'status' => $value === 'test' ? 'healthy' : 'unhealthy',
                'message' => $value === 'test' ? 'Cache working properly' : 'Cache not working',
            ];
        } catch (\Exception $e) {
            $health['checks']['cache'] = [
                'status' => 'unhealthy',
                'message' => 'Cache error: ' . $e->getMessage(),
            ];
        }

        // Storage health
        $storageWritable = is_writable(storage_path());
        $health['checks']['storage'] = [
            'status' => $storageWritable ? 'healthy' : 'unhealthy',
            'message' => $storageWritable ? 'Storage directory writable' : 'Storage directory not writable',
        ];

        if (!$storageWritable) {
            $health['overall_status'] = 'unhealthy';
        }

        // Queue health (if using queues)
        $health['checks']['queue'] = [
            'status' => 'healthy',
            'message' => 'Queue system operational',
            'pending_jobs' => $this->getPendingJobsCount(),
        ];

        return $health;
    }

    /**
     * Get security status.
     */
    private function getSecurityStatus(): array
    {
        return [
            'https_enabled' => request()->isSecure(),
            'debug_mode' => config('app.debug'),
            'environment' => config('app.env'),
            'failed_logins_24h' => $this->getFailedLoginsCount(),
            'blocked_ips' => $this->getBlockedIpsCount(),
            'security_headers' => $this->checkSecurityHeaders(),
            'last_security_scan' => Cache::get('last_security_scan', 'Never'),
        ];
    }

    /**
     * Get recent alerts.
     */
    private function getRecentAlerts(): array
    {
        // This would typically come from a monitoring system
        return [
            [
                'id' => 1,
                'type' => 'warning',
                'message' => 'High memory usage detected',
                'timestamp' => now()->subMinutes(15),
                'resolved' => false,
            ],
            [
                'id' => 2,
                'type' => 'info',
                'message' => 'Database optimization completed',
                'timestamp' => now()->subHours(2),
                'resolved' => true,
            ],
        ];
    }

    /**
     * Get application logs.
     */
    private function getApplicationLogs(string $level, int $limit): array
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (!file_exists($logFile)) {
            return [];
        }

        $logs = [];
        $handle = fopen($logFile, 'r');
        
        if ($handle) {
            $lines = [];
            while (($line = fgets($handle)) !== false) {
                $lines[] = $line;
            }
            fclose($handle);
            
            // Get last N lines
            $lines = array_slice($lines, -$limit);
            
            foreach ($lines as $line) {
                if ($this->matchesLogLevel($line, $level)) {
                    $logs[] = $this->parseLogLine($line);
                }
            }
        }

        return array_reverse($logs);
    }

    /**
     * Helper methods.
     */
    private function measureDatabaseResponseTime(): float
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        return round((microtime(true) - $start) * 1000, 2);
    }

    private function getPendingJobsCount(): int
    {
        try {
            return DB::table('jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getFailedLoginsCount(): int
    {
        // This would typically come from authentication logs
        return Cache::get('failed_logins_24h', 0);
    }

    private function getBlockedIpsCount(): int
    {
        return count(Cache::get('blocked_ips', []));
    }

    private function checkSecurityHeaders(): array
    {
        return [
            'x_frame_options' => true,
            'x_content_type_options' => true,
            'x_xss_protection' => true,
            'strict_transport_security' => request()->isSecure(),
        ];
    }

    private function getDatabaseStats(): array
    {
        return [
            'total_urls' => DB::table('urls')->count(),
            'total_clicks' => DB::table('analytics')->count(),
            'total_users' => DB::table('users')->count(),
            'active_users_24h' => DB::table('users')
                ->where('last_login_at', '>', now()->subDay())
                ->count(),
        ];
    }

    private function getApplicationStats(): array
    {
        return [
            'uptime' => $this->getUptime(),
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
        ];
    }

    private function getUptime(): string
    {
        // This would typically come from system monitoring
        return '5 days, 12 hours';
    }

    private function matchesLogLevel(string $line, string $level): bool
    {
        if ($level === 'all') {
            return true;
        }
        
        return str_contains(strtolower($line), strtolower($level));
    }

    private function parseLogLine(string $line): array
    {
        // Basic log parsing - would be more sophisticated in production
        preg_match('/\[(.*?)\].*?(ERROR|WARNING|INFO|DEBUG).*?:(.*?)$/', $line, $matches);
        
        return [
            'timestamp' => $matches[1] ?? '',
            'level' => $matches[2] ?? 'INFO',
            'message' => trim($matches[3] ?? $line),
        ];
    }
}
