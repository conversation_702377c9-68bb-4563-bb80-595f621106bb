@extends('layouts.modern')

@section('title', 'Dashboard - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ Auth::user()->name }}!</h1>
                <p class="text-gray-600 mt-1">Here's what's happening with your links today.</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('urls.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Create Link
                </a>
                @if(Auth::user()->subscription?->analytics)
                    <a href="{{ route('analytics.advanced.index') }}" class="btn-secondary">
                        <i class="fas fa-chart-line mr-2"></i>Advanced Analytics
                    </a>
                @else
                    <a href="{{ route('subscriptions.index') }}" class="btn-outline">
                        <i class="fas fa-crown mr-2"></i>Upgrade
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total URLs -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-link text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Links</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_urls']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Clicks -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-mouse-pointer text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Clicks</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_clicks']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- This Month -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">This Month</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['clicks_this_month']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Links -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Links</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['active_urls']) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Clicks Chart -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Clicks Over Time</h3>
            </div>
            <div class="card-body">
                <canvas id="clicksChart" height="300"></canvas>
            </div>
        </div>

        <!-- Top Countries -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Top Countries</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    @foreach($topCountries as $country)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                <span class="text-xs font-medium text-gray-600">{{ $country->country }}</span>
                            </div>
                            <span class="font-medium text-gray-900">{{ $country->country_name ?? $country->country }}</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ ($country->clicks / $topCountries->first()->clicks) * 100 }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-600">{{ number_format($country->clicks) }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Links -->
    <div class="mb-6">
        <!-- Recent Links -->
        <div>
            <div class="card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Links</h3>
                        <a href="{{ route('urls.manage') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Link</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentUrls as $url)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $url->title ?: 'Untitled' }}
                                            </div>
                                            <div class="text-sm text-gray-500 truncate max-w-xs">
                                                {{ $url->original_url }}
                                            </div>
                                            <div class="text-xs text-blue-600 font-mono">
                                                {{ $url->short_url }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-gray-900">{{ number_format($url->click_count) }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $url->created_at->diffForHumans() }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ $url->short_url }}" target="_blank" class="text-blue-600 hover:text-blue-700">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                            <button onclick="copyToClipboard('{{ $url->short_url }}')" class="text-gray-600 hover:text-gray-700">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <a href="{{ route('urls.show', $url) }}" class="text-green-600 hover:text-green-700">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Create, Subscription, and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Quick Create -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Quick Create</h3>
            </div>
            <div class="card-body">
                <form action="{{ route('urls.store') }}" method="POST" class="space-y-4">
                    @csrf
                    <div>
                        <input type="url"
                               name="original_url"
                               placeholder="Enter URL to shorten..."
                               class="input-field"
                               required>
                    </div>
                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-magic mr-2"></i>Shorten
                    </button>
                </form>
            </div>
        </div>

        <!-- Subscription Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Subscription</h3>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-crown text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900">{{ Auth::user()->subscription->name ?? 'Free' }}</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        @if(Auth::user()->subscription && Auth::user()->subscription->price > 0)
                            ${{ number_format(Auth::user()->subscription->price / 100, 2) }}/month
                        @else
                            Free Plan
                        @endif
                    </p>

                    @if(Auth::user()->subscription)
                        <div class="mt-4 space-y-2">
                            @if(Auth::user()->subscription->url_limit)
                                <div class="flex justify-between text-xs">
                                    <span>URLs this month</span>
                                    <span>{{ $stats['urls_this_month'] }}/{{ number_format(Auth::user()->subscription->url_limit) }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(($stats['urls_this_month'] / Auth::user()->subscription->url_limit) * 100, 100) }}%"></div>
                                </div>
                            @endif
                        </div>
                    @endif

                    <a href="{{ route('subscriptions.index') }}" class="btn-outline w-full mt-4">
                        Manage Plan
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    @foreach($recentActivity as $activity)
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-{{ $activity['icon'] }} text-gray-600 text-xs"></i>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-900">{{ $activity['message'] }}</p>
                            <p class="text-xs text-gray-500">{{ $activity['time'] }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Features -->
    @if(Auth::user()->subscription && (Auth::user()->subscription->bulk_operations || Auth::user()->subscription->analytics || Auth::user()->subscription->custom_domains) || Auth::user()->role === 'admin')
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Premium Features</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                @if(Auth::user()->subscription && Auth::user()->subscription->bulk_operations)
                <a href="{{ route('bulk-operations.index') }}" class="btn-outline text-center">
                    <i class="fas fa-layer-group mr-2"></i>Bulk Operations
                </a>
                @endif

                @if(Auth::user()->subscription && Auth::user()->subscription->analytics)
                <a href="{{ route('analytics.advanced.index') }}" class="btn-outline text-center">
                    <i class="fas fa-chart-line mr-2"></i>Advanced Analytics
                </a>
                @endif

                @if(Auth::user()->subscription && Auth::user()->subscription->custom_domains)
                <a href="{{ route('custom-domains.index') }}" class="btn-outline text-center">
                    <i class="fas fa-globe mr-2"></i>Custom Domains
                </a>
                @endif

                @if(Auth::user()->role === 'admin')
                <a href="{{ route('admin.seo.index') }}" class="btn-outline text-center">
                    <i class="fas fa-search mr-2"></i>SEO Management
                </a>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
// Clicks Chart
const ctx = document.getElementById('clicksChart').getContext('2d');
const clicksChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode($chartData['labels']) !!},
        datasets: [{
            label: 'Clicks',
            data: {!! json_encode($chartData['data']) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-600');
        }, 2000);
    });
}
</script>
@endpush
