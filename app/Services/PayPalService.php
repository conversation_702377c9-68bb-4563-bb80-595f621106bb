<?php

namespace App\Services;

use PayPalCheckoutSdk\Core\PayPalHttpClient;
use PayPalCheckoutSdk\Core\SandboxEnvironment;
use PayPalCheckoutSdk\Core\ProductionEnvironment;
use PayPalCheckoutSdk\Orders\OrdersCreateRequest;
use PayPalCheckoutSdk\Orders\OrdersCaptureRequest;
use PayPalCheckoutSdk\Orders\OrdersGetRequest;
use PayPalHttp\HttpException;
use Exception;
use Illuminate\Support\Facades\Log;

class PayPalService
{
    private $client;
    private $clientId;
    private $clientSecret;
    private $mode;

    public function __construct()
    {
        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
        $this->mode = config('services.paypal.mode');

        $environment = $this->mode === 'live' 
            ? new ProductionEnvironment($this->clientId, $this->clientSecret)
            : new SandboxEnvironment($this->clientId, $this->clientSecret);

        $this->client = new PayPalHttpClient($environment);
    }

    /**
     * Create a PayPal order
     */
    public function createOrder($amount, $currency = 'USD', $description = 'Subscription Payment')
    {
        try {
            $request = new OrdersCreateRequest();
            $request->prefer('return=representation');
            $request->body = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => number_format($amount, 2, '.', '')
                        ],
                        'description' => $description
                    ]
                ],
                'application_context' => [
                    'cancel_url' => route('subscriptions.payment-cancelled'),
                    'return_url' => route('subscriptions.payment-success'),
                    'brand_name' => 'Minilink.at',
                    'locale' => 'en-US',
                    'landing_page' => 'BILLING',
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW'
                ]
            ];

            $response = $this->client->execute($request);
            
            Log::info('PayPal order created', [
                'order_id' => $response->result->id,
                'status' => $response->result->status
            ]);

            return [
                'success' => true,
                'order_id' => $response->result->id,
                'status' => $response->result->status,
                'links' => $response->result->links
            ];

        } catch (HttpException $ex) {
            Log::error('PayPal order creation failed', [
                'error' => $ex->getMessage(),
                'status_code' => $ex->statusCode
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create PayPal order',
                'details' => $ex->getMessage()
            ];
        }
    }

    /**
     * Capture a PayPal order
     */
    public function captureOrder($orderId)
    {
        try {
            $request = new OrdersCaptureRequest($orderId);
            $request->prefer('return=representation');

            $response = $this->client->execute($request);

            Log::info('PayPal order captured', [
                'order_id' => $orderId,
                'status' => $response->result->status,
                'capture_id' => $response->result->purchase_units[0]->payments->captures[0]->id ?? null
            ]);

            return [
                'success' => true,
                'order_id' => $orderId,
                'status' => $response->result->status,
                'capture_id' => $response->result->purchase_units[0]->payments->captures[0]->id ?? null,
                'amount' => $response->result->purchase_units[0]->payments->captures[0]->amount ?? null,
                'payer' => $response->result->payer ?? null
            ];

        } catch (HttpException $ex) {
            Log::error('PayPal order capture failed', [
                'order_id' => $orderId,
                'error' => $ex->getMessage(),
                'status_code' => $ex->statusCode
            ]);

            return [
                'success' => false,
                'error' => 'Failed to capture PayPal order',
                'details' => $ex->getMessage()
            ];
        }
    }

    /**
     * Get order details
     */
    public function getOrder($orderId)
    {
        try {
            $request = new OrdersGetRequest($orderId);
            $response = $this->client->execute($request);

            return [
                'success' => true,
                'order' => $response->result
            ];

        } catch (HttpException $ex) {
            Log::error('PayPal get order failed', [
                'order_id' => $orderId,
                'error' => $ex->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get PayPal order',
                'details' => $ex->getMessage()
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($headers, $body)
    {
        try {
            $webhookId = config('services.paypal.webhook_id');
            
            if (!$webhookId) {
                Log::warning('PayPal webhook ID not configured');
                return false;
            }

            // PayPal webhook verification logic
            $expectedSignature = $headers['PAYPAL-TRANSMISSION-SIG'] ?? '';
            $certId = $headers['PAYPAL-CERT-ID'] ?? '';
            $timestamp = $headers['PAYPAL-TRANSMISSION-TIME'] ?? '';
            $authAlgo = $headers['PAYPAL-AUTH-ALGO'] ?? '';

            // For now, we'll log the webhook and return true
            // In production, implement proper signature verification
            Log::info('PayPal webhook received', [
                'cert_id' => $certId,
                'timestamp' => $timestamp,
                'auth_algo' => $authAlgo,
                'body_preview' => substr($body, 0, 200)
            ]);

            return true;

        } catch (Exception $ex) {
            Log::error('PayPal webhook verification failed', [
                'error' => $ex->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Process webhook event
     */
    public function processWebhookEvent($eventData)
    {
        try {
            $eventType = $eventData['event_type'] ?? '';
            
            Log::info('Processing PayPal webhook event', [
                'event_type' => $eventType,
                'event_id' => $eventData['id'] ?? ''
            ]);

            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentCaptureCompleted($eventData);
                
                case 'PAYMENT.CAPTURE.DENIED':
                    return $this->handlePaymentCaptureDenied($eventData);
                
                case 'BILLING.SUBSCRIPTION.CREATED':
                    return $this->handleSubscriptionCreated($eventData);
                
                case 'BILLING.SUBSCRIPTION.CANCELLED':
                    return $this->handleSubscriptionCancelled($eventData);
                
                default:
                    Log::info('Unhandled PayPal webhook event type', ['event_type' => $eventType]);
                    return ['success' => true, 'message' => 'Event type not handled'];
            }

        } catch (Exception $ex) {
            Log::error('PayPal webhook processing failed', [
                'error' => $ex->getMessage(),
                'event_data' => $eventData
            ]);

            return ['success' => false, 'error' => $ex->getMessage()];
        }
    }

    private function handlePaymentCaptureCompleted($eventData)
    {
        // Handle successful payment capture
        $captureId = $eventData['resource']['id'] ?? '';
        $amount = $eventData['resource']['amount'] ?? [];
        
        Log::info('Payment capture completed', [
            'capture_id' => $captureId,
            'amount' => $amount
        ]);

        return ['success' => true, 'message' => 'Payment capture completed'];
    }

    private function handlePaymentCaptureDenied($eventData)
    {
        // Handle denied payment
        $captureId = $eventData['resource']['id'] ?? '';
        
        Log::warning('Payment capture denied', [
            'capture_id' => $captureId
        ]);

        return ['success' => true, 'message' => 'Payment capture denied'];
    }

    private function handleSubscriptionCreated($eventData)
    {
        // Handle subscription creation
        $subscriptionId = $eventData['resource']['id'] ?? '';
        
        Log::info('Subscription created', [
            'subscription_id' => $subscriptionId
        ]);

        return ['success' => true, 'message' => 'Subscription created'];
    }

    private function handleSubscriptionCancelled($eventData)
    {
        // Handle subscription cancellation
        $subscriptionId = $eventData['resource']['id'] ?? '';
        
        Log::info('Subscription cancelled', [
            'subscription_id' => $subscriptionId
        ]);

        return ['success' => true, 'message' => 'Subscription cancelled'];
    }
}
