<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Models\Analytics;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     */
    public function index(): View
    {
        $user = Auth::user();

        // Calculate statistics
        $stats = [
            'total_urls' => $user->urls()->count(),
            'total_clicks' => $user->urls()->sum('click_count'),
            'clicks_this_month' => Analytics::whereHas('url', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->whereMonth('clicked_at', now()->month)
            ->whereYear('clicked_at', now()->year)
            ->count(),
            'active_urls' => $user->urls()->where('is_active', true)->count(),
            'urls_this_month' => $user->urls()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        // Get recent URLs
        $recentUrls = $user->urls()
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent analytics for chart
        $recentAnalytics = Analytics::whereHas('url', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->where('clicked_at', '>=', now()->subDays(30))
        ->selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        // Prepare chart data
        $chartLabels = [];
        $chartData = [];

        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $chartLabels[] = now()->subDays($i)->format('M j');

            $dayClicks = $recentAnalytics->where('date', $date)->first();
            $chartData[] = $dayClicks ? $dayClicks->clicks : 0;
        }

        $chartData = [
            'labels' => $chartLabels,
            'data' => $chartData,
        ];

        // Get top countries
        $topCountries = Analytics::whereHas('url', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->selectRaw('country, country_name, COUNT(*) as clicks')
        ->whereNotNull('country')
        ->groupBy('country', 'country_name')
        ->orderBy('clicks', 'desc')
        ->take(5)
        ->get();

        // Recent activity
        $recentActivity = [
            [
                'icon' => 'link',
                'message' => 'Created new short link',
                'time' => '2 hours ago',
            ],
            [
                'icon' => 'mouse-pointer',
                'message' => '50 new clicks today',
                'time' => '4 hours ago',
            ],
            [
                'icon' => 'chart-bar',
                'message' => 'Weekly report generated',
                'time' => '1 day ago',
            ],
        ];

        return view('dashboard.modern', compact(
            'stats',
            'recentUrls',
            'chartData',
            'topCountries',
            'recentActivity'
        ));
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats($user): array
    {
        $userUrls = $user->urls();

        return [
            'total_urls' => $userUrls->count(),
            'total_clicks' => Analytics::whereIn('url_id', $userUrls->pluck('id'))->count(),
            'urls_today' => $userUrls->whereDate('created_at', today())->count(),
            'clicks_today' => Analytics::whereIn('url_id', $userUrls->pluck('id'))
                ->whereDate('clicked_at', today())->count(),
            'active_urls' => $userUrls->where('is_active', true)->count(),
            'expired_urls' => $userUrls->where('expires_at', '<', now())->count(),
        ];
    }

    /**
     * Get recent activity.
     */
    private function getRecentActivity($user): array
    {
        $recentUrls = $user->urls()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentClicks = Analytics::whereIn('url_id', $user->urls()->pluck('id'))
            ->with('url')
            ->orderBy('clicked_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'recent_urls' => $recentUrls,
            'recent_clicks' => $recentClicks,
        ];
    }

    /**
     * Show analytics overview.
     */
    public function analytics(Request $request): View
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');

        // Get analytics data
        $analyticsData = $this->getAnalyticsData($user, $period);

        return view('dashboard.analytics', compact('analyticsData', 'period'));
    }

    /**
     * Get analytics data for the user.
     */
    private function getAnalyticsData($user, string $period): array
    {
        $userUrlIds = $user->urls()->pluck('id');
        $query = Analytics::whereIn('url_id', $userUrlIds);

        // Apply period filter
        switch ($period) {
            case 'today':
                $query->whereDate('clicked_at', today());
                break;
            case 'week':
                $query->whereBetween('clicked_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('clicked_at', now()->month);
                break;
            case 'year':
                $query->whereYear('clicked_at', now()->year);
                break;
        }

        $analytics = $query->get();

        return [
            'total_clicks' => $analytics->count(),
            'unique_visitors' => $analytics->unique('ip_address')->count(),
            'top_urls' => $this->getTopUrls($user, $period),
            'countries' => $this->getTopCountries($analytics),
            'devices' => $this->getDeviceStats($analytics),
            'browsers' => $this->getBrowserStats($analytics),
            'daily_clicks' => $this->getDailyClicks($user, $period),
        ];
    }

    /**
     * Get top performing URLs.
     */
    private function getTopUrls($user, string $period): array
    {
        $query = $user->urls()
            ->withCount(['analytics' => function($query) use ($period) {
                switch ($period) {
                    case 'today':
                        $query->whereDate('clicked_at', today());
                        break;
                    case 'week':
                        $query->whereBetween('clicked_at', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'month':
                        $query->whereMonth('clicked_at', now()->month);
                        break;
                    case 'year':
                        $query->whereYear('clicked_at', now()->year);
                        break;
                }
            }]);

        return $query->orderBy('analytics_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get top countries.
     */
    private function getTopCountries($analytics): array
    {
        return $analytics->whereNotNull('country_name')
            ->groupBy('country_name')
            ->map(function ($group) use ($analytics) {
                return [
                    'country' => $group->first()->country_name,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get device statistics.
     */
    private function getDeviceStats($analytics): array
    {
        return $analytics->whereNotNull('device_type')
            ->groupBy('device_type')
            ->map(function ($group) use ($analytics) {
                return [
                    'device' => $group->first()->device_type,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->values()
            ->toArray();
    }

    /**
     * Get browser statistics.
     */
    private function getBrowserStats($analytics): array
    {
        return $analytics->whereNotNull('browser')
            ->groupBy('browser')
            ->map(function ($group) use ($analytics) {
                return [
                    'browser' => $group->first()->browser,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get daily clicks for chart.
     */
    private function getDailyClicks($user, string $period): array
    {
        $days = match($period) {
            'week' => 7,
            'month' => 30,
            'year' => 365,
            default => 30,
        };

        $startDate = now()->subDays($days);
        $userUrlIds = $user->urls()->pluck('id');

        $clicks = Analytics::whereIn('url_id', $userUrlIds)
            ->where('clicked_at', '>=', $startDate)
            ->selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $result = [];
        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $result[] = [
                'date' => $date,
                'clicks' => $clicks->get($date)?->clicks ?? 0,
            ];
        }

        return $result;
    }
}
