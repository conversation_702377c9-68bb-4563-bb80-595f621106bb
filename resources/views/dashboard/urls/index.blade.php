@extends('layouts.modern')

@section('title', 'Manage URLs - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-link mr-3 text-blue-600"></i>
                    Manage URLs
                </h1>
                <p class="text-gray-600 mt-1">Organize and manage all your shortened URLs</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <a href="{{ route('dashboard') }}" class="btn-outline">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <a href="{{ route('home') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Create New URL
                </a>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-filter mr-2 text-purple-600"></i>
                Filters & Search
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('dashboard.urls.index') }}" id="filterForm">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search URLs</label>
                        <div class="relative">
                            <input type="text"
                                   class="form-input pl-10"
                                   id="search"
                                   name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Search by title, URL, or code...">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            @if(request('search'))
                                <button type="button"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        onclick="clearSearch()">
                                    <i class="fas fa-times text-gray-400 hover:text-gray-600"></i>
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                                Active ({{ $filterCounts['active'] }})
                            </option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                                Inactive ({{ $filterCounts['inactive'] }})
                            </option>
                            <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>
                                Expired ({{ $filterCounts['expired'] }})
                            </option>
                            <option value="password_protected" {{ request('status') === 'password_protected' ? 'selected' : '' }}>
                                Password Protected ({{ $filterCounts['password_protected'] }})
                            </option>
                        </select>
                    </div>

                    <!-- Sort Options -->
                    <div>
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <div class="space-y-2">
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>Date Created</option>
                                <option value="title" {{ request('sort_by') === 'title' ? 'selected' : '' }}>Title</option>
                                <option value="click_count" {{ request('sort_by') === 'click_count' ? 'selected' : '' }}>Clicks</option>
                                <option value="original_url" {{ request('sort_by') === 'original_url' ? 'selected' : '' }}>Original URL</option>
                            </select>
                            <select class="form-select" name="sort_order">
                                <option value="desc" {{ request('sort_order') === 'desc' ? 'selected' : '' }}>Descending</option>
                                <option value="asc" {{ request('sort_order') === 'asc' ? 'selected' : '' }}>Ascending</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input type="date"
                               class="form-input"
                               id="date_from"
                               name="date_from"
                               value="{{ request('date_from') }}">
                    </div>
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input type="date"
                               class="form-input"
                               id="date_to"
                               name="date_to"
                               value="{{ request('date_to') }}">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3 mt-6 pt-6 border-t border-gray-200">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search mr-2"></i>Apply Filters
                    </button>
                    <a href="{{ route('dashboard.urls.index') }}" class="btn-outline">
                        <i class="fas fa-undo mr-2"></i>Clear All
                    </a>
                    <button type="button" class="btn-outline" onclick="exportSelected()">
                        <i class="fas fa-download mr-2"></i>Export Selected
                    </button>

                    <!-- Bulk Actions Dropdown -->
                    <div class="relative inline-block text-left">
                        <button type="button"
                                class="btn-outline dropdown-toggle"
                                id="bulk-actions-menu"
                                onclick="toggleDropdown('bulk-actions')">
                            <i class="fas fa-cog mr-2"></i>Bulk Actions
                        </button>
                        <div class="dropdown-menu hidden" id="bulk-actions">
                            <a href="#" class="dropdown-item" onclick="bulkAction('activate')">
                                <i class="fas fa-check mr-2 text-green-500"></i>Activate Selected
                            </a>
                            <a href="#" class="dropdown-item" onclick="bulkAction('deactivate')">
                                <i class="fas fa-pause mr-2 text-yellow-500"></i>Deactivate Selected
                            </a>
                            <div class="border-t border-gray-100 my-1"></div>
                            <a href="#" class="dropdown-item text-red-600" onclick="bulkAction('delete')">
                                <i class="fas fa-trash mr-2"></i>Delete Selected
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- URLs Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-list mr-2 text-blue-600"></i>
                    Your URLs
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ $urls->total() }}
                    </span>
                </h3>
                <div class="flex items-center space-x-2">
                    <input type="checkbox" class="form-checkbox" id="selectAll">
                    <label for="selectAll" class="text-sm text-gray-700">Select All</label>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($urls->count() > 0)
                <!-- Modern Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                    <input type="checkbox" class="form-checkbox" id="selectAllHeader">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL Details</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Short Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($urls as $url)
                            <tr class="hover:bg-gray-50 transition-colors duration-200 {{ $url->isExpired() ? 'bg-yellow-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="form-checkbox url-checkbox" value="{{ $url->id }}">
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1 min-w-0">
                                            @if($url->title)
                                                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $url->title }}</h4>
                                            @endif
                                            <p class="text-sm text-gray-500 truncate">{{ Str::limit($url->original_url, 60) }}</p>
                                            @if($url->description)
                                                <p class="text-xs text-gray-400 mt-1">{{ Str::limit($url->description, 80) }}</p>
                                            @endif
                                        </div>
                                        <div class="flex items-center space-x-1 ml-4">
                                            @if($url->isPasswordProtected())
                                                <i class="fas fa-lock text-yellow-500" title="Password Protected"></i>
                                            @endif
                                            @if($url->qr_code_path)
                                                <i class="fas fa-qrcode text-blue-500" title="Has QR Code"></i>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        <input type="text"
                                               class="form-input text-sm w-32"
                                               value="{{ $url->custom_alias ?: $url->short_code }}"
                                               readonly>
                                        <button class="inline-flex items-center justify-center w-8 h-8 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200"
                                                onclick="copyToClipboard('{{ $url->short_url }}')"
                                                title="Copy URL">
                                            <i class="fas fa-copy text-sm"></i>
                                        </button>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <div class="text-sm font-medium text-blue-600">{{ number_format($url->click_count) }}</div>
                                    <div class="text-xs text-gray-500">clicks</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($url->is_active)
                                        @if($url->isExpired())
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-clock mr-1"></i>Expired
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle mr-1"></i>Active
                                            </span>
                                        @endif
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ $url->created_at->format('M j, Y') }}</div>
                                    <div class="text-xs">{{ $url->created_at->diffForHumans() }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('urls.show', $url) }}"
                                           class="inline-flex items-center justify-center w-8 h-8 rounded-md bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200"
                                           title="Analytics">
                                            <i class="fas fa-chart-bar text-sm"></i>
                                        </a>
                                        <a href="{{ route('urls.edit', $url) }}"
                                           class="inline-flex items-center justify-center w-8 h-8 rounded-md bg-yellow-100 text-yellow-600 hover:bg-yellow-200 transition-colors duration-200"
                                           title="Edit">
                                            <i class="fas fa-edit text-sm"></i>
                                        </a>
                                        <button type="button"
                                                class="inline-flex items-center justify-center w-8 h-8 rounded-md bg-red-100 text-red-600 hover:bg-red-200 transition-colors duration-200"
                                                onclick="deleteUrl({{ $url->id }})"
                                                title="Delete">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="flex items-center justify-between px-6 py-4 border-t border-gray-200">
                    <div class="text-sm text-gray-700">
                        Showing {{ $urls->firstItem() }} to {{ $urls->lastItem() }} of {{ $urls->total() }} results
                    </div>
                    <div class="pagination-wrapper">
                        {{ $urls->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-16">
                    <i class="fas fa-link text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No URLs found</h3>
                    <p class="text-gray-500 mb-6">
                        @if(request()->hasAny(['search', 'status', 'date_from', 'date_to']))
                            Try adjusting your filters or <a href="{{ route('dashboard.urls.index') }}" class="text-blue-600 hover:text-blue-500">clear all filters</a>.
                        @else
                            Create your first shortened URL to get started!
                        @endif
                    </p>
                    @if(!request()->hasAny(['search', 'status', 'date_from', 'date_to']))
                        <a href="{{ route('home') }}" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Create Your First URL
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-submit form on filter changes
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filterForm');
    const autoSubmitElements = ['status', 'sort_by', 'sort_order'];

    autoSubmitElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', () => form.submit());
        }
    });
});

// Dropdown functionality
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    dropdown.classList.toggle('hidden');

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest(`#${dropdownId}`) && !event.target.closest(`#${dropdownId}-menu`)) {
            dropdown.classList.add('hidden');
        }
    });
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.url-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.url-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
    document.getElementById('selectAll').checked = this.checked;
});

// Clear search
function clearSearch() {
    document.getElementById('search').value = '';
    document.getElementById('filterForm').submit();
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check text-sm"></i>';
        btn.classList.remove('bg-gray-100', 'text-gray-600', 'hover:bg-gray-200');
        btn.classList.add('bg-green-100', 'text-green-600');

        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('bg-green-100', 'text-green-600');
            btn.classList.add('bg-gray-100', 'text-gray-600', 'hover:bg-gray-200');
        }, 2000);
    });
}

// Get selected URL IDs
function getSelectedUrls() {
    const checkboxes = document.querySelectorAll('.url-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Bulk actions
function bulkAction(action) {
    const selectedUrls = getSelectedUrls();

    if (selectedUrls.length === 0) {
        alert('Please select at least one URL.');
        return;
    }

    const actionText = action === 'delete' ? 'delete' : action;
    if (!confirm(`Are you sure you want to ${actionText} ${selectedUrls.length} URL(s)?`)) {
        return;
    }

    fetch('{{ route("dashboard.urls.bulk-action") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: action,
            url_ids: selectedUrls
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
    });
}

// Export selected URLs
function exportSelected() {
    const selectedUrls = getSelectedUrls();

    if (selectedUrls.length === 0) {
        alert('Please select at least one URL to export.');
        return;
    }

    fetch('{{ route("dashboard.urls.bulk-action") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: 'export',
            url_ids: selectedUrls
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Create a temporary link to download the file
            const link = document.createElement('a');
            link.href = data.download_url;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert(data.message);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
    });
}

// Delete single URL
function deleteUrl(urlId) {
    if (!confirm('Are you sure you want to delete this URL? This action cannot be undone.')) {
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/urls/${urlId}`;

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'DELETE';

    form.appendChild(csrfToken);
    form.appendChild(methodField);
    document.body.appendChild(form);
    form.submit();
}
</script>
@endpush
