@extends('layouts.dashboard-template')

@section('page_title', 'Add Custom Domain - Minilink.at')
@section('page_heading')
    <i class="fas fa-plus mr-3 text-blue-600"></i>Add Custom Domain
@endsection
@section('page_description', 'Add your own domain for branded short URLs')

@section('page_actions')
    <a href="{{ route('custom-domains.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Domains
    </a>
    <a href="{{ route('dashboard') }}" class="btn-outline">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
    </a>
@endsection

@section('main_content')

    <div class="max-w-2xl mx-auto">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-globe mr-2 text-blue-600"></i>
                    Domain Information
                </h3>
            </div>
            <div class="card-body">
                    <form action="{{ route('custom-domains.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <div>
                        <label for="domain" class="block text-sm font-medium text-gray-700 mb-2">Domain Name</label>
                        <input type="text"
                               class="input-field @error('domain') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               id="domain"
                               name="domain"
                               value="{{ old('domain') }}"
                               placeholder="example.com"
                               required>
                        @error('domain')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            Enter your domain name without protocol (http/https). Examples: example.com, short.example.com
                        </p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-blue-900 mb-3 flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            What happens next?
                        </h4>
                        <ol class="text-blue-800 space-y-2">
                            <li class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
                                Your domain will be added to your account
                            </li>
                            <li class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
                                You'll receive DNS configuration instructions
                            </li>
                            <li class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
                                Configure your DNS records as instructed
                            </li>
                            <li class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
                                Verify domain ownership
                            </li>
                            <li class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">5</span>
                                Start creating branded short URLs!
                            </li>
                        </ol>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('custom-domains.index') }}" class="btn-secondary">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Add Domain
                        </button>
                    </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Requirements and Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-list-check mr-2 text-green-600"></i>
                        Requirements
                    </h3>
                </div>
                <div class="card-body">
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-3 mt-1"></i>
                            <div>
                                <strong class="text-gray-900">Domain Ownership:</strong>
                                <span class="text-gray-600">You must own the domain</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-3 mt-1"></i>
                            <div>
                                <strong class="text-gray-900">DNS Access:</strong>
                                <span class="text-gray-600">Ability to modify DNS records</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-3 mt-1"></i>
                            <div>
                                <strong class="text-gray-900">Valid Domain:</strong>
                                <span class="text-gray-600">Must be a valid, registered domain</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-3 mt-1"></i>
                            <div>
                                <strong class="text-gray-900">No Conflicts:</strong>
                                <span class="text-gray-600">Domain not already in use</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-cog mr-2 text-blue-600"></i>
                        DNS Configuration
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-gray-600 mb-4">After adding your domain, you'll need to configure these DNS records:</p>

                    <div class="space-y-4">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">TXT Record (for verification):</h4>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                                <code class="text-sm text-gray-800">_minilink-verification.yourdomain.com</code>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">CNAME Record (for routing):</h4>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                                <code class="text-sm text-gray-800">yourdomain.com → {{ parse_url(config('app.url'), PHP_URL_HOST) }}</code>
                            </div>
                        </div>
                    </div>

                    <p class="text-sm text-gray-500 mt-4">
                        Detailed instructions will be provided after adding your domain.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Limits -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6><i class="fas fa-info-circle"></i> Subscription Limits</h6>
                            <p class="mb-0">
                                You can add up to <strong>{{ Auth::user()->subscription?->custom_domains_limit ?? 1 }}</strong> custom domains.
                                Currently using <strong>{{ Auth::user()->customDomains()->count() }}</strong> domains.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            @if(Auth::user()->customDomains()->count() >= (Auth::user()->subscription?->custom_domains_limit ?? 1))
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-up"></i> Upgrade Plan
                                </a>
                            @else
                                <span class="badge bg-success fs-6">
                                    {{ (Auth::user()->subscription?->custom_domains_limit ?? 1) - Auth::user()->customDomains()->count() }} remaining
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const domainInput = document.getElementById('domain');
    
    // Auto-format domain input
    domainInput.addEventListener('input', function() {
        let value = this.value.toLowerCase();
        
        // Remove protocol if present
        value = value.replace(/^https?:\/\//, '');
        
        // Remove trailing slash
        value = value.replace(/\/$/, '');
        
        // Remove www. prefix (optional)
        // value = value.replace(/^www\./, '');
        
        this.value = value;
    });
    
    // Validate domain format
    domainInput.addEventListener('blur', function() {
        const domain = this.value;
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
        
        if (domain && !domainRegex.test(domain)) {
            this.classList.add('is-invalid');
            
            // Add or update error message
            let feedback = this.parentNode.querySelector('.invalid-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                this.parentNode.appendChild(feedback);
            }
            feedback.textContent = 'Please enter a valid domain name (e.g., example.com)';
        } else {
            this.classList.remove('is-invalid');
        }
    });
});
</script>
@endpush
