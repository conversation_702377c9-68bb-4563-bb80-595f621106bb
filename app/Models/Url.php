<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Url extends Model
{
    use HasFactory;

    protected $fillable = [
        'original_url',
        'short_code',
        'custom_alias',
        'user_id',
        'team_id',
        'custom_domain_id',
        'title',
        'description',
        'qr_code_path',
        'click_count',
        'is_active',
        'expires_at',
        'password',
        'metadata',
    ];

    protected $hidden = [
        'password',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'expires_at' => 'datetime',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the user that owns the URL.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the custom domain for the URL.
     */
    public function customDomain(): BelongsTo
    {
        return $this->belongsTo(CustomDomain::class);
    }

    /**
     * Get the team for the URL.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the analytics for the URL.
     */
    public function analytics(): HasMany
    {
        return $this->hasMany(Analytics::class);
    }

    /**
     * Generate a unique short code.
     */
    public static function generateShortCode(): string
    {
        do {
            $shortCode = Str::random(6);
        } while (self::where('short_code', $shortCode)->exists());

        return $shortCode;
    }

    /**
     * Get the full short URL.
     */
    public function getShortUrlAttribute(): string
    {
        $domain = $this->customDomain ? $this->customDomain->domain : config('app.url');
        $code = $this->custom_alias ?: $this->short_code;

        return rtrim($domain, '/') . '/' . $code;
    }

    /**
     * Check if URL is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if URL is password protected.
     */
    public function isPasswordProtected(): bool
    {
        return !empty($this->password);
    }

    /**
     * Increment click count.
     */
    public function incrementClicks(): void
    {
        $this->increment('click_count');
    }
}
