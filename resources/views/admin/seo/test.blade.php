@extends('layouts.dashboard-template')

@section('page_title', 'SEO Configuration Test - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-vial mr-3 text-blue-600"></i>SEO Configuration Test
@endsection
@section('page_description', 'Test and validate your SEO configuration settings')

@section('page_actions')
    <a href="{{ route('admin.seo.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to SEO
    </a>
    <a href="{{ route('admin.seo.audit') }}" class="btn-outline">
        <i class="fas fa-chart-line mr-2"></i>SEO Audit
    </a>
    <button onclick="window.location.reload()" class="btn-primary">
        <i class="fas fa-sync mr-2"></i>Refresh Test
    </button>
@endsection

@section('main_content')
    <!-- Test Overview -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">SEO Configuration Test Results</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Meta Tags Status -->
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl mb-2">
                        @if($testResults['meta_tags']['status'] === 'success')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @elseif($testResults['meta_tags']['status'] === 'warning')
                            <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Meta Tags</div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ $testResults['meta_tags']['count'] ?? 0 }} configured
                    </div>
                </div>

                <!-- Social Media Status -->
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl mb-2">
                        @if($testResults['social_media']['status'] === 'success')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @elseif($testResults['social_media']['status'] === 'warning')
                            <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Social Media</div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ $testResults['social_media']['count'] ?? 0 }} configured
                    </div>
                </div>

                <!-- Analytics Status -->
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl mb-2">
                        @if($testResults['analytics_codes']['status'] === 'success')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @elseif($testResults['analytics_codes']['status'] === 'warning')
                            <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Analytics</div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ $testResults['analytics_codes']['count'] ?? 0 }} configured
                    </div>
                </div>

                <!-- Verification Status -->
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl mb-2">
                        @if($testResults['verification_tags']['status'] === 'success')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @elseif($testResults['verification_tags']['status'] === 'warning')
                            <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Verification</div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ $testResults['verification_tags']['count'] ?? 0 }} configured
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Meta Tags Test -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-tags mr-2 text-blue-500"></i>Meta Tags Test
            </h3>
        </div>
        <div class="card-body">
            @if(isset($testResults['meta_tags']['tags']) && count($testResults['meta_tags']['tags']) > 0)
                <div class="space-y-4">
                    @foreach($testResults['meta_tags']['tags'] as $tag => $content)
                        <div class="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ ucwords(str_replace('_', ' ', $tag)) }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ $content }}</div>
                            </div>
                            <div class="ml-4">
                                <i class="fas fa-check text-green-500"></i>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Meta Tags Configured</h4>
                    <p class="text-gray-600 mb-4">Configure meta tags to improve search engine optimization.</p>
                    <a href="{{ route('admin.seo.meta') }}" class="btn-primary">
                        <i class="fas fa-tags mr-2"></i>Configure Meta Tags
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Social Media Test -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-share-alt mr-2 text-green-500"></i>Social Media Test
            </h3>
        </div>
        <div class="card-body">
            @if(isset($testResults['social_media']['tags']) && count($testResults['social_media']['tags']) > 0)
                <div class="space-y-4">
                    @foreach($testResults['social_media']['tags'] as $tag => $content)
                        <div class="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ ucwords(str_replace(['_', 'og:', 'twitter:'], [' ', 'Open Graph ', 'Twitter '], $tag)) }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ $content }}</div>
                            </div>
                            <div class="ml-4">
                                <i class="fas fa-check text-green-500"></i>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Social Media Tags Configured</h4>
                    <p class="text-gray-600 mb-4">Configure Open Graph and Twitter Cards for better social sharing.</p>
                    <a href="{{ route('admin.seo.social') }}" class="btn-primary">
                        <i class="fas fa-share-alt mr-2"></i>Configure Social Media
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Analytics Test -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chart-bar mr-2 text-purple-500"></i>Analytics Test
            </h3>
        </div>
        <div class="card-body">
            @if(isset($testResults['analytics_codes']['codes']) && count($testResults['analytics_codes']['codes']) > 0)
                <div class="space-y-4">
                    @foreach($testResults['analytics_codes']['codes'] as $platform => $code)
                        <div class="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ ucwords(str_replace('_', ' ', $platform)) }}</div>
                                <div class="text-sm text-gray-600 mt-1 font-mono">{{ Str::limit($code, 50) }}</div>
                            </div>
                            <div class="ml-4">
                                <i class="fas fa-check text-green-500"></i>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Analytics Configured</h4>
                    <p class="text-gray-600 mb-4">Configure analytics tracking to monitor your website performance.</p>
                    <a href="{{ route('admin.seo.analytics') }}" class="btn-primary">
                        <i class="fas fa-chart-bar mr-2"></i>Configure Analytics
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Verification Test -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-shield-check mr-2 text-orange-500"></i>Search Engine Verification Test
            </h3>
        </div>
        <div class="card-body">
            @if(isset($testResults['verification_tags']['tags']) && count($testResults['verification_tags']['tags']) > 0)
                <div class="space-y-4">
                    @foreach($testResults['verification_tags']['tags'] as $platform => $code)
                        <div class="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ ucwords(str_replace('_', ' ', $platform)) }} Verification</div>
                                <div class="text-sm text-gray-600 mt-1 font-mono">{{ Str::limit($code, 50) }}</div>
                            </div>
                            <div class="ml-4">
                                <i class="fas fa-check text-green-500"></i>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Search Engine Verification</h4>
                    <p class="text-gray-600 mb-4">Verify your website with search engines for better indexing.</p>
                    <a href="{{ route('admin.seo.verification') }}" class="btn-primary">
                        <i class="fas fa-shield-check mr-2"></i>Configure Verification
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
