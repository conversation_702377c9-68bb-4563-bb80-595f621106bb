<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Url;
use App\Models\Subscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UrlShorteningTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create default subscription
        Subscription::factory()->create([
            'name' => 'Free',
            'price' => 0,
            'url_limit' => 50,
            'click_limit' => 1000,
            'is_active' => true,
        ]);
    }

    public function test_guest_can_create_short_url(): void
    {
        $originalUrl = 'https://example.com';

        $response = $this->post('/', [
            'original_url' => $originalUrl,
            'title' => 'Test URL',
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('urls', [
            'original_url' => $originalUrl,
            'title' => 'Test URL',
            'user_id' => null,
        ]);
    }

    public function test_authenticated_user_can_create_short_url(): void
    {
        $user = User::factory()->create();
        $originalUrl = 'https://example.com';

        $response = $this->actingAs($user)->post('/', [
            'original_url' => $originalUrl,
            'title' => 'Test URL',
            'description' => 'Test Description',
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('urls', [
            'original_url' => $originalUrl,
            'title' => 'Test URL',
            'description' => 'Test Description',
            'user_id' => $user->id,
        ]);
    }

    public function test_short_url_redirects_to_original(): void
    {
        $url = Url::factory()->create([
            'original_url' => 'https://example.com',
            'short_code' => 'test123',
        ]);

        $response = $this->get('/test123');

        $response->assertRedirect('https://example.com');

        // Check that analytics was recorded
        $this->assertDatabaseHas('analytics', [
            'url_id' => $url->id,
        ]);
    }

    public function test_invalid_url_returns_validation_error(): void
    {
        $response = $this->post('/', [
            'original_url' => 'not-a-valid-url',
        ]);

        $response->assertSessionHasErrors('original_url');
    }

    public function test_custom_alias_can_be_used(): void
    {
        $user = User::factory()->create();
        $customAlias = 'my-custom-link';

        $response = $this->actingAs($user)->post('/', [
            'original_url' => 'https://example.com',
            'custom_alias' => $customAlias,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('urls', [
            'short_code' => $customAlias,
            'custom_alias' => $customAlias,
        ]);
    }

    public function test_duplicate_custom_alias_returns_error(): void
    {
        $user = User::factory()->create();
        $customAlias = 'duplicate-alias';

        // Create first URL with custom alias
        Url::factory()->create([
            'short_code' => $customAlias,
            'custom_alias' => $customAlias,
        ]);

        // Try to create second URL with same alias
        $response = $this->actingAs($user)->post('/', [
            'original_url' => 'https://example.com',
            'custom_alias' => $customAlias,
        ]);

        $response->assertSessionHasErrors('custom_alias');
    }

    public function test_expired_url_shows_expired_page(): void
    {
        $url = Url::factory()->create([
            'short_code' => 'expired123',
            'expires_at' => now()->subDay(),
        ]);

        $response = $this->get('/expired123');

        $response->assertStatus(410); // Gone
    }

    public function test_inactive_url_shows_not_found(): void
    {
        $url = Url::factory()->create([
            'short_code' => 'inactive123',
            'is_active' => false,
        ]);

        $response = $this->get('/inactive123');

        $response->assertStatus(404);
    }

    public function test_password_protected_url_requires_password(): void
    {
        $url = Url::factory()->create([
            'short_code' => 'protected123',
            'password' => bcrypt('secret'),
        ]);

        $response = $this->get('/protected123');

        $response->assertStatus(200);
        $response->assertSee('Password Required');
    }

    public function test_correct_password_allows_access(): void
    {
        $url = Url::factory()->create([
            'short_code' => 'protected123',
            'password' => bcrypt('secret'),
            'original_url' => 'https://example.com',
        ]);

        $response = $this->post('/protected123', [
            'password' => 'secret',
        ]);

        $response->assertRedirect('https://example.com');
    }

    public function test_url_click_count_increments(): void
    {
        $url = Url::factory()->create([
            'short_code' => 'counter123',
            'click_count' => 0,
        ]);

        $this->get('/counter123');

        $url->refresh();
        $this->assertEquals(1, $url->click_count);
    }
}
