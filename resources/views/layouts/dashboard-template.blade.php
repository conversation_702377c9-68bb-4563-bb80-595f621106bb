@extends('layouts.modern')

@section('title')
@hasSection('page_title')
@yield('page_title')
@else
Minilink.at
@endif
@endsection

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">@yield('page_heading')</h1>
                <p class="text-gray-600 mt-1">@yield('page_description')</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
                @yield('page_actions')
            </div>
        </div>
    </div>

    <!-- Optional Stats/Info Cards -->
    @hasSection('stats_cards')
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        @yield('stats_cards')
    </div>
    @endif

    <!-- Main Content Area -->
    <div class="space-y-8">
        @yield('main_content')
    </div>

    <!-- Optional Bottom Section -->
    @hasSection('bottom_section')
    <div class="mt-8">
        @yield('bottom_section')
    </div>
    @endif
</div>
@endsection

{{-- 
DASHBOARD TEMPLATE USAGE GUIDE:

@extends('layouts.dashboard-template')

@section('page_title', 'Your Page Title - Minilink.at')
@section('page_heading', 'Your Page Heading')
@section('page_description', 'Your page description text')

@section('page_actions')
    <a href="#" class="btn-primary">Primary Action</a>
    <a href="#" class="btn-secondary">Secondary Action</a>
@endsection

@section('stats_cards')
    <!-- Optional: Add stat cards here -->
    <div class="card">
        <div class="card-body">
            <!-- Stat card content -->
        </div>
    </div>
@endsection

@section('main_content')
    <!-- Your main page content here -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Section Title</h3>
        </div>
        <div class="card-body">
            <!-- Section content -->
        </div>
    </div>
@endsection

@section('bottom_section')
    <!-- Optional: Additional content at bottom -->
@endsection
--}}
