<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Url;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class UrlController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of URLs.
     */
    public function index(Request $request): View
    {
        $query = Url::with(['user', 'analytics']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('original_url', 'like', "%{$search}%")
                  ->orWhere('custom_alias', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            switch ($request->get('status')) {
                case 'active':
                    $query->where('is_active', true)
                          ->where(function($q) {
                              $q->whereNull('expires_at')
                                ->orWhere('expires_at', '>', now());
                          });
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
                case 'password_protected':
                    $query->whereNotNull('password');
                    break;
                case 'custom_alias':
                    $query->whereNotNull('custom_alias');
                    break;
            }
        }

        // Apply user filter
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'title', 'click_count', 'original_url'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $urls = $query->paginate(25)->withQueryString();

        // Get filter counts
        $filterCounts = [
            'all' => Url::count(),
            'active' => Url::where('is_active', true)
                          ->where(function($q) {
                              $q->whereNull('expires_at')
                                ->orWhere('expires_at', '>', now());
                          })->count(),
            'inactive' => Url::where('is_active', false)->count(),
            'expired' => Url::where('expires_at', '<', now())->count(),
            'password_protected' => Url::whereNotNull('password')->count(),
            'custom_alias' => Url::whereNotNull('custom_alias')->count(),
        ];

        // Get users for filter dropdown
        $users = User::orderBy('name')->get(['id', 'name', 'email']);

        return view('admin.urls.index', compact('urls', 'filterCounts', 'users'));
    }

    /**
     * Display the specified URL.
     */
    public function show(Url $url): View
    {
        $url->load(['user', 'analytics' => function($query) {
            $query->orderBy('clicked_at', 'desc')->limit(50);
        }]);

        $stats = [
            'total_clicks' => $url->analytics()->count(),
            'unique_clicks' => $url->analytics()->distinct('ip_address')->count(),
            'clicks_today' => $url->analytics()->whereDate('clicked_at', today())->count(),
            'clicks_week' => $url->analytics()->where('clicked_at', '>=', now()->subWeek())->count(),
            'clicks_month' => $url->analytics()->where('clicked_at', '>=', now()->subMonth())->count(),
            'top_countries' => $url->analytics()
                ->selectRaw('country, COUNT(*) as count')
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'top_referrers' => $url->analytics()
                ->selectRaw('referrer, COUNT(*) as count')
                ->whereNotNull('referrer')
                ->where('referrer', '!=', '')
                ->groupBy('referrer')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
        ];

        return view('admin.urls.show', compact('url', 'stats'));
    }

    /**
     * Show the form for editing the specified URL.
     */
    public function edit(Url $url): View
    {
        $users = User::orderBy('name')->get(['id', 'name', 'email']);
        return view('admin.urls.edit', compact('url', 'users'));
    }

    /**
     * Update the specified URL.
     */
    public function update(Request $request, Url $url): RedirectResponse
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'nullable|boolean',
            'user_id' => 'required|exists:users,id',
            'custom_alias' => 'nullable|string|max:50|unique:urls,custom_alias,' . $url->id,
        ]);

        $updateData = $request->only(['title', 'description', 'expires_at', 'user_id']);
        $updateData['is_active'] = $request->has('is_active');

        // Handle custom alias
        if ($request->filled('custom_alias')) {
            $updateData['custom_alias'] = $request->custom_alias;
        }

        $url->update($updateData);

        return redirect()->route('admin.urls.show', $url)
            ->with('success', 'URL updated successfully!');
    }

    /**
     * Remove the specified URL.
     */
    public function destroy(Url $url): RedirectResponse
    {
        $title = $url->title ?: 'Untitled URL';
        $url->delete();

        return redirect()->route('admin.urls.index')
            ->with('success', "URL '{$title}' has been deleted successfully.");
    }

    /**
     * Bulk operations on URLs.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,export',
            'url_ids' => 'required|array',
            'url_ids.*' => 'exists:urls,id',
        ]);

        $urlIds = $request->get('url_ids');
        $action = $request->get('action');
        $affectedCount = 0;

        switch ($action) {
            case 'activate':
                $affectedCount = Url::whereIn('id', $urlIds)->update(['is_active' => true]);
                break;
                
            case 'deactivate':
                $affectedCount = Url::whereIn('id', $urlIds)->update(['is_active' => false]);
                break;
                
            case 'delete':
                $affectedCount = Url::whereIn('id', $urlIds)->count();
                Url::whereIn('id', $urlIds)->delete();
                break;
                
            case 'export':
                return $this->exportUrls($urlIds);
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully {$action}d {$affectedCount} URL(s).",
            'affected_count' => $affectedCount,
        ]);
    }

    /**
     * Export URLs to CSV.
     */
    private function exportUrls(array $urlIds): JsonResponse
    {
        $urls = Url::with('user')->whereIn('id', $urlIds)->get();
        
        $csvData = [];
        $csvData[] = ['Title', 'Original URL', 'Short URL', 'User', 'Clicks', 'Status', 'Created'];

        foreach ($urls as $url) {
            $status = 'Active';
            if (!$url->is_active) {
                $status = 'Inactive';
            } elseif ($url->isExpired()) {
                $status = 'Expired';
            }

            $csvData[] = [
                $url->title ?: 'Untitled',
                $url->original_url,
                $url->short_url,
                $url->user->name,
                $url->click_count,
                $status,
                $url->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'admin_urls_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $filePath = storage_path('app/exports/' . $filename);
        
        // Ensure directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        $file = fopen($filePath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return response()->json([
            'success' => true,
            'download_url' => route('admin.urls.download-export', ['filename' => $filename]),
            'message' => 'Export generated successfully.',
        ]);
    }

    /**
     * Download exported CSV file.
     */
    public function downloadExport(Request $request, string $filename)
    {
        $filePath = storage_path('app/exports/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'Export file not found.');
        }

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Get URL statistics for dashboard.
     */
    public function getStats(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week'); // week, month, year
        
        $startDate = match($period) {
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'year' => now()->subYear(),
            default => now()->subWeek(),
        };

        $stats = [
            'total_urls' => Url::count(),
            'active_urls' => Url::where('is_active', true)->count(),
            'urls_in_period' => Url::where('created_at', '>=', $startDate)->count(),
            'total_clicks' => Url::sum('click_count'),
            'top_performing' => Url::with('user')
                ->orderBy('click_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'short_code', 'click_count', 'user_id']),
            'recent_activity' => Url::with('user')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'short_code', 'created_at', 'user_id']),
        ];

        return response()->json($stats);
    }

    /**
     * Moderate URL (approve/reject).
     */
    public function moderate(Request $request, Url $url): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:approve,reject',
            'reason' => 'nullable|string|max:500',
        ]);

        $action = $request->get('action');
        $reason = $request->get('reason');

        if ($action === 'approve') {
            $url->update(['is_active' => true]);
            $message = 'URL approved successfully.';
        } else {
            $url->update(['is_active' => false]);
            $message = 'URL rejected successfully.';
            
            // Optionally notify user about rejection
            if ($reason) {
                // Here you could send an email notification to the user
                // with the rejection reason
            }
        }

        return redirect()->route('admin.urls.show', $url)
            ->with('success', $message);
    }
}
