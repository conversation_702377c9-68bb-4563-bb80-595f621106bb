@extends('layouts.modern')

@section('title', 'Edit URL')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-edit"></i> Edit URL</h1>
                    <p class="text-muted mb-0">{{ $url->title ?: 'Untitled URL' }}</p>
                </div>
                <div>
                    <a href="{{ route('urls.show', $url) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Analytics
                    </a>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-info">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> URL Settings</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('urls.update', $url) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- URL Information -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">URL Information</h6>
                            
                            <div class="mb-3">
                                <label for="original_url" class="form-label">Original URL</label>
                                <input type="text" class="form-control" value="{{ $url->original_url }}" readonly>
                                <div class="form-text">The original URL cannot be changed after creation.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="short_url" class="form-label">Short URL</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ $url->short_url }}" readonly>
                                    <button type="button" class="btn btn-outline-secondary copy-btn" onclick="copyToClipboard('{{ $url->short_url }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    @if($url->custom_alias)
                                        Using custom alias: <strong>{{ $url->custom_alias }}</strong>
                                    @else
                                        Using generated code: <strong>{{ $url->short_code }}</strong>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Metadata</h6>
                            
                            <div class="mb-3">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $url->title) }}"
                                       maxlength="255"
                                       placeholder="Enter a descriptive title for your URL">
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">This title will help you identify the URL in your dashboard.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" 
                                          name="description" 
                                          rows="3"
                                          maxlength="500"
                                          placeholder="Enter a description for your URL">{{ old('description', $url->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional description to help you remember what this URL is for.</div>
                            </div>
                        </div>

                        <!-- URL Settings -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">URL Settings</h6>
                            
                            <div class="mb-3">
                                <label for="expires_at" class="form-label">Expiration Date</label>
                                <input type="datetime-local" 
                                       class="form-control @error('expires_at') is-invalid @enderror" 
                                       id="expires_at" 
                                       name="expires_at" 
                                       value="{{ old('expires_at', $url->expires_at ? $url->expires_at->format('Y-m-d\TH:i') : '') }}"
                                       min="{{ now()->format('Y-m-d\TH:i') }}">
                                @error('expires_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave empty for no expiration. URL will become inactive after this date.</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ $url->is_active ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        URL is active
                                    </label>
                                </div>
                                <div class="form-text">Inactive URLs will not redirect and will show a 404 error.</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                                <a href="{{ route('urls.show', $url) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                    <i class="fas fa-trash"></i> Delete URL
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- URL Statistics Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> URL Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ number_format($url->click_count) }}</h4>
                            <small class="text-muted">Total Clicks</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ $url->created_at->diffForHumans() }}</h4>
                            <small class="text-muted">Created</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        @if($url->is_active)
                            @if($url->isExpired())
                                <span class="badge bg-warning">Expired</span>
                            @else
                                <span class="badge bg-success">Active</span>
                            @endif
                        @else
                            <span class="badge bg-secondary">Inactive</span>
                        @endif
                        
                        @if($url->isPasswordProtected())
                            <span class="badge bg-info">Password Protected</span>
                        @endif
                    </div>
                    
                    @if($url->expires_at)
                    <div class="mb-3">
                        <h6 class="text-muted">Expires</h6>
                        <p class="mb-0">{{ $url->expires_at->format('M j, Y g:i A') }}</p>
                        <small class="text-muted">{{ $url->expires_at->diffForHumans() }}</small>
                    </div>
                    @endif
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('urls.show', $url) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-chart-line"></i> View Analytics
                        </a>
                        <a href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}" class="btn btn-outline-secondary btn-sm" target="_blank">
                            <i class="fas fa-qrcode"></i> View QR Code
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete URL</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this URL?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The short URL will become permanently unavailable.
                </div>
                <div class="bg-light p-3 rounded">
                    <strong>URL:</strong> {{ $url->short_url }}<br>
                    <strong>Original:</strong> {{ Str::limit($url->original_url, 60) }}<br>
                    <strong>Clicks:</strong> {{ number_format($url->click_count) }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('urls.destroy', $url) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete URL
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const btn = event.target.closest('.copy-btn');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}
</script>
@endpush
