<?php

namespace App\Http\Controllers;

use App\Models\Analytics;
use App\Models\Url;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdvancedAnalyticsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show advanced analytics dashboard.
     */
    public function index(): View
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return view('analytics.upgrade-required');
        }

        $overview = $this->getOverviewStats($user);
        $recentActivity = $this->getRecentActivity($user);

        return view('analytics.advanced.index', compact('overview', 'recentActivity'));
    }

    /**
     * Get detailed analytics for a specific URL.
     */
    public function url(Request $request, Url $url): View
    {
        $this->authorize('view', $url);
        
        $user = Auth::user();
        if (!$user->subscription?->analytics) {
            return view('analytics.upgrade-required');
        }

        $period = $request->get('period', 'week');
        $analytics = $this->getUrlAnalytics($url, $period);

        return view('analytics.advanced.url', compact('url', 'analytics', 'period'));
    }

    /**
     * Get comparison analytics between URLs.
     */
    public function compare(Request $request): View
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return view('analytics.upgrade-required');
        }

        $urlIds = $request->get('urls', []);
        $period = $request->get('period', 'week');

        $urls = $user->urls()->whereIn('id', $urlIds)->get();
        $comparison = $this->getComparisonData($urls, $period);

        return view('analytics.advanced.compare', compact('urls', 'comparison', 'period'));
    }

    /**
     * Get real-time analytics data.
     */
    public function realtime(): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $data = $this->getRealtimeData($user);
        
        return response()->json($data);
    }

    /**
     * Get geographic analytics data.
     */
    public function geographic(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $period = $request->get('period', 'week');
        $data = $this->getGeographicData($user, $period);
        
        return response()->json($data);
    }

    /**
     * Get device and browser analytics.
     */
    public function devices(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $period = $request->get('period', 'week');
        $data = $this->getDeviceData($user, $period);
        
        return response()->json($data);
    }

    /**
     * Get traffic sources analytics.
     */
    public function sources(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $period = $request->get('period', 'week');
        $data = $this->getTrafficSources($user, $period);
        
        return response()->json($data);
    }

    /**
     * Get click trends over time.
     */
    public function trends(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $period = $request->get('period', 'week');
        $urlId = $request->get('url_id');
        
        $data = $this->getClickTrends($user, $period, $urlId);
        
        return response()->json($data);
    }

    /**
     * Export analytics data.
     */
    public function export(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user->subscription?->analytics) {
            return response()->json(['error' => 'Advanced analytics not available'], 403);
        }

        $request->validate([
            'format' => 'required|in:csv,json,pdf',
            'period' => 'required|in:week,month,quarter,year',
            'include_details' => 'boolean',
        ]);

        $filename = $this->generateAnalyticsExport($user, $request->all());
        
        return response()->json([
            'success' => true,
            'download_url' => route('analytics.download', ['filename' => $filename]),
            'filename' => $filename,
        ]);
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($user): array
    {
        $urlIds = $user->urls()->pluck('id');

        // Current period stats
        $totalClicks = Analytics::whereIn('url_id', $urlIds)->count();
        $uniqueVisitors = Analytics::whereIn('url_id', $urlIds)->distinct('ip_address')->count();
        $clicksThisMonth = Analytics::whereIn('url_id', $urlIds)
            ->where('clicked_at', '>=', now()->subMonth())->count();
        $clicksLastMonth = Analytics::whereIn('url_id', $urlIds)
            ->whereBetween('clicked_at', [now()->subMonths(2), now()->subMonth()])->count();

        // Calculate percentage changes
        $clicksChange = $clicksLastMonth > 0 ? round((($clicksThisMonth - $clicksLastMonth) / $clicksLastMonth) * 100, 1) : 0;
        $visitorsChange = 15.2; // Placeholder - would need more complex calculation

        // Top performing URL
        $topUrl = $user->urls()->orderBy('click_count', 'desc')->first();

        // Chart data for last 30 days
        $chartLabels = [];
        $chartData = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $chartLabels[] = $date->format('M j');
            $chartData[] = Analytics::whereIn('url_id', $urlIds)
                ->whereDate('clicked_at', $date)
                ->count();
        }

        // Geographic data (top 5 countries)
        $geoData = Analytics::whereIn('url_id', $urlIds)
            ->selectRaw('country, COUNT(*) as clicks')
            ->whereNotNull('country')
            ->groupBy('country')
            ->orderBy('clicks', 'desc')
            ->limit(5)
            ->get();

        // Device data
        $deviceData = Analytics::whereIn('url_id', $urlIds)
            ->selectRaw('device_type, COUNT(*) as clicks')
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->orderBy('clicks', 'desc')
            ->get();

        return [
            'total_clicks' => $totalClicks,
            'unique_visitors' => $uniqueVisitors,
            'clicks_change' => $clicksChange,
            'visitors_change' => $visitorsChange,
            'top_url_clicks' => $topUrl ? $topUrl->click_count : 0,
            'top_url_title' => $topUrl ? ($topUrl->title ?: 'Untitled') : 'No URLs',
            'click_rate' => $user->urls()->count() > 0 ? ($totalClicks / $user->urls()->count()) : 0,
            'chart_labels' => $chartLabels,
            'chart_data' => $chartData,
            'geo_labels' => $geoData->pluck('country')->toArray(),
            'geo_data' => $geoData->pluck('clicks')->toArray(),
            'device_labels' => $deviceData->pluck('device_type')->toArray(),
            'device_data' => $deviceData->pluck('clicks')->toArray(),
            'top_urls' => $user->urls()
                ->orderBy('click_count', 'desc')
                ->limit(5)
                ->get()
                ->map(function($url) {
                    return [
                        'title' => $url->title,
                        'short_url' => $url->short_url,
                        'original_url' => $url->original_url,
                        'clicks' => $url->click_count
                    ];
                })->toArray(),
        ];
    }

    /**
     * Get recent activity.
     */
    private function getRecentActivity($user): array
    {
        $urlIds = $user->urls()->pluck('id');

        return Analytics::whereIn('url_id', $urlIds)
            ->with('url:id,title,short_code')
            ->orderBy('clicked_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($analytic) {
                return [
                    'url_title' => $analytic->url->title ?: 'Untitled',
                    'short_code' => $analytic->url->short_code,
                    'country' => $analytic->country,
                    'device' => $analytic->device_type ?: 'Unknown device',
                    'device_type' => $analytic->device_type,
                    'browser' => $analytic->browser,
                    'clicked_at' => $analytic->clicked_at,
                    'time_ago' => $analytic->clicked_at->diffForHumans(),
                ];
            })->toArray();
    }

    /**
     * Get detailed URL analytics.
     */
    private function getUrlAnalytics(Url $url, string $period): array
    {
        $startDate = $this->getPeriodStartDate($period);
        
        $query = $url->analytics();
        if ($startDate) {
            $query->where('clicked_at', '>=', $startDate);
        }

        return [
            'total_clicks' => $query->count(),
            'unique_clicks' => $query->distinct('ip_address')->count(),
            'countries' => $query->select('country', DB::raw('COUNT(*) as count'))
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'devices' => $query->select('device_type', DB::raw('COUNT(*) as count'))
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->orderBy('count', 'desc')
                ->get(),
            'browsers' => $query->select('browser', DB::raw('COUNT(*) as count'))
                ->whereNotNull('browser')
                ->groupBy('browser')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'referrers' => $query->select('referrer', DB::raw('COUNT(*) as count'))
                ->whereNotNull('referrer')
                ->where('referrer', '!=', '')
                ->groupBy('referrer')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'hourly_distribution' => $this->getHourlyDistribution($url, $period),
            'daily_trends' => $this->getDailyTrends($url, $period),
        ];
    }

    /**
     * Get comparison data between URLs.
     */
    private function getComparisonData($urls, string $period): array
    {
        $startDate = $this->getPeriodStartDate($period);
        $comparison = [];

        foreach ($urls as $url) {
            $query = $url->analytics();
            if ($startDate) {
                $query->where('clicked_at', '>=', $startDate);
            }

            $comparison[] = [
                'url' => $url,
                'clicks' => $query->count(),
                'unique_clicks' => $query->distinct('ip_address')->count(),
                'conversion_rate' => $this->calculateConversionRate($url, $period),
                'avg_daily_clicks' => $this->getAverageDailyClicks($url, $period),
            ];
        }

        return $comparison;
    }

    /**
     * Get real-time data.
     */
    private function getRealtimeData($user): array
    {
        $urlIds = $user->urls()->pluck('id');
        $last24Hours = now()->subDay();

        return [
            'clicks_last_hour' => Analytics::whereIn('url_id', $urlIds)
                ->where('clicked_at', '>=', now()->subHour())->count(),
            'clicks_last_24h' => Analytics::whereIn('url_id', $urlIds)
                ->where('clicked_at', '>=', $last24Hours)->count(),
            'active_urls' => Analytics::whereIn('url_id', $urlIds)
                ->where('clicked_at', '>=', $last24Hours)
                ->distinct('url_id')->count(),
            'top_countries' => Analytics::whereIn('url_id', $urlIds)
                ->where('clicked_at', '>=', $last24Hours)
                ->select('country', DB::raw('COUNT(*) as count'))
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'recent_clicks' => Analytics::whereIn('url_id', $urlIds)
                ->with('url:id,title,short_code')
                ->orderBy('clicked_at', 'desc')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get geographic data.
     */
    private function getGeographicData($user, string $period): array
    {
        $urlIds = $user->urls()->pluck('id');
        $startDate = $this->getPeriodStartDate($period);
        
        $query = Analytics::whereIn('url_id', $urlIds);
        if ($startDate) {
            $query->where('clicked_at', '>=', $startDate);
        }

        return [
            'countries' => $query->select('country', DB::raw('COUNT(*) as clicks'))
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('clicks', 'desc')
                ->get(),
            'cities' => $query->select('city', 'country', DB::raw('COUNT(*) as clicks'))
                ->whereNotNull('city')
                ->groupBy('city', 'country')
                ->orderBy('clicks', 'desc')
                ->limit(20)
                ->get(),
        ];
    }

    /**
     * Get device data.
     */
    private function getDeviceData($user, string $period): array
    {
        $urlIds = $user->urls()->pluck('id');
        $startDate = $this->getPeriodStartDate($period);
        
        $query = Analytics::whereIn('url_id', $urlIds);
        if ($startDate) {
            $query->where('clicked_at', '>=', $startDate);
        }

        return [
            'devices' => $query->select('device_type', DB::raw('COUNT(*) as clicks'))
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->orderBy('clicks', 'desc')
                ->get(),
            'browsers' => $query->select('browser', DB::raw('COUNT(*) as clicks'))
                ->whereNotNull('browser')
                ->groupBy('browser')
                ->orderBy('clicks', 'desc')
                ->get(),
            'platforms' => $query->select('platform', DB::raw('COUNT(*) as clicks'))
                ->whereNotNull('platform')
                ->groupBy('platform')
                ->orderBy('clicks', 'desc')
                ->get(),
        ];
    }

    /**
     * Get traffic sources.
     */
    private function getTrafficSources($user, string $period): array
    {
        $urlIds = $user->urls()->pluck('id');
        $startDate = $this->getPeriodStartDate($period);
        
        $query = Analytics::whereIn('url_id', $urlIds);
        if ($startDate) {
            $query->where('clicked_at', '>=', $startDate);
        }

        $directClicks = $query->clone()->where(function($q) {
            $q->whereNull('referrer')->orWhere('referrer', '');
        })->count();

        $referrers = $query->select('referrer', DB::raw('COUNT(*) as clicks'))
            ->whereNotNull('referrer')
            ->where('referrer', '!=', '')
            ->groupBy('referrer')
            ->orderBy('clicks', 'desc')
            ->get();

        return [
            'direct' => $directClicks,
            'referrers' => $referrers,
            'social_media' => $this->categorizeSocialMedia($referrers),
            'search_engines' => $this->categorizeSearchEngines($referrers),
        ];
    }

    /**
     * Get click trends.
     */
    private function getClickTrends($user, string $period, $urlId = null): array
    {
        $days = match($period) {
            'week' => 7,
            'month' => 30,
            'quarter' => 90,
            'year' => 365,
            default => 7,
        };

        $query = Analytics::query();
        
        if ($urlId) {
            $query->where('url_id', $urlId);
        } else {
            $urlIds = $user->urls()->pluck('id');
            $query->whereIn('url_id', $urlIds);
        }

        $trends = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = $query->clone()->whereDate('clicked_at', $date)->count();
            
            $trends[] = [
                'date' => $date->format('Y-m-d'),
                'clicks' => $count,
            ];
        }

        return $trends;
    }

    /**
     * Helper methods.
     */
    private function getPeriodStartDate(string $period): ?\Carbon\Carbon
    {
        return match($period) {
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'quarter' => now()->subMonths(3),
            'year' => now()->subYear(),
            default => null,
        };
    }

    private function getHourlyDistribution(Url $url, string $period): array
    {
        // Implementation for hourly click distribution
        return [];
    }

    private function getDailyTrends(Url $url, string $period): array
    {
        // Implementation for daily trends
        return [];
    }

    private function calculateConversionRate(Url $url, string $period): float
    {
        // Implementation for conversion rate calculation
        return 0.0;
    }

    private function getAverageDailyClicks(Url $url, string $period): float
    {
        // Implementation for average daily clicks
        return 0.0;
    }

    private function categorizeSocialMedia($referrers): array
    {
        // Implementation for social media categorization
        return [];
    }

    private function categorizeSearchEngines($referrers): array
    {
        // Implementation for search engine categorization
        return [];
    }

    private function generateAnalyticsExport($user, array $options): string
    {
        // Implementation for analytics export
        return 'analytics_export_' . now()->format('Y-m-d_H-i-s') . '.' . $options['format'];
    }
}
