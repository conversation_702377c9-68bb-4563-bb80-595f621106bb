@extends('layouts.dashboard-template')

@section('page_title', 'Analytics Settings - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-chart-bar mr-3 text-blue-600"></i>Analytics Settings
@endsection
@section('page_description', 'Configure Google Analytics, Google Tag Manager, and other tracking codes')

@section('page_actions')
    <a href="{{ route('admin.seo.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to SEO
    </a>
    <a href="{{ route('admin.seo.test') }}" class="btn-primary">
        <i class="fas fa-vial mr-2"></i>Test Analytics
    </a>
@endsection

@section('main_content')
    <!-- Google Analytics -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-google mr-2 text-red-500"></i>Google Analytics
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- GA4 Measurement ID -->
                    <div>
                        <label for="ga4_measurement_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chart-line mr-1 text-blue-500"></i>GA4 Measurement ID
                        </label>
                        <input type="text" 
                               id="ga4_measurement_id" 
                               name="analytics[ga4_measurement_id]" 
                               class="input-field"
                               value="{{ $analyticsSettings->where('key', 'ga4_measurement_id')->first()->value ?? '' }}"
                               placeholder="G-XXXXXXXXXX">
                        <p class="mt-1 text-sm text-gray-500">
                            Your Google Analytics 4 Measurement ID (starts with G-)
                        </p>
                    </div>

                    <!-- Universal Analytics (Legacy) -->
                    <div>
                        <label for="ua_tracking_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chart-area mr-1 text-green-500"></i>Universal Analytics ID (Legacy)
                        </label>
                        <input type="text" 
                               id="ua_tracking_id" 
                               name="analytics[ua_tracking_id]" 
                               class="input-field"
                               value="{{ $analyticsSettings->where('key', 'ua_tracking_id')->first()->value ?? '' }}"
                               placeholder="UA-XXXXXXXXX-X">
                        <p class="mt-1 text-sm text-gray-500">
                            Legacy Universal Analytics ID (will stop working July 2024)
                        </p>
                    </div>

                    <!-- Enhanced Ecommerce -->
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="ga_enhanced_ecommerce" 
                               name="analytics[ga_enhanced_ecommerce]" 
                               class="form-checkbox h-4 w-4 text-blue-600"
                               value="1"
                               {{ ($analyticsSettings->where('key', 'ga_enhanced_ecommerce')->first()->value ?? '') ? 'checked' : '' }}>
                        <label for="ga_enhanced_ecommerce" class="ml-2 text-sm text-gray-700">
                            Enable Enhanced Ecommerce Tracking
                        </label>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Google Analytics
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Google Tag Manager -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-tags mr-2 text-purple-600"></i>Google Tag Manager
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- GTM Container ID -->
                    <div>
                        <label for="gtm_container_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-cube mr-1 text-purple-500"></i>GTM Container ID
                        </label>
                        <input type="text" 
                               id="gtm_container_id" 
                               name="analytics[gtm_container_id]" 
                               class="input-field"
                               value="{{ $analyticsSettings->where('key', 'gtm_container_id')->first()->value ?? '' }}"
                               placeholder="GTM-XXXXXXX">
                        <p class="mt-1 text-sm text-gray-500">
                            Your Google Tag Manager Container ID (starts with GTM-)
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save GTM Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Other Analytics -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chart-pie mr-2 text-orange-600"></i>Other Analytics Services
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Facebook Pixel -->
                    <div>
                        <label for="facebook_pixel_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-facebook mr-1 text-blue-600"></i>Facebook Pixel ID
                        </label>
                        <input type="text" 
                               id="facebook_pixel_id" 
                               name="analytics[facebook_pixel_id]" 
                               class="input-field"
                               value="{{ $analyticsSettings->where('key', 'facebook_pixel_id')->first()->value ?? '' }}"
                               placeholder="123456789012345">
                        <p class="mt-1 text-sm text-gray-500">
                            Your Facebook Pixel ID for conversion tracking
                        </p>
                    </div>

                    <!-- Hotjar -->
                    <div>
                        <label for="hotjar_site_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-fire mr-1 text-red-500"></i>Hotjar Site ID
                        </label>
                        <input type="text" 
                               id="hotjar_site_id" 
                               name="analytics[hotjar_site_id]" 
                               class="input-field"
                               value="{{ $analyticsSettings->where('key', 'hotjar_site_id')->first()->value ?? '' }}"
                               placeholder="1234567">
                        <p class="mt-1 text-sm text-gray-500">
                            Your Hotjar Site ID for heatmaps and user recordings
                        </p>
                    </div>

                    <!-- Custom Analytics Code -->
                    <div>
                        <label for="custom_analytics_code" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-code mr-1 text-gray-600"></i>Custom Analytics Code
                        </label>
                        <textarea id="custom_analytics_code" 
                                  name="analytics[custom_analytics_code]" 
                                  rows="6" 
                                  class="input-field font-mono text-sm"
                                  placeholder="<!-- Custom analytics code here -->">{{ $analyticsSettings->where('key', 'custom_analytics_code')->first()->value ?? '' }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">
                            Custom analytics or tracking code (will be inserted in the head section)
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Other Analytics
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Analytics Status -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Analytics Status</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($analyticsSettings->where('key', 'ga4_measurement_id')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Google Analytics 4</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($analyticsSettings->where('key', 'gtm_container_id')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Google Tag Manager</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($analyticsSettings->where('key', 'facebook_pixel_id')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Facebook Pixel</div>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl mb-2">
                        @if($analyticsSettings->where('key', 'hotjar_site_id')->first()->value ?? '')
                            <i class="fas fa-check-circle text-green-500"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500"></i>
                        @endif
                    </div>
                    <div class="text-sm font-medium">Hotjar</div>
                </div>
            </div>
        </div>
    </div>
@endsection
