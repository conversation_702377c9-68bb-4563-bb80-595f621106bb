<?php

namespace App\Policies;

use App\Models\CustomDomain;
use App\Models\User;

class CustomDomainPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view their own domains
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CustomDomain $customDomain): bool
    {
        return $user->id === $customDomain->user_id || $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Check if user has reached their domain limit
        $maxDomains = $user->subscription?->custom_domains_limit ?? 1;
        $currentDomains = $user->customDomains()->count();

        return $currentDomains < $maxDomains;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CustomDomain $customDomain): bool
    {
        return $user->id === $customDomain->user_id || $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CustomDomain $customDomain): bool
    {
        return $user->id === $customDomain->user_id || $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CustomDomain $customDomain): bool
    {
        return $user->id === $customDomain->user_id || $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CustomDomain $customDomain): bool
    {
        return $user->isAdmin();
    }
}
