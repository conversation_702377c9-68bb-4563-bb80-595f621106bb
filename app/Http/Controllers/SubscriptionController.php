<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display subscription plans.
     */
    public function index(): View
    {
        $subscriptions = Subscription::where('is_active', true)
            ->orderBy('price')
            ->get();

        $currentSubscription = Auth::user()->subscription;

        return view('subscriptions.modern-index', compact('subscriptions', 'currentSubscription'));
    }

    /**
     * Show subscription details.
     */
    public function show(Subscription $subscription): View
    {
        $currentSubscription = Auth::user()->subscription;
        
        return view('subscriptions.show', compact('subscription', 'currentSubscription'));
    }

    /**
     * Upgrade to a subscription plan.
     */
    public function upgrade(Request $request, Subscription $subscription): RedirectResponse
    {
        $user = Auth::user();
        $currentSubscription = $user->subscription;

        // Check if user is trying to downgrade
        if ($currentSubscription && $subscription->price < $currentSubscription->price) {
            return back()->with('error', 'Cannot downgrade to a lower plan. Please contact support.');
        }

        // Check if user already has this subscription
        if ($currentSubscription && $currentSubscription->id === $subscription->id) {
            return back()->with('info', 'You already have this subscription plan.');
        }

        // For free plans, upgrade immediately
        if ($subscription->isFree()) {
            $user->update(['subscription_id' => $subscription->id]);
            return redirect()->route('subscriptions.index')
                ->with('success', 'Successfully upgraded to ' . $subscription->name . '!');
        }

        // For paid plans, redirect to payment (in a real app, this would integrate with Stripe/PayPal)
        return redirect()->route('subscriptions.payment', $subscription)
            ->with('info', 'Please complete payment to upgrade to ' . $subscription->name . '.');
    }

    /**
     * Show payment page for subscription.
     */
    public function payment(Subscription $subscription): View
    {
        $user = Auth::user();
        $currentSubscription = $user->subscription;

        return view('subscriptions.payment', compact('subscription', 'currentSubscription'));
    }

    /**
     * Process subscription payment (mock implementation).
     */
    public function processPayment(Request $request, Subscription $subscription): RedirectResponse
    {
        $request->validate([
            'payment_method' => 'required|in:credit_card,paypal',
            'terms_accepted' => 'required|accepted',
        ]);

        $user = Auth::user();

        if ($request->payment_method === 'paypal') {
            // For PayPal, redirect to PayPal payment page
            $paypalService = new PayPalService();
            $result = $paypalService->createOrder(
                $subscription->price,
                'USD',
                $subscription->name . ' Subscription'
            );

            if ($result['success']) {
                // Store the order ID in session for later verification
                session(['paypal_order_id' => $result['order_id'], 'subscription_id' => $subscription->id]);

                // Find the approval URL
                foreach ($result['links'] as $link) {
                    if ($link->rel === 'approve') {
                        return redirect($link->href);
                    }
                }
            }

            return back()->with('error', 'Failed to create PayPal payment. Please try again.');
        }

        // Handle credit card payment (mock implementation)
        $request->validate([
            'card_number' => 'required_if:payment_method,credit_card',
            'expiry_date' => 'required_if:payment_method,credit_card',
            'cvv' => 'required_if:payment_method,credit_card',
            'cardholder_name' => 'required_if:payment_method,credit_card',
        ]);

        // Simulate credit card processing
        // In production, integrate with Stripe or other payment processor

        // Update user subscription
        $user->update([
            'subscription_id' => $subscription->id,
            'subscription_expires_at' => now()->addMonth(), // Monthly billing
        ]);

        // Log the subscription change
        Log::info('Subscription upgraded', [
            'user_id' => $user->id,
            'subscription_id' => $subscription->id,
            'payment_method' => $request->payment_method,
        ]);

        return redirect()->route('subscriptions.index')
            ->with('success', 'Payment successful! Welcome to ' . $subscription->name . '!');
    }

    /**
     * Cancel subscription.
     */
    public function cancel(): RedirectResponse
    {
        $user = Auth::user();
        $currentSubscription = $user->subscription;

        if (!$currentSubscription || $currentSubscription->isFree()) {
            return back()->with('error', 'No active subscription to cancel.');
        }

        // Find free plan
        $freePlan = Subscription::where('price', 0)->where('is_active', true)->first();
        
        if (!$freePlan) {
            return back()->with('error', 'Unable to process cancellation. Please contact support.');
        }

        // Downgrade to free plan
        $user->update([
            'subscription_id' => $freePlan->id,
            'subscription_expires_at' => null,
        ]);

        return redirect()->route('subscriptions.index')
            ->with('success', 'Subscription cancelled successfully. You have been moved to the free plan.');
    }

    /**
     * Show subscription usage and limits.
     */
    public function usage(): View
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        if (!$subscription) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'No active subscription found.');
        }

        $usage = [
            'urls' => [
                'current' => $user->urls()->count(),
                'limit' => $subscription->url_limit,
                'percentage' => $subscription->url_limit ? 
                    round(($user->urls()->count() / $subscription->url_limit) * 100, 1) : 0,
            ],
            'clicks' => [
                'current' => $user->urls()->sum('click_count'),
                'limit' => $subscription->click_limit,
                'percentage' => $subscription->click_limit ? 
                    round(($user->urls()->sum('click_count') / $subscription->click_limit) * 100, 1) : 0,
            ],
            'custom_domains' => [
                'current' => $user->customDomains()->count(),
                'limit' => $subscription->custom_domains_limit ?? ($subscription->custom_domains ? 5 : 0),
                'percentage' => $subscription->custom_domains ? 
                    round(($user->customDomains()->count() / 5) * 100, 1) : 0,
            ],
        ];

        return view('subscriptions.usage', compact('subscription', 'usage'));
    }

    /**
     * Show billing history.
     */
    public function billing(): View
    {
        $user = Auth::user();
        
        // In a real app, this would fetch actual billing records
        $billingHistory = collect([
            [
                'id' => 'inv_001',
                'date' => now()->subMonth(),
                'amount' => $user->subscription?->price ?? 0,
                'status' => 'paid',
                'description' => $user->subscription?->name ?? 'Free Plan',
            ],
            [
                'id' => 'inv_002',
                'date' => now()->subMonths(2),
                'amount' => $user->subscription?->price ?? 0,
                'status' => 'paid',
                'description' => $user->subscription?->name ?? 'Free Plan',
            ],
        ]);

        return view('subscriptions.billing', compact('billingHistory'));
    }

    /**
     * Compare subscription plans.
     */
    public function compare(): View
    {
        $subscriptions = Subscription::where('is_active', true)
            ->orderBy('price')
            ->get();

        $features = [
            'url_limit' => 'URLs per month',
            'click_limit' => 'Clicks per month',
            'custom_domains' => 'Custom domains',
            'analytics' => 'Advanced analytics',
            'api_access' => 'API access',
            'bulk_operations' => 'Bulk operations',
            'password_protection' => 'Password protection',
            'expiration_dates' => 'URL expiration',
        ];

        return view('subscriptions.compare', compact('subscriptions', 'features'));
    }

    /**
     * Check if user can perform action based on subscription.
     */
    public function checkLimit(string $feature): bool
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        if (!$subscription) {
            return false;
        }

        switch ($feature) {
            case 'create_url':
                if ($subscription->url_limit) {
                    return $user->urls()->count() < $subscription->url_limit;
                }
                return true;

            case 'custom_domain':
                if (!$subscription->custom_domains) {
                    return false;
                }
                $limit = $subscription->custom_domains_limit ?? 5;
                return $user->customDomains()->count() < $limit;

            case 'api_access':
                return $subscription->api_access;

            case 'bulk_operations':
                return $subscription->bulk_operations;

            case 'password_protection':
                return $subscription->password_protection;

            case 'analytics':
                return $subscription->analytics;

            default:
                return false;
        }
    }

    /**
     * Get subscription features for user.
     */
    public function getFeatures(): array
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        if (!$subscription) {
            return [];
        }

        return [
            'url_limit' => $subscription->url_limit,
            'click_limit' => $subscription->click_limit,
            'custom_domains' => $subscription->custom_domains,
            'custom_domains_limit' => $subscription->custom_domains_limit ?? 5,
            'analytics' => $subscription->analytics,
            'api_access' => $subscription->api_access,
            'bulk_operations' => $subscription->bulk_operations,
            'password_protection' => $subscription->password_protection,
            'expiration_dates' => $subscription->expiration_dates,
            'features' => $subscription->features ?? [],
        ];
    }

    /**
     * Handle PayPal payment success
     */
    public function paypalSuccess(Request $request, Subscription $subscription): JsonResponse
    {
        try {
            $orderID = $request->input('orderID');
            $payerID = $request->input('payerID');
            $details = $request->input('details');

            if (!$orderID || !$payerID) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing payment details'
                ], 400);
            }

            $paypalService = new PayPalService();
            $result = $paypalService->captureOrder($orderID);

            if ($result['success']) {
                $user = Auth::user();

                // Update user subscription
                $user->update([
                    'subscription_id' => $subscription->id,
                    'subscription_expires_at' => now()->addMonth(),
                ]);

                // Create payment record
                try {
                    Payment::create([
                        'user_id' => $user->id,
                        'subscription_id' => $subscription->id,
                        'payment_method' => 'paypal',
                        'amount' => $subscription->price,
                        'currency' => 'USD',
                        'transaction_id' => $result['capture_id'],
                        'status' => 'completed',
                        'payment_data' => $details,
                        'paid_at' => now(),
                    ]);
                } catch (\Exception $e) {
                    // Log error but continue with subscription activation
                    Log::warning('Could not save payment record', ['error' => $e->getMessage()]);
                }

                Log::info('PayPal payment completed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'order_id' => $orderID,
                    'capture_id' => $result['capture_id']
                ]);

                return response()->json([
                    'success' => true,
                    'redirect_url' => route('subscriptions.index')
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment capture failed'
            ], 400);

        } catch (\Exception $e) {
            Log::error('PayPal payment success handling failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }

    /**
     * Handle PayPal payment cancellation
     */
    public function paypalCancel(): RedirectResponse
    {
        return redirect()->route('subscriptions.index')
            ->with('error', 'Payment was cancelled. Please try again.');
    }

    /**
     * Handle PayPal webhooks
     */
    public function paypalWebhook(Request $request): JsonResponse
    {
        try {
            $headers = $request->headers->all();
            $body = $request->getContent();
            $eventData = json_decode($body, true);

            $paypalService = new PayPalService();

            // Verify webhook signature
            if (!$paypalService->verifyWebhookSignature($headers, $body)) {
                Log::warning('PayPal webhook signature verification failed');
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // Process the webhook event
            $result = $paypalService->processWebhookEvent($eventData);

            if ($result['success']) {
                return response()->json(['status' => 'success']);
            }

            return response()->json(['error' => 'Event processing failed'], 400);

        } catch (\Exception $e) {
            Log::error('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'body' => $request->getContent()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }
}
