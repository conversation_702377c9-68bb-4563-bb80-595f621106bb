@extends('layouts.modern')

@section('title', 'Customize QR Code - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-qrcode mr-3 text-purple-600"></i>
                    Customize QR Code
                </h1>
                <p class="text-gray-600 mt-1">{{ $url->title ?: 'Untitled URL' }}</p>
            </div>
            <div>
                <a href="{{ route('urls.show', $url) }}" class="btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Analytics
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- QR Code Preview -->
        <div class="lg:order-2">
            <div class="card sticky top-8">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-eye mr-2 text-purple-600"></i>
                        QR Code Preview
                    </h3>
                </div>
                <div class="card-body text-center">
                    <div id="qrPreview" class="mb-6">
                        @if($qrStats['qr_code_exists'])
                            <div class="inline-block p-4 bg-white rounded-lg shadow-sm border">
                                <img src="{{ asset('storage/' . $url->qr_code_path) }}"
                                     alt="QR Code"
                                     class="max-w-full h-auto"
                                     style="max-width: 300px;">
                            </div>
                        @else
                            <div class="py-16 text-gray-400">
                                <i class="fas fa-qrcode text-6xl mb-4"></i>
                                <p class="text-lg">No QR code generated yet</p>
                                <p class="text-sm">Use the customization options to generate your QR code</p>
                            </div>
                        @endif
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}"
                           class="btn-outline"
                           target="_blank">
                            <i class="fas fa-external-link-alt mr-2"></i>View
                        </a>
                        <a href="{{ route('qr.download', $url->custom_alias ?: $url->short_code) }}"
                           class="btn-primary">
                            <i class="fas fa-download mr-2"></i>Download
                        </a>
                    </div>
                </div>
            </div>

            <!-- QR Code Stats -->
            <div class="card mt-6">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        QR Code Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Status:</span>
                            <div class="mt-1">
                                @if($qrStats['qr_code_exists'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Generated
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>Not Generated
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">File Size:</span>
                            <div class="mt-1 text-sm text-gray-900">
                                {{ $qrStats['qr_code_size'] ? number_format($qrStats['qr_code_size'] / 1024, 1) . ' KB' : 'N/A' }}
                            </div>
                        </div>
                    </div>
                    @if($qrStats['created_at'])
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <span class="text-sm font-medium text-gray-500">Created:</span>
                        <div class="mt-1 text-sm text-gray-900">
                            {{ date('M d, Y H:i', $qrStats['created_at']) }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Customization Options -->
        <div class="lg:order-1 space-y-6">
            <!-- Basic Customization -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-palette mr-2 text-purple-600"></i>
                        Basic Customization
                    </h3>
                </div>
                <div class="card-body">
                    <form id="qrCustomizeForm" class="space-y-6">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="size" class="block text-sm font-medium text-gray-700 mb-2">Size (px)</label>
                                <input type="range"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                       id="size"
                                       name="size"
                                       min="100"
                                       max="1000"
                                       value="300"
                                       step="50">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>100px</span>
                                    <span id="sizeValue" class="font-medium text-purple-600">300px</span>
                                    <span>1000px</span>
                                </div>
                            </div>
                            <div>
                                <label for="margin" class="block text-sm font-medium text-gray-700 mb-2">Margin</label>
                                <input type="range"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                       id="margin"
                                       name="margin"
                                       min="0"
                                       max="10"
                                       value="2"
                                       step="1">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>0</span>
                                    <span id="marginValue" class="font-medium text-purple-600">2</span>
                                    <span>10</span>
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="format" class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                                <select class="form-select" id="format" name="format">
                                    <option value="png">PNG (Recommended)</option>
                                    <option value="svg">SVG (Vector)</option>
                                </select>
                            </div>
                            <div>
                                <label for="errorCorrection" class="block text-sm font-medium text-gray-700 mb-2">Error Correction</label>
                                <select class="form-select" id="errorCorrection" name="error_correction">
                                    <option value="L">Low (7%)</option>
                                    <option value="M" selected>Medium (15%)</option>
                                    <option value="Q">Quartile (25%)</option>
                                    <option value="H">High (30%)</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="backgroundColor" class="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color"
                                           class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
                                           id="backgroundColor"
                                           value="#ffffff">
                                    <span class="text-sm text-gray-600" id="backgroundColorValue">#ffffff</span>
                                </div>
                            </div>
                            <div>
                                <label for="foregroundColor" class="block text-sm font-medium text-gray-700 mb-2">Foreground Color</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color"
                                           class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
                                           id="foregroundColor"
                                           value="#000000">
                                    <span class="text-sm text-gray-600" id="foregroundColorValue">#000000</span>
                                </div>
                            </div>
                        </div>

                        <div class="pt-6 border-t border-gray-200">
                            <button type="submit" class="w-full btn-primary">
                                <i class="fas fa-magic mr-2"></i>Generate QR Code
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Logo Upload -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-image mr-2 text-green-600"></i>
                        Add Logo
                    </h3>
                </div>
                <div class="card-body">
                    <form id="qrLogoForm" enctype="multipart/form-data" class="space-y-6">
                        @csrf
                        <div>
                            <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">Upload Logo</label>
                            <div class="flex items-center justify-center w-full">
                                <label for="logo" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                        <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
                                        <p class="text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                    </div>
                                    <input type="file"
                                           class="hidden"
                                           id="logo"
                                           name="logo"
                                           accept="image/*">
                                </label>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">Recommended: Square image for best results</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="logoSize" class="block text-sm font-medium text-gray-700 mb-2">Logo Size</label>
                                <input type="range"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                       id="logoSize"
                                       name="logo_size"
                                       min="20"
                                       max="200"
                                       value="60"
                                       step="10">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>20px</span>
                                    <span id="logoSizeValue" class="font-medium text-green-600">60px</span>
                                    <span>200px</span>
                                </div>
                            </div>
                            <div>
                                <label for="qrSizeForLogo" class="block text-sm font-medium text-gray-700 mb-2">QR Size</label>
                                <input type="range"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                       id="qrSizeForLogo"
                                       name="size"
                                       min="200"
                                       max="800"
                                       value="300"
                                       step="50">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>200px</span>
                                    <span id="qrSizeForLogoValue" class="font-medium text-green-600">300px</span>
                                    <span>800px</span>
                                </div>
                            </div>
                        </div>

                        <div class="pt-6 border-t border-gray-200">
                            <button type="submit" class="w-full btn-primary bg-green-600 hover:bg-green-700">
                                <i class="fas fa-plus mr-2"></i>Generate with Logo
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Multiple Formats -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-layer-group mr-2 text-blue-600"></i>
                        Multiple Formats
                    </h3>
                </div>
                <div class="card-body">
                    <form id="qrMultipleForm" class="space-y-6">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Select Formats</label>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input class="form-checkbox" type="checkbox" value="png" id="formatPng" checked>
                                    <label class="ml-3 text-sm text-gray-700" for="formatPng">
                                        <span class="font-medium">PNG</span> - Best for web and print
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input class="form-checkbox" type="checkbox" value="svg" id="formatSvg">
                                    <label class="ml-3 text-sm text-gray-700" for="formatSvg">
                                        <span class="font-medium">SVG</span> - Vector format, scalable
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="pt-6 border-t border-gray-200">
                            <button type="submit" class="w-full btn-outline">
                                <i class="fas fa-download mr-2"></i>Generate Multiple Formats
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Update range input values
document.getElementById('size').addEventListener('input', function() {
    document.getElementById('sizeValue').textContent = this.value + 'px';
});

document.getElementById('margin').addEventListener('input', function() {
    document.getElementById('marginValue').textContent = this.value;
});

document.getElementById('logoSize').addEventListener('input', function() {
    document.getElementById('logoSizeValue').textContent = this.value + 'px';
});

document.getElementById('qrSizeForLogo').addEventListener('input', function() {
    document.getElementById('qrSizeForLogoValue').textContent = this.value + 'px';
});

// Update color values
document.getElementById('backgroundColor').addEventListener('input', function() {
    document.getElementById('backgroundColorValue').textContent = this.value;
});

document.getElementById('foregroundColor').addEventListener('input', function() {
    document.getElementById('foregroundColorValue').textContent = this.value;
});

// File upload preview
document.getElementById('logo').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const label = this.parentElement;
        label.innerHTML = `
            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <i class="fas fa-check-circle text-2xl text-green-500 mb-2"></i>
                <p class="text-sm text-gray-700"><span class="font-semibold">${file.name}</span></p>
                <p class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>
        `;
        label.appendChild(this);
    }
});

// Basic QR Code Generation
document.getElementById('qrCustomizeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formData.append('size', document.getElementById('size').value);
    formData.append('margin', document.getElementById('margin').value);
    formData.append('format', document.getElementById('format').value);
    formData.append('error_correction', document.getElementById('errorCorrection').value);
    
    // Convert hex colors to RGB arrays
    const bgColor = hexToRgb(document.getElementById('backgroundColor').value);
    const fgColor = hexToRgb(document.getElementById('foregroundColor').value);
    
    formData.append('background_color[0]', bgColor.r);
    formData.append('background_color[1]', bgColor.g);
    formData.append('background_color[2]', bgColor.b);
    formData.append('foreground_color[0]', fgColor.r);
    formData.append('foreground_color[1]', fgColor.g);
    formData.append('foreground_color[2]', fgColor.b);
    
    generateQrCode(formData, '{{ route("qr.generate", $url) }}');
});

// QR Code with Logo
document.getElementById('qrLogoForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    
    generateQrCode(formData, '{{ route("qr.generate-logo", $url) }}');
});

// Multiple Formats
document.getElementById('qrMultipleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formats = [];
    if (document.getElementById('formatPng').checked) formats.push('png');
    if (document.getElementById('formatSvg').checked) formats.push('svg');
    
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formats.forEach(format => formData.append('formats[]', format));
    
    generateQrCode(formData, '{{ route("qr.generate-multiple", $url) }}');
});

function generateQrCode(formData, url) {
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    submitBtn.disabled = true;
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateQrPreview(data.data);
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while generating the QR code.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function updateQrPreview(data) {
    const preview = document.getElementById('qrPreview');

    if (data.url) {
        preview.innerHTML = `
            <div class="inline-block p-4 bg-white rounded-lg shadow-sm border">
                <img src="${data.url}" alt="QR Code" class="max-w-full h-auto" style="max-width: 300px;">
            </div>
        `;
    } else if (data.png && data.png.url) {
        preview.innerHTML = `
            <div class="inline-block p-4 bg-white rounded-lg shadow-sm border">
                <img src="${data.png.url}" alt="QR Code" class="max-w-full h-auto" style="max-width: 300px;">
            </div>
        `;
    }
}

function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                   type === 'danger' ? 'bg-red-50 border-red-200 text-red-800' :
                   'bg-blue-50 border-blue-200 text-blue-800';

    alertDiv.className = `fixed top-4 right-4 z-50 p-4 border rounded-lg shadow-lg ${bgColor}`;
    alertDiv.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
                <span>${message}</span>
            </div>
            <button type="button" class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
@endpush
