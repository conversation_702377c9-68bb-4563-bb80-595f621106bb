<?php

namespace App\Services;

use App\Models\Analytics;
use App\Models\Url;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Agent\Agent;
use Jaybizzle\CrawlerDetect\CrawlerDetect;

class AnalyticsService
{
    private Agent $agent;
    private CrawlerDetect $crawlerDetect;

    public function __construct()
    {
        $this->agent = new Agent();
        $this->crawlerDetect = new CrawlerDetect();
    }

    /**
     * Track a click on a URL.
     */
    public function trackClick(Url $url, Request $request): Analytics
    {
        $ipAddress = $this->getClientIp($request);
        $userAgent = $request->userAgent();
        
        // Set user agent for the Agent library
        $this->agent->setUserAgent($userAgent);

        // Get geolocation data (you might want to use a service like MaxMind GeoIP2)
        $geoData = $this->getGeolocationData($ipAddress);

        // Create analytics record
        $analytics = Analytics::create([
            'url_id' => $url->id,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'referer' => $request->header('referer'),
            'country' => $geoData['country'] ?? null,
            'country_name' => $geoData['country_name'] ?? null,
            'city' => $geoData['city'] ?? null,
            'region' => $geoData['region'] ?? null,
            'device_type' => $this->getDeviceType(),
            'browser' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform(),
            'platform_version' => $this->agent->version($this->agent->platform()),
            'is_bot' => $this->crawlerDetect->isCrawler($userAgent),
            'clicked_at' => now(),
        ]);

        // Increment URL click count
        $url->incrementClicks();

        return $analytics;
    }

    /**
     * Get client IP address.
     */
    private function getClientIp(Request $request): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * Get device type.
     */
    private function getDeviceType(): string
    {
        if ($this->agent->isMobile()) {
            return 'mobile';
        } elseif ($this->agent->isTablet()) {
            return 'tablet';
        } elseif ($this->agent->isDesktop()) {
            return 'desktop';
        }

        return 'unknown';
    }

    /**
     * Get geolocation data from IP address.
     * This is a basic implementation. For production, use a service like MaxMind GeoIP2.
     */
    private function getGeolocationData(string $ipAddress): array
    {
        // Skip geolocation for local IPs
        if ($this->isLocalIp($ipAddress)) {
            return [
                'country' => 'XX',
                'country_name' => 'Local',
                'city' => 'Local',
                'region' => 'Local',
            ];
        }

        try {
            // Using a free IP geolocation service (consider using MaxMind for production)
            $response = file_get_contents("http://ip-api.com/json/{$ipAddress}");
            $data = json_decode($response, true);

            if ($data && $data['status'] === 'success') {
                return [
                    'country' => $data['countryCode'] ?? null,
                    'country_name' => $data['country'] ?? null,
                    'city' => $data['city'] ?? null,
                    'region' => $data['regionName'] ?? null,
                ];
            }
        } catch (\Exception $e) {
            \Log::error('Geolocation API error: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * Check if IP is local.
     */
    private function isLocalIp(string $ip): bool
    {
        return in_array($ip, ['127.0.0.1', '::1']) || 
               !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }

    /**
     * Get analytics summary for a URL.
     */
    public function getUrlAnalytics(Url $url, ?string $period = null): array
    {
        $query = $url->analytics();

        // Apply date filter if period is specified
        if ($period) {
            $query = $this->applyPeriodFilter($query, $period);
        }

        $analytics = $query->get();

        return [
            'total_clicks' => $analytics->count(),
            'unique_visitors' => $analytics->unique('ip_address')->count(),
            'countries' => $this->getCountryStats($analytics),
            'devices' => $this->getDeviceStats($analytics),
            'browsers' => $this->getBrowserStats($analytics),
            'platforms' => $this->getPlatformStats($analytics),
            'referrers' => $this->getReferrerStats($analytics),
            'daily_clicks' => $this->getDailyClickStats($url, $period),
        ];
    }

    /**
     * Apply period filter to query.
     */
    private function applyPeriodFilter($query, string $period)
    {
        switch ($period) {
            case 'today':
                return $query->whereDate('clicked_at', today());
            case 'week':
                return $query->whereBetween('clicked_at', [now()->startOfWeek(), now()->endOfWeek()]);
            case 'month':
                return $query->whereMonth('clicked_at', now()->month);
            case 'year':
                return $query->whereYear('clicked_at', now()->year);
            default:
                return $query;
        }
    }

    /**
     * Get country statistics.
     */
    private function getCountryStats($analytics): array
    {
        return $analytics->whereNotNull('country_name')
            ->groupBy('country_name')
            ->map(function ($group) {
                return [
                    'country' => $group->first()->country_name,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get device statistics.
     */
    private function getDeviceStats($analytics): array
    {
        return $analytics->whereNotNull('device_type')
            ->groupBy('device_type')
            ->map(function ($group) use ($analytics) {
                return [
                    'device' => $group->first()->device_type,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->values()
            ->toArray();
    }

    /**
     * Get browser statistics.
     */
    private function getBrowserStats($analytics): array
    {
        return $analytics->whereNotNull('browser')
            ->groupBy('browser')
            ->map(function ($group) use ($analytics) {
                return [
                    'browser' => $group->first()->browser,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get platform statistics.
     */
    private function getPlatformStats($analytics): array
    {
        return $analytics->whereNotNull('platform')
            ->groupBy('platform')
            ->map(function ($group) use ($analytics) {
                return [
                    'platform' => $group->first()->platform,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get referrer statistics.
     */
    private function getReferrerStats($analytics): array
    {
        return $analytics->whereNotNull('referer')
            ->groupBy('referer')
            ->map(function ($group) use ($analytics) {
                $referer = $group->first()->referer;
                $domain = parse_url($referer, PHP_URL_HOST) ?? $referer;
                
                return [
                    'referrer' => $domain,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get daily click statistics.
     */
    private function getDailyClickStats(Url $url, ?string $period = null): array
    {
        $days = match($period) {
            'week' => 7,
            'month' => 30,
            default => 30,
        };

        $startDate = now()->subDays($days);

        $clicks = $url->analytics()
            ->where('clicked_at', '>=', $startDate)
            ->selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $result = [];
        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $result[] = [
                'date' => $date,
                'clicks' => $clicks->get($date)?->clicks ?? 0,
            ];
        }

        return $result;
    }

    /**
     * Get advanced analytics with filtering options.
     */
    public function getAdvancedAnalytics(Url $url, array $filters = []): array
    {
        $query = $url->analytics();

        // Apply filters
        if (!empty($filters['start_date'])) {
            $query->where('clicked_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('clicked_at', '<=', $filters['end_date']);
        }

        if (!empty($filters['country'])) {
            $query->where('country', $filters['country']);
        }

        if (!empty($filters['device_type'])) {
            $query->where('device_type', $filters['device_type']);
        }

        if (!empty($filters['browser'])) {
            $query->where('browser', $filters['browser']);
        }

        if (isset($filters['is_bot'])) {
            $query->where('is_bot', $filters['is_bot']);
        }

        $analytics = $query->get();

        return [
            'total_clicks' => $analytics->count(),
            'unique_visitors' => $analytics->unique('ip_address')->count(),
            'filtered_results' => $analytics->count(),
            'countries' => $this->getCountryStats($analytics),
            'devices' => $this->getDeviceStats($analytics),
            'browsers' => $this->getBrowserStats($analytics),
            'platforms' => $this->getPlatformStats($analytics),
            'referrers' => $this->getReferrerStats($analytics),
            'hourly_distribution' => $this->getHourlyDistribution($analytics),
            'weekly_distribution' => $this->getWeeklyDistribution($analytics),
            'top_cities' => $this->getTopCities($analytics),
            'bot_traffic' => $this->getBotTrafficStats($analytics),
        ];
    }

    /**
     * Get hourly click distribution.
     */
    private function getHourlyDistribution($analytics): array
    {
        $hourlyStats = [];
        for ($i = 0; $i < 24; $i++) {
            $hourlyStats[$i] = 0;
        }

        foreach ($analytics as $analytic) {
            $hour = (int) $analytic->clicked_at->format('H');
            $hourlyStats[$hour]++;
        }

        return array_map(function($hour, $count) {
            return [
                'hour' => sprintf('%02d:00', $hour),
                'count' => $count,
            ];
        }, array_keys($hourlyStats), $hourlyStats);
    }

    /**
     * Get weekly click distribution.
     */
    private function getWeeklyDistribution($analytics): array
    {
        $weeklyStats = [
            'Monday' => 0,
            'Tuesday' => 0,
            'Wednesday' => 0,
            'Thursday' => 0,
            'Friday' => 0,
            'Saturday' => 0,
            'Sunday' => 0,
        ];

        foreach ($analytics as $analytic) {
            $dayName = $analytic->clicked_at->format('l');
            $weeklyStats[$dayName]++;
        }

        return array_map(function($day, $count) {
            return [
                'day' => $day,
                'count' => $count,
            ];
        }, array_keys($weeklyStats), $weeklyStats);
    }

    /**
     * Get top cities.
     */
    private function getTopCities($analytics): array
    {
        return $analytics->whereNotNull('city')
            ->groupBy('city')
            ->map(function ($group) use ($analytics) {
                return [
                    'city' => $group->first()->city,
                    'country' => $group->first()->country_name,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $analytics->count()) * 100, 1),
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * Get bot traffic statistics.
     */
    private function getBotTrafficStats($analytics): array
    {
        $botClicks = $analytics->where('is_bot', true)->count();
        $humanClicks = $analytics->where('is_bot', false)->count();
        $totalClicks = $analytics->count();

        return [
            'bot_clicks' => $botClicks,
            'human_clicks' => $humanClicks,
            'total_clicks' => $totalClicks,
            'bot_percentage' => $totalClicks > 0 ? round(($botClicks / $totalClicks) * 100, 1) : 0,
            'human_percentage' => $totalClicks > 0 ? round(($humanClicks / $totalClicks) * 100, 1) : 0,
        ];
    }

    /**
     * Generate analytics report for export.
     */
    public function generateReport(Url $url, array $options = []): array
    {
        $format = $options['format'] ?? 'array';
        $period = $options['period'] ?? 'month';
        $includeRawData = $options['include_raw_data'] ?? false;

        $analytics = $this->getAdvancedAnalytics($url, $options['filters'] ?? []);

        $report = [
            'url_info' => [
                'id' => $url->id,
                'original_url' => $url->original_url,
                'short_url' => $url->short_url,
                'title' => $url->title,
                'created_at' => $url->created_at->toISOString(),
                'total_clicks' => $url->click_count,
            ],
            'report_info' => [
                'generated_at' => now()->toISOString(),
                'period' => $period,
                'filters_applied' => $options['filters'] ?? [],
            ],
            'analytics' => $analytics,
        ];

        if ($includeRawData) {
            $report['raw_data'] = $url->analytics()
                ->when(!empty($options['filters']), function($query) use ($options) {
                    foreach ($options['filters'] as $field => $value) {
                        if (!empty($value)) {
                            $query->where($field, $value);
                        }
                    }
                })
                ->get()
                ->toArray();
        }

        return $report;
    }

    /**
     * Get real-time analytics (last 24 hours with hourly breakdown).
     */
    public function getRealTimeAnalytics(Url $url): array
    {
        $last24Hours = now()->subHours(24);

        $recentClicks = $url->analytics()
            ->where('clicked_at', '>=', $last24Hours)
            ->orderBy('clicked_at', 'desc')
            ->get();

        $hourlyBreakdown = [];
        for ($i = 23; $i >= 0; $i--) {
            $hour = now()->subHours($i);
            $hourStart = $hour->format('Y-m-d H:00:00');
            $hourEnd = $hour->format('Y-m-d H:59:59');

            $clicksInHour = $recentClicks->whereBetween('clicked_at', [$hourStart, $hourEnd])->count();

            $hourlyBreakdown[] = [
                'hour' => $hour->format('H:00'),
                'datetime' => $hour->toISOString(),
                'clicks' => $clicksInHour,
            ];
        }

        return [
            'total_clicks_24h' => $recentClicks->count(),
            'unique_visitors_24h' => $recentClicks->unique('ip_address')->count(),
            'hourly_breakdown' => $hourlyBreakdown,
            'recent_clicks' => $recentClicks->take(10)->map(function($click) {
                return [
                    'clicked_at' => $click->clicked_at->toISOString(),
                    'country' => $click->country_name,
                    'city' => $click->city,
                    'device' => $click->device_type,
                    'browser' => $click->browser,
                    'is_bot' => $click->is_bot,
                ];
            })->toArray(),
            'top_countries_24h' => $this->getCountryStats($recentClicks),
            'top_devices_24h' => $this->getDeviceStats($recentClicks),
        ];
    }

    /**
     * Compare analytics between two periods.
     */
    public function comparePerformance(Url $url, string $currentPeriod, string $previousPeriod): array
    {
        $currentAnalytics = $this->getUrlAnalytics($url, $currentPeriod);
        $previousAnalytics = $this->getUrlAnalytics($url, $previousPeriod);

        $comparison = [];
        $metrics = ['total_clicks', 'unique_visitors'];

        foreach ($metrics as $metric) {
            $current = $currentAnalytics[$metric] ?? 0;
            $previous = $previousAnalytics[$metric] ?? 0;

            $change = $current - $previous;
            $percentageChange = $previous > 0 ? round(($change / $previous) * 100, 1) : 0;

            $comparison[$metric] = [
                'current' => $current,
                'previous' => $previous,
                'change' => $change,
                'percentage_change' => $percentageChange,
                'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable'),
            ];
        }

        return $comparison;
    }
}
