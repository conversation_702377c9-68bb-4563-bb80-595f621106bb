<?php

namespace App\Services;

use App\Models\SeoSetting;
use App\Models\Url;
use Illuminate\Support\Facades\View;

class SeoService
{
    /**
     * Generate meta tags for a page.
     */
    public function generateMetaTags(array $data = []): array
    {
        $defaults = [
            'title' => SeoSetting::get('site_title'),
            'description' => SeoSetting::get('site_description'),
            'keywords' => SeoSetting::get('site_keywords'),
            'author' => SeoSetting::get('site_author'),
            'canonical' => request()->url(),
            'image' => asset(SeoSetting::get('og_image', '/images/og-image.jpg')),
        ];

        $meta = array_merge($defaults, $data);

        return [
            'title' => $meta['title'],
            'description' => $meta['description'],
            'keywords' => $meta['keywords'],
            'author' => $meta['author'],
            'canonical' => $meta['canonical'],
            'robots' => $meta['robots'] ?? 'index,follow',
            'image' => $meta['image'],
        ];
    }

    /**
     * Generate Open Graph tags.
     */
    public function generateOpenGraphTags(array $data = []): array
    {
        $defaults = [
            'title' => SeoSetting::get('site_title'),
            'description' => SeoSetting::get('site_description'),
            'site_name' => SeoSetting::get('og_site_name'),
            'type' => 'website',
            'url' => request()->url(),
            'image' => asset(SeoSetting::get('og_image', '/images/og-image.jpg')),
            'image:width' => SeoSetting::get('og_image_width', '1200'),
            'image:height' => SeoSetting::get('og_image_height', '630'),
        ];

        return array_merge($defaults, $data);
    }

    /**
     * Generate Twitter Card tags.
     */
    public function generateTwitterCardTags(array $data = []): array
    {
        $defaults = [
            'card' => SeoSetting::get('twitter_card', 'summary_large_image'),
            'site' => SeoSetting::get('twitter_site'),
            'creator' => SeoSetting::get('twitter_creator'),
            'title' => SeoSetting::get('site_title'),
            'description' => SeoSetting::get('site_description'),
            'image' => asset(SeoSetting::get('og_image', '/images/og-image.jpg')),
        ];

        return array_merge($defaults, $data);
    }

    /**
     * Generate Schema.org markup for URL shortener.
     */
    public function generateSchemaMarkup(array $data = []): array
    {
        $baseSchema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebApplication',
            'name' => SeoSetting::get('site_title'),
            'description' => SeoSetting::get('site_description'),
            'url' => config('app.url'),
            'applicationCategory' => 'UtilitiesApplication',
            'operatingSystem' => 'Web',
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD',
            ],
            'author' => [
                '@type' => 'Organization',
                'name' => SeoSetting::get('site_author'),
            ],
        ];

        return array_merge($baseSchema, $data);
    }

    /**
     * Generate SEO data for URL redirect page.
     */
    public function generateUrlSeoData(Url $url): array
    {
        $title = $url->title
            ? "{$url->title} - Minilink.at"
            : "Redirecting to {$url->original_url} - Minilink.at";

        $description = $url->description
            ? $url->description
            : "This short link redirects to {$url->original_url}. Created with Minilink.at URL shortener.";

        return [
            'title' => $title,
            'description' => $description,
            'canonical' => $url->short_url,
            'robots' => 'noindex,nofollow', // Don't index redirect pages
            'og:type' => 'article',
            'og:url' => $url->short_url,
            'schema' => [
                '@context' => 'https://schema.org',
                '@type' => 'WebPage',
                'name' => $title,
                'description' => $description,
                'url' => $url->short_url,
                'mainEntity' => [
                    '@type' => 'URL',
                    'url' => $url->original_url,
                ],
            ],
        ];
    }

    /**
     * Generate analytics tracking codes.
     */
    public function generateAnalyticsCodes(): array
    {
        $codes = [];

        // Google Analytics 4
        if ($gaId = SeoSetting::get('google_analytics_id')) {
            $codes['google_analytics'] = $gaId;
        }

        // Google Tag Manager
        if ($gtmId = SeoSetting::get('google_tag_manager_id')) {
            $codes['google_tag_manager'] = $gtmId;
        }

        // Facebook Pixel
        if ($fbPixelId = SeoSetting::get('facebook_pixel_id')) {
            $codes['facebook_pixel'] = $fbPixelId;
        }

        return $codes;
    }

    /**
     * Generate verification meta tags.
     */
    public function generateVerificationTags(): array
    {
        $tags = [];

        if ($googleVerification = SeoSetting::get('google_site_verification')) {
            $tags['google-site-verification'] = $googleVerification;
        }

        if ($bingVerification = SeoSetting::get('bing_site_verification')) {
            $tags['msvalidate.01'] = $bingVerification;
        }

        if ($facebookVerification = SeoSetting::get('facebook_domain_verification')) {
            $tags['facebook-domain-verification'] = $facebookVerification;
        }

        return $tags;
    }

    /**
     * Generate robots.txt content.
     */
    public function generateRobotsTxt(): string
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /dashboard/\n";
        $content .= "Disallow: /profile/\n";
        $content .= "Disallow: /api/\n";
        $content .= "\n";
        
        if (SeoSetting::get('enable_sitemap', true)) {
            $content .= "Sitemap: " . config('app.url') . "/sitemap.xml\n";
        }

        return $content;
    }

    /**
     * Get sitemap URLs.
     */
    public function getSitemapUrls(): array
    {
        $urls = [];

        // Add main pages
        $urls[] = [
            'url' => config('app.url'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0',
        ];

        $urls[] = [
            'url' => config('app.url') . '/login',
            'lastmod' => now()->toISOString(),
            'changefreq' => 'monthly',
            'priority' => '0.5',
        ];

        $urls[] = [
            'url' => config('app.url') . '/register',
            'lastmod' => now()->toISOString(),
            'changefreq' => 'monthly',
            'priority' => '0.5',
        ];

        // Add public short URLs (if any are marked as public)
        // This would require adding a 'is_public' field to URLs table
        // For now, we'll skip this to avoid exposing private URLs

        return $urls;
    }

    /**
     * Share SEO data with all views.
     */
    public function shareWithViews(): void
    {
        View::composer('*', function ($view) {
            $view->with([
                'seoMeta' => $this->generateMetaTags(),
                'seoOg' => $this->generateOpenGraphTags(),
                'seoTwitter' => $this->generateTwitterCardTags(),
                'seoSchema' => $this->generateSchemaMarkup(),
                'seoAnalytics' => $this->generateAnalyticsCodes(),
                'seoVerification' => $this->generateVerificationTags(),
            ]);
        });
    }
}
