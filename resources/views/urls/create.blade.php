@extends('layouts.dashboard-template')

@section('page_title', 'Create New Link - Minilink.at')
@section('page_heading', 'Create New Link')
@section('page_description', 'Transform your long URL into a short, shareable link')

@section('page_actions')
    <a href="{{ route('urls.manage') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Links
    </a>
    <a href="{{ route('dashboard') }}" class="btn-outline">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
    </a>
@endsection

@section('main_content')

    <!-- Create Form -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Link Details</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('urls.store') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Original URL -->
                <div>
                    <label for="original_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Original URL <span class="text-red-500">*</span>
                    </label>
                    <input type="url" 
                           id="original_url" 
                           name="original_url" 
                           class="input-field @error('original_url') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                           placeholder="https://example.com/very-long-url"
                           value="{{ old('original_url') }}"
                           required>
                    @error('original_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Enter the long URL you want to shorten
                    </p>
                </div>

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Title (Optional)
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="input-field @error('title') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                           placeholder="My Awesome Link"
                           value="{{ old('title') }}">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        A descriptive title to help you identify this link
                    </p>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description (Optional)
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="input-field @error('description') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                              placeholder="Brief description of your link">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Optional description for better organization
                    </p>
                </div>

                <!-- Custom Alias -->
                <div>
                    <label for="custom_alias" class="block text-sm font-medium text-gray-700 mb-2">
                        Custom Alias (Optional)
                    </label>
                    <div class="flex max-w-2xl">
                        <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm whitespace-nowrap">
                            {{ config('app.url') }}/
                        </span>
                        <input type="text"
                               id="custom_alias"
                               name="custom_alias"
                               class="input-field rounded-l-none flex-1 min-w-0 @error('custom_alias') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               placeholder="my-custom-link"
                               value="{{ old('custom_alias') }}">
                    </div>
                    @error('custom_alias')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Leave empty for auto-generated short code. Only letters, numbers, and hyphens allowed.
                    </p>
                </div>

                <!-- Advanced Options -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Advanced Options</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Expiration Date -->
                        <div>
                            <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">
                                Expiration Date (Optional)
                            </label>
                            <input type="datetime-local" 
                                   id="expires_at" 
                                   name="expires_at" 
                                   class="input-field @error('expires_at') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                   value="{{ old('expires_at') }}">
                            @error('expires_at')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Link will stop working after this date
                            </p>
                        </div>

                        <!-- Password Protection -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Password Protection (Optional)
                            </label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   class="input-field @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                   placeholder="Enter password">
                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Visitors will need this password to access the link
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Custom Domain (Premium Feature) -->
                @if(Auth::user()->subscription?->custom_domains && Auth::user()->customDomains->count() > 0)
                <div class="border-t border-gray-200 pt-6">
                    <label for="custom_domain_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Custom Domain (Optional)
                    </label>
                    <select id="custom_domain_id" 
                            name="custom_domain_id" 
                            class="input-field @error('custom_domain_id') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                        <option value="">Use default domain ({{ config('app.url') }})</option>
                        @foreach(Auth::user()->customDomains as $domain)
                            <option value="{{ $domain->id }}" {{ old('custom_domain_id') == $domain->id ? 'selected' : '' }}>
                                {{ $domain->domain }}
                            </option>
                        @endforeach
                    </select>
                    @error('custom_domain_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Use your custom domain for branded links
                    </p>
                </div>
                @endif

                <!-- QR Code Generation -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="generate_qr" 
                               name="generate_qr" 
                               value="1"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                               {{ old('generate_qr') ? 'checked' : '' }}>
                        <label for="generate_qr" class="ml-2 block text-sm text-gray-700">
                            Generate QR Code
                        </label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 ml-6">
                        Automatically generate a QR code for this link
                    </p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('urls.manage') }}" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-magic mr-2"></i>Create Short Link
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tips Card -->
    <div class="card mt-6">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>Tips for Better Links
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Custom Aliases</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Use descriptive names like "summer-sale-2024"</li>
                        <li>• Keep them short and memorable</li>
                        <li>• Use hyphens instead of spaces</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Best Practices</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Add titles and descriptions for organization</li>
                        <li>• Set expiration dates for time-sensitive content</li>
                        <li>• Use password protection for sensitive links</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Custom alias validation
document.getElementById('custom_alias').addEventListener('input', function() {
    let value = this.value;
    // Remove invalid characters
    value = value.replace(/[^a-zA-Z0-9-]/g, '');
    // Update the input
    this.value = value;
});

// URL validation
document.getElementById('original_url').addEventListener('blur', function() {
    const url = this.value;
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        this.value = 'https://' + url;
    }
});
</script>
@endpush
