#!/bin/bash

# Minilink.at Production Deployment Script
# This script handles the deployment of the Minilink.at application to production

set -e  # Exit on any error

# Configuration
APP_NAME="minilink-at"
APP_DIR="/var/www/minilink-at"
BACKUP_DIR="/var/backups/minilink-at"
LOG_FILE="/var/log/minilink-at-deploy.log"
PHP_VERSION="8.2"
NODE_VERSION="18"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check PHP version
    if ! command -v php &> /dev/null; then
        error "PHP is not installed"
    fi
    
    PHP_CURRENT=$(php -r "echo PHP_MAJOR_VERSION.'.'.PHP_MINOR_VERSION;")
    if [[ "$PHP_CURRENT" < "$PHP_VERSION" ]]; then
        error "PHP $PHP_VERSION or higher is required. Current: $PHP_CURRENT"
    fi
    
    # Check Composer
    if ! command -v composer &> /dev/null; then
        error "Composer is not installed"
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    # Check required PHP extensions
    REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "mbstring" "openssl" "tokenizer" "xml" "ctype" "json" "bcmath" "curl" "gd")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            error "Required PHP extension '$ext' is not installed"
        fi
    done
    
    success "System requirements check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$TIMESTAMP"
    
    mkdir -p $BACKUP_PATH
    
    # Backup application files
    if [ -d "$APP_DIR" ]; then
        cp -r $APP_DIR $BACKUP_PATH/app
        success "Application files backed up to $BACKUP_PATH/app"
    fi
    
    # Backup database
    if [ ! -z "$DB_DATABASE" ]; then
        mysqldump -u$DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > $BACKUP_PATH/database.sql
        success "Database backed up to $BACKUP_PATH/database.sql"
    fi
    
    # Keep only last 5 backups
    cd $BACKUP_DIR
    ls -t | tail -n +6 | xargs -r rm -rf
    
    success "Backup created successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    # Create application directory if it doesn't exist
    mkdir -p $APP_DIR
    cd $APP_DIR
    
    # Clone or pull latest code
    if [ -d ".git" ]; then
        log "Pulling latest changes..."
        git pull origin main
    else
        log "Cloning repository..."
        git clone https://github.com/yourusername/minilink-at.git .
    fi
    
    # Install PHP dependencies
    log "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    # Install Node.js dependencies
    log "Installing Node.js dependencies..."
    npm ci --production
    
    # Build assets
    log "Building assets..."
    npm run build
    
    success "Application deployed successfully"
}

# Configure environment
configure_environment() {
    log "Configuring environment..."
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.production" ]; then
            cp .env.production .env
        else
            cp .env.example .env
            warning "Please configure .env file with production settings"
        fi
    fi
    
    # Generate application key if not set
    if ! grep -q "APP_KEY=base64:" .env; then
        php artisan key:generate --force
    fi
    
    # Set proper permissions
    chmod -R 755 storage bootstrap/cache
    chown -R www-data:www-data storage bootstrap/cache
    
    success "Environment configured successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Check database connection
    php artisan migrate:status || error "Cannot connect to database"
    
    # Run migrations
    php artisan migrate --force
    
    # Seed database if needed
    if [ "$1" == "--seed" ]; then
        php artisan db:seed --force
    fi
    
    success "Database migrations completed"
}

# Optimize application
optimize_application() {
    log "Optimizing application..."
    
    # Clear all caches
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    # Cache configuration and routes
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # Optimize autoloader
    php artisan optimize
    
    # Create symbolic link for storage
    php artisan storage:link
    
    success "Application optimized successfully"
}

# Configure web server
configure_webserver() {
    log "Configuring web server..."
    
    # Nginx configuration
    if command -v nginx &> /dev/null; then
        NGINX_CONFIG="/etc/nginx/sites-available/$APP_NAME"
        
        cat > $NGINX_CONFIG << EOF
server {
    listen 80;
    listen [::]:80;
    server_name minilink.at www.minilink.at;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name minilink.at www.minilink.at;
    root $APP_DIR/public;

    index index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/minilink.at/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/minilink.at/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php$PHP_VERSION-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
EOF

        # Enable site
        ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
        nginx -t && systemctl reload nginx
        
        success "Nginx configured successfully"
    fi
}

# Setup SSL certificate
setup_ssl() {
    log "Setting up SSL certificate..."
    
    if command -v certbot &> /dev/null; then
        certbot --nginx -d minilink.at -d www.minilink.at --non-interactive --agree-tos --email <EMAIL>
        success "SSL certificate configured successfully"
    else
        warning "Certbot not found. Please install SSL certificate manually"
    fi
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Create log rotation
    cat > /etc/logrotate.d/$APP_NAME << EOF
$APP_DIR/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF

    # Setup cron jobs
    (crontab -l 2>/dev/null; echo "0 2 * * * cd $APP_DIR && php artisan schedule:run >> /dev/null 2>&1") | crontab -
    
    success "Monitoring configured successfully"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if application is responding
    if curl -f -s http://localhost > /dev/null; then
        success "Application is responding"
    else
        error "Application is not responding"
    fi
    
    # Check database connection
    if php artisan migrate:status > /dev/null 2>&1; then
        success "Database connection is working"
    else
        error "Database connection failed"
    fi
    
    # Check storage permissions
    if [ -w "$APP_DIR/storage" ]; then
        success "Storage directory is writable"
    else
        error "Storage directory is not writable"
    fi
}

# Main deployment function
main() {
    log "Starting Minilink.at deployment..."
    
    check_root
    check_requirements
    create_backup
    deploy_application
    configure_environment
    run_migrations $1
    optimize_application
    configure_webserver
    setup_ssl
    setup_monitoring
    health_check
    
    success "Deployment completed successfully!"
    log "Application is now available at https://minilink.at"
}

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "deploy-with-seed")
        main --seed
        ;;
    "backup")
        create_backup
        ;;
    "optimize")
        optimize_application
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {deploy|deploy-with-seed|backup|optimize|health}"
        exit 1
        ;;
esac
