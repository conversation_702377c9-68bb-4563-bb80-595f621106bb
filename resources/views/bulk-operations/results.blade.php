@extends('layouts.modern')

@section('title', 'Bulk Operation Results - Minilink.at')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Bulk Operation Results</h1>
                <p class="text-gray-600 mt-1">Review the results of your bulk operation</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Operations
                </a>
                <a href="{{ route('dashboard') }}" class="btn-outline">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Operation Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Total Processed -->
        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-list text-blue-600 text-xl"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900">{{ $results['total'] ?? 0 }}</div>
                <div class="text-sm text-gray-600">Total Processed</div>
            </div>
        </div>

        <!-- Successful -->
        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <div class="text-2xl font-bold text-green-600">{{ $results['successful'] ?? 0 }}</div>
                <div class="text-sm text-gray-600">Successful</div>
            </div>
        </div>

        <!-- Failed -->
        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-times text-red-600 text-xl"></i>
                </div>
                <div class="text-2xl font-bold text-red-600">{{ $results['failed'] ?? 0 }}</div>
                <div class="text-sm text-gray-600">Failed</div>
            </div>
        </div>

        <!-- Success Rate -->
        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-percentage text-purple-600 text-xl"></i>
                </div>
                <div class="text-2xl font-bold text-purple-600">
                    {{ $results['total'] > 0 ? round(($results['successful'] / $results['total']) * 100, 1) : 0 }}%
                </div>
                <div class="text-sm text-gray-600">Success Rate</div>
            </div>
        </div>
    </div>

    <!-- Operation Details -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Operation Details</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Operation Type</dt>
                            <dd class="text-sm text-gray-900">{{ ucfirst($results['type'] ?? 'Unknown') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Started At</dt>
                            <dd class="text-sm text-gray-900">{{ $results['started_at'] ?? 'Unknown' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Completed At</dt>
                            <dd class="text-sm text-gray-900">{{ $results['completed_at'] ?? 'Unknown' }}</dd>
                        </div>
                    </dl>
                </div>
                <div>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Processing Time</dt>
                            <dd class="text-sm text-gray-900">{{ $results['processing_time'] ?? 'Unknown' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Batch ID</dt>
                            <dd class="text-sm text-gray-900 font-mono">{{ $results['batch_id'] ?? 'Unknown' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-600">Status</dt>
                            <dd class="text-sm">
                                @if(($results['status'] ?? '') === 'completed')
                                    <span class="badge badge-success">Completed</span>
                                @elseif(($results['status'] ?? '') === 'failed')
                                    <span class="badge badge-danger">Failed</span>
                                @else
                                    <span class="badge badge-secondary">{{ ucfirst($results['status'] ?? 'Unknown') }}</span>
                                @endif
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Tabs -->
    <div class="card">
        <div class="card-header">
            <div class="flex space-x-4">
                <button onclick="showTab('successful')" id="successful-tab" class="tab-button active">
                    <i class="fas fa-check mr-2"></i>Successful ({{ $results['successful'] ?? 0 }})
                </button>
                @if(($results['failed'] ?? 0) > 0)
                <button onclick="showTab('failed')" id="failed-tab" class="tab-button">
                    <i class="fas fa-times mr-2"></i>Failed ({{ $results['failed'] ?? 0 }})
                </button>
                @endif
                @if(($results['skipped'] ?? 0) > 0)
                <button onclick="showTab('skipped')" id="skipped-tab" class="tab-button">
                    <i class="fas fa-skip-forward mr-2"></i>Skipped ({{ $results['skipped'] ?? 0 }})
                </button>
                @endif
            </div>
        </div>
        <div class="card-body p-0">
            <!-- Successful URLs -->
            <div id="successful-content" class="tab-content">
                @if(isset($results['successful_urls']) && count($results['successful_urls']) > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($results['successful_urls'] as $url)
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900 truncate">
                                        {{ $url['title'] ?? 'Untitled' }}
                                    </div>
                                    <div class="text-sm text-blue-600 font-mono">{{ $url['short_url'] ?? '' }}</div>
                                    <div class="text-xs text-gray-500 truncate">{{ $url['original_url'] ?? '' }}</div>
                                </div>
                                <div class="flex-shrink-0 ml-4">
                                    <span class="badge badge-success">
                                        <i class="fas fa-check mr-1"></i>Created
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-check text-gray-400 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Successful URLs</h3>
                        <p class="text-gray-600">No URLs were successfully processed in this operation.</p>
                    </div>
                @endif
            </div>

            <!-- Failed URLs -->
            @if(($results['failed'] ?? 0) > 0)
            <div id="failed-content" class="tab-content hidden">
                @if(isset($results['failed_urls']) && count($results['failed_urls']) > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($results['failed_urls'] as $url)
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900 truncate">
                                        {{ $url['original_url'] ?? 'Unknown URL' }}
                                    </div>
                                    <div class="text-sm text-red-600">{{ $url['error'] ?? 'Unknown error' }}</div>
                                </div>
                                <div class="flex-shrink-0 ml-4">
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times mr-1"></i>Failed
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @endif
            </div>
            @endif

            <!-- Skipped URLs -->
            @if(($results['skipped'] ?? 0) > 0)
            <div id="skipped-content" class="tab-content hidden">
                @if(isset($results['skipped_urls']) && count($results['skipped_urls']) > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($results['skipped_urls'] as $url)
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900 truncate">
                                        {{ $url['original_url'] ?? 'Unknown URL' }}
                                    </div>
                                    <div class="text-sm text-yellow-600">{{ $url['reason'] ?? 'Already exists' }}</div>
                                </div>
                                <div class="flex-shrink-0 ml-4">
                                    <span class="badge badge-warning">
                                        <i class="fas fa-skip-forward mr-1"></i>Skipped
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @endif
            </div>
            @endif
        </div>
    </div>

    <!-- Actions -->
    <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
        @if(($results['successful'] ?? 0) > 0)
        <form action="{{ route('bulk-operations.export') }}" method="POST" class="inline">
            @csrf
            <input type="hidden" name="batch_id" value="{{ $results['batch_id'] ?? '' }}">
            <button type="submit" class="btn-primary">
                <i class="fas fa-download mr-2"></i>Export Results
            </button>
        </form>
        @endif
        
        <a href="{{ route('bulk-operations.create') }}" class="btn-secondary">
            <i class="fas fa-plus mr-2"></i>New Bulk Operation
        </a>
        
        <a href="{{ route('urls.manage') }}" class="btn-outline">
            <i class="fas fa-link mr-2"></i>Manage URLs
        </a>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // Add active class to selected tab
    document.getElementById(tabName + '-tab').classList.add('active');
}
</script>

<style>
.tab-button {
    @apply px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300 transition-colors;
}

.tab-button.active {
    @apply text-blue-600 border-blue-500;
}
</style>
@endpush
