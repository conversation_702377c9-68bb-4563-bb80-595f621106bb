<?php

namespace App\Http\Controllers;

use App\Models\Url;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class UserUrlController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the user's URLs with advanced filtering.
     */
    public function index(Request $request): View
    {
        $user = Auth::user();
        $query = $user->urls()->with(['analytics' => function($query) {
            $query->selectRaw('url_id, COUNT(*) as total_clicks')
                ->groupBy('url_id');
        }]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('original_url', 'like', "%{$search}%")
                  ->orWhere('custom_alias', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            switch ($request->get('status')) {
                case 'active':
                    $query->where('is_active', true)
                          ->where(function($q) {
                              $q->whereNull('expires_at')
                                ->orWhere('expires_at', '>', now());
                          });
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
                case 'password_protected':
                    $query->whereNotNull('password');
                    break;
            }
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'title', 'click_count', 'original_url'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $urls = $query->paginate(20)->withQueryString();

        // Get filter counts for UI
        $filterCounts = $this->getFilterCounts($user);

        return view('dashboard.urls.index', compact('urls', 'filterCounts'));
    }

    /**
     * Get counts for different URL filters.
     */
    private function getFilterCounts($user): array
    {
        $userUrls = $user->urls();
        
        return [
            'all' => $userUrls->count(),
            'active' => $userUrls->where('is_active', true)
                               ->where(function($q) {
                                   $q->whereNull('expires_at')
                                     ->orWhere('expires_at', '>', now());
                               })->count(),
            'inactive' => $userUrls->where('is_active', false)->count(),
            'expired' => $userUrls->where('expires_at', '<', now())->count(),
            'password_protected' => $userUrls->whereNotNull('password')->count(),
        ];
    }

    /**
     * Bulk operations on URLs.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate,export',
            'url_ids' => 'required|array',
            'url_ids.*' => 'exists:urls,id',
        ]);

        $user = Auth::user();
        $urlIds = $request->get('url_ids');
        
        // Verify user owns all URLs
        $urls = Url::whereIn('id', $urlIds)
                   ->where('user_id', $user->id)
                   ->get();

        if ($urls->count() !== count($urlIds)) {
            return response()->json([
                'success' => false,
                'message' => 'You can only perform actions on your own URLs.'
            ], 403);
        }

        $action = $request->get('action');
        $affectedCount = 0;

        switch ($action) {
            case 'delete':
                $affectedCount = $urls->count();
                Url::whereIn('id', $urlIds)->delete();
                break;
                
            case 'activate':
                $affectedCount = Url::whereIn('id', $urlIds)
                                   ->update(['is_active' => true]);
                break;
                
            case 'deactivate':
                $affectedCount = Url::whereIn('id', $urlIds)
                                   ->update(['is_active' => false]);
                break;
                
            case 'export':
                return $this->exportUrls($urls);
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully {$action}d {$affectedCount} URL(s).",
            'affected_count' => $affectedCount,
        ]);
    }

    /**
     * Export URLs to CSV.
     */
    private function exportUrls($urls): JsonResponse
    {
        $csvData = [];
        $csvData[] = ['Title', 'Original URL', 'Short URL', 'Clicks', 'Created', 'Status'];

        foreach ($urls as $url) {
            $status = 'Active';
            if (!$url->is_active) {
                $status = 'Inactive';
            } elseif ($url->isExpired()) {
                $status = 'Expired';
            }

            $csvData[] = [
                $url->title ?: 'Untitled',
                $url->original_url,
                $url->short_url,
                $url->click_count,
                $url->created_at->format('Y-m-d H:i:s'),
                $status,
            ];
        }

        $filename = 'urls_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $filePath = storage_path('app/exports/' . $filename);
        
        // Ensure directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        $file = fopen($filePath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return response()->json([
            'success' => true,
            'download_url' => route('dashboard.urls.download-export', ['filename' => $filename]),
            'message' => 'Export generated successfully.',
        ]);
    }

    /**
     * Download exported CSV file.
     */
    public function downloadExport(Request $request, string $filename): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filePath = storage_path('app/exports/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'Export file not found.');
        }

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Get URL statistics for dashboard widgets.
     */
    public function getStats(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 'week'); // week, month, year
        
        $startDate = match($period) {
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'year' => now()->subYear(),
            default => now()->subWeek(),
        };

        $stats = [
            'total_urls' => $user->urls()->count(),
            'total_clicks' => $user->urls()->sum('click_count'),
            'urls_in_period' => $user->urls()->where('created_at', '>=', $startDate)->count(),
            'clicks_in_period' => $user->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->where('analytics.clicked_at', '>=', $startDate)
                ->count(),
            'top_performing' => $user->urls()
                ->orderBy('click_count', 'desc')
                ->limit(5)
                ->get(['id', 'title', 'short_code', 'click_count']),
        ];

        return response()->json($stats);
    }
}
