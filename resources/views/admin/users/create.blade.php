@extends('layouts.dashboard-template')

@section('page_title', 'Create New User - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-user-plus mr-3 text-blue-600"></i>Create New User
@endsection
@section('page_description', 'Add a new user to the system')

@section('page_actions')
    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Users
    </a>
@endsection

@section('main_content')
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">User Information</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.users.store') }}" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-1 text-blue-500"></i>Full Name *
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               class="input-field @error('name') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               value="{{ old('name') }}"
                               placeholder="Enter full name"
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-1 text-green-500"></i>Email Address *
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="input-field @error('email') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               value="{{ old('email') }}"
                               placeholder="Enter email address"
                               required>
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Password Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-1 text-purple-500"></i>Password *
                        </label>
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="input-field @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               placeholder="Enter password"
                               required>
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            Minimum 8 characters with letters and numbers
                        </p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-1 text-purple-500"></i>Confirm Password *
                        </label>
                        <input type="password" 
                               id="password_confirmation" 
                               name="password_confirmation" 
                               class="input-field"
                               placeholder="Confirm password"
                               required>
                    </div>
                </div>

                <!-- User Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">User Settings</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Role -->
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user-tag mr-1 text-orange-500"></i>User Role
                            </label>
                            <select id="role" 
                                    name="role" 
                                    class="input-field @error('role') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                                <option value="user" {{ old('role', 'user') === 'user' ? 'selected' : '' }}>
                                    👤 User
                                </option>
                                <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>
                                    👑 Admin
                                </option>
                            </select>
                            @error('role')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Admin users have full system access
                            </p>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-toggle-on mr-1 text-red-500"></i>Account Status
                            </label>
                            <select id="status" 
                                    name="status" 
                                    class="input-field @error('status') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>
                                    ✅ Active
                                </option>
                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>
                                    ❌ Inactive
                                </option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Email Verification -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Email Verification</h4>
                    
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="email_verified" 
                               name="email_verified" 
                               class="form-checkbox h-4 w-4 text-blue-600"
                               value="1"
                               {{ old('email_verified') ? 'checked' : '' }}>
                        <label for="email_verified" class="ml-2 text-sm text-gray-700">
                            <i class="fas fa-shield-check mr-1 text-green-500"></i>
                            Mark email as verified
                        </label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        If unchecked, the user will need to verify their email address
                    </p>
                </div>

                <!-- Subscription Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Subscription Settings</h4>
                    
                    <div>
                        <label for="subscription_type" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-crown mr-1 text-yellow-500"></i>Subscription Type
                        </label>
                        <select id="subscription_type" 
                                name="subscription_type" 
                                class="input-field @error('subscription_type') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                            <option value="free" {{ old('subscription_type', 'free') === 'free' ? 'selected' : '' }}>
                                🆓 Free Plan
                            </option>
                            <option value="pro" {{ old('subscription_type') === 'pro' ? 'selected' : '' }}>
                                ⭐ Pro Plan
                            </option>
                            <option value="enterprise" {{ old('subscription_type') === 'enterprise' ? 'selected' : '' }}>
                                🏢 Enterprise Plan
                            </option>
                        </select>
                        @error('subscription_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            Determines the user's feature access and limits
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-user-plus mr-2"></i>Create User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Help Section -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-info-circle mr-2 text-blue-500"></i>User Creation Guidelines
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Password Requirements</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Minimum 8 characters long</li>
                        <li>• Must contain letters and numbers</li>
                        <li>• Special characters recommended</li>
                        <li>• Avoid common passwords</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">User Roles</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• <strong>User:</strong> Standard access to URL shortening features</li>
                        <li>• <strong>Admin:</strong> Full system access including user management</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Account Status</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• <strong>Active:</strong> User can log in and use the system</li>
                        <li>• <strong>Inactive:</strong> User account is disabled</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Subscription Plans</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• <strong>Free:</strong> Basic features with limits</li>
                        <li>• <strong>Pro:</strong> Advanced features and higher limits</li>
                        <li>• <strong>Enterprise:</strong> Full features and custom limits</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    // You can add password strength indicator here if needed
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('password_confirmation').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match!');
        return false;
    }
});
</script>
@endpush
