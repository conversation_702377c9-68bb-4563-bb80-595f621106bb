@extends('layouts.modern')

@section('title', 'Payment - Minilink.at')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Complete Your Subscription</h1>
            <p class="text-lg text-gray-600">Choose your payment method to activate your {{ $subscription->name }} plan</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Payment Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>
                    
                    <form id="payment-form" action="{{ route('subscriptions.process-payment', $subscription) }}" method="POST">
                        @csrf
                        
                        <!-- Payment Method Selection -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">Payment Method</label>
                            <div class="space-y-4">
                                <!-- PayPal Option -->
                                <div class="relative">
                                    <input type="radio" 
                                           id="paypal" 
                                           name="payment_method" 
                                           value="paypal" 
                                           class="sr-only peer"
                                           checked>
                                    <label for="paypal" 
                                           class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all duration-200">
                                        <div class="flex items-center justify-between w-full">
                                            <div class="flex items-center">
                                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fab fa-paypal text-white text-xl"></i>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">PayPal</h3>
                                                    <p class="text-sm text-gray-600">Pay securely with your PayPal account</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                
                                <!-- Credit Card Option -->
                                <div class="relative">
                                    <input type="radio" 
                                           id="credit_card" 
                                           name="payment_method" 
                                           value="credit_card" 
                                           class="sr-only peer">
                                    <label for="credit_card" 
                                           class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all duration-200">
                                        <div class="flex items-center justify-between w-full">
                                            <div class="flex items-center">
                                                <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fas fa-credit-card text-white text-xl"></i>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Credit Card</h3>
                                                    <p class="text-sm text-gray-600">Pay with Visa, Mastercard, or American Express</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Credit Card Form (Hidden by default) -->
                        <div id="credit-card-form" class="hidden mb-8">
                            <div class="grid grid-cols-2 gap-6 mb-6">
                                <div class="col-span-2">
                                    <label for="card_number" class="block text-sm font-medium text-gray-700 mb-2">Card Number</label>
                                    <input type="text" 
                                           id="card_number" 
                                           name="card_number" 
                                           placeholder="1234 5678 9012 3456"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
                                    <input type="text" 
                                           id="expiry_date" 
                                           name="expiry_date" 
                                           placeholder="MM/YY"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="cvv" class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                    <input type="text" 
                                           id="cvv" 
                                           name="cvv" 
                                           placeholder="123"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label for="cardholder_name" class="block text-sm font-medium text-gray-700 mb-2">Cardholder Name</label>
                                <input type="text" 
                                       id="cardholder_name" 
                                       name="cardholder_name" 
                                       placeholder="John Doe"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-8">
                            <label class="flex items-start">
                                <input type="checkbox" 
                                       name="terms_accepted" 
                                       value="1" 
                                       required
                                       class="mt-1 text-blue-600 focus:ring-blue-500 rounded">
                                <span class="ml-3 text-sm text-gray-700">
                                    I agree to the 
                                    <a href="{{ route('terms-of-service') }}" class="text-blue-600 hover:text-blue-700 underline" target="_blank">Terms of Service</a> 
                                    and 
                                    <a href="{{ route('privacy-policy') }}" class="text-blue-600 hover:text-blue-700 underline" target="_blank">Privacy Policy</a>
                                </span>
                            </label>
                        </div>

                        <!-- PayPal Button Container -->
                        <div id="paypal-button-container" class="mb-6"></div>

                        <!-- Submit Button for Credit Card -->
                        <button type="submit" 
                                id="credit-card-submit"
                                class="hidden w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                            <i class="fas fa-lock mr-2"></i>
                            Complete Payment
                        </button>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl border border-gray-200 p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h3>
                    
                    <!-- Plan Details -->
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Plan</span>
                            <span class="font-semibold text-gray-900">{{ $subscription->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Billing</span>
                            <span class="text-gray-900">{{ ucfirst($subscription->billing_cycle) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Price</span>
                            <span class="text-gray-900">${{ number_format($subscription->price, 2) }}</span>
                        </div>
                        @if($subscription->features)
                        <div class="pt-4 border-t border-gray-200">
                            <h4 class="font-medium text-gray-900 mb-2">Included Features</h4>
                            <ul class="space-y-1">
                                @foreach(is_array($subscription->features) ? $subscription->features : json_decode($subscription->features, true) as $feature)
                                <li class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                    {{ $feature }}
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        @endif
                    </div>
                    
                    <!-- Total -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-semibold text-gray-900">Total</span>
                            <span class="text-2xl font-bold text-blue-600">${{ number_format($subscription->price, 2) }}</span>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Billed {{ $subscription->billing_cycle }}</p>
                    </div>
                    
                    <!-- Security Notice -->
                    <div class="mt-6 p-4 bg-green-50 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-800">Secure Payment</span>
                        </div>
                        <p class="text-xs text-green-700 mt-1">Your payment information is encrypted and secure</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ config('services.paypal.client_id') }}&currency=USD"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paypalRadio = document.getElementById('paypal');
    const creditCardRadio = document.getElementById('credit_card');
    const creditCardForm = document.getElementById('credit-card-form');
    const creditCardSubmit = document.getElementById('credit-card-submit');
    const paypalContainer = document.getElementById('paypal-button-container');

    function togglePaymentMethod() {
        if (paypalRadio.checked) {
            creditCardForm.classList.add('hidden');
            creditCardSubmit.classList.add('hidden');
            paypalContainer.classList.remove('hidden');
            initPayPal();
        } else {
            creditCardForm.classList.remove('hidden');
            creditCardSubmit.classList.remove('hidden');
            paypalContainer.classList.add('hidden');
            paypalContainer.innerHTML = '';
        }
    }

    paypalRadio.addEventListener('change', togglePaymentMethod);
    creditCardRadio.addEventListener('change', togglePaymentMethod);

    function initPayPal() {
        paypal.Buttons({
            createOrder: function(data, actions) {
                return actions.order.create({
                    purchase_units: [{
                        amount: {
                            value: '{{ $subscription->price }}'
                        },
                        description: '{{ $subscription->name }} Subscription'
                    }]
                });
            },
            onApprove: function(data, actions) {
                return actions.order.capture().then(function(details) {
                    // Send the payment details to your server
                    fetch('{{ route("subscriptions.paypal-success", $subscription) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            orderID: data.orderID,
                            payerID: data.payerID,
                            details: details
                        })
                    }).then(function(response) {
                        return response.json();
                    }).then(function(data) {
                        if (data.success) {
                            window.location.href = data.redirect_url;
                        } else {
                            alert('Payment processing failed. Please try again.');
                        }
                    });
                });
            },
            onError: function(err) {
                console.error('PayPal error:', err);
                alert('An error occurred with PayPal. Please try again.');
            }
        }).render('#paypal-button-container');
    }

    // Initialize with PayPal selected by default
    togglePaymentMethod();
});
</script>
@endsection
