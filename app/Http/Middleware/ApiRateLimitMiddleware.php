<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $maxAttempts = '1000', string $decayMinutes = '60'): Response
    {
        $user = $request->get('api_user');

        if (!$user) {
            return $next($request);
        }

        $key = $this->resolveRequestSignature($request, $user);
        $maxAttempts = (int) $maxAttempts;
        $decayMinutes = (int) $decayMinutes;

        if ($this->tooManyAttempts($key, $maxAttempts)) {
            return $this->buildRateLimitResponse($key, $maxAttempts, $decayMinutes);
        }

        $this->incrementAttempts($key, $decayMinutes);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts),
            $this->getTimeUntilNextRetry($key)
        );
    }

    /**
     * Resolve the request signature.
     */
    protected function resolveRequestSignature(Request $request, $user): string
    {
        return 'api_rate_limit:' . $user->id . ':' . $request->ip();
    }

    /**
     * Determine if the user has too many attempts.
     */
    protected function tooManyAttempts(string $key, int $maxAttempts): bool
    {
        return Cache::get($key . ':count', 0) >= $maxAttempts;
    }

    /**
     * Increment the counter for a given key.
     */
    protected function incrementAttempts(string $key, int $decayMinutes): void
    {
        $count = Cache::get($key . ':count', 0) + 1;
        $resetTime = now()->addMinutes($decayMinutes);

        Cache::put($key . ':count', $count, $resetTime);
        Cache::put($key . ':reset', $resetTime, $resetTime);
    }

    /**
     * Calculate the number of remaining attempts.
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return max(0, $maxAttempts - Cache::get($key . ':count', 0));
    }

    /**
     * Get the time until the next retry.
     */
    protected function getTimeUntilNextRetry(string $key): int
    {
        $resetTime = Cache::get($key . ':reset');
        return $resetTime ? max(0, $resetTime->diffInSeconds(now())) : 0;
    }

    /**
     * Build the rate limit response.
     */
    protected function buildRateLimitResponse(string $key, int $maxAttempts, int $decayMinutes): JsonResponse
    {
        $retryAfter = $this->getTimeUntilNextRetry($key);

        $response = response()->json([
            'error' => 'Rate limit exceeded',
            'message' => 'Too many requests. Please try again later.',
            'status_code' => 429,
            'rate_limit' => [
                'limit' => $maxAttempts,
                'remaining' => 0,
                'reset_at' => Cache::get($key . ':reset')?->toISOString(),
                'retry_after' => $retryAfter,
            ],
        ], 429);

        return $this->addHeaders($response, $maxAttempts, 0, $retryAfter);
    }

    /**
     * Add rate limit headers to the response.
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts, int $retryAfter): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
            'X-RateLimit-Reset' => now()->addSeconds($retryAfter)->timestamp,
            'Retry-After' => $retryAfter,
        ]);

        return $response;
    }
}
