@extends('layouts.modern')

@section('title', 'Account Settings - Minilink.at')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-cog mr-3 text-purple-600"></i>
                    Account Settings
                </h1>
                <p class="text-gray-600 mt-1">Manage your account preferences and notifications</p>
            </div>
            <div>
                <a href="{{ route('profile.show') }}" class="btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-sliders-h mr-2 text-purple-600"></i>
                Preferences
            </h3>
        </div>
        <div class="card-body">
            <form action="{{ route('profile.update-settings') }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Timezone Settings -->
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock mr-1 text-blue-500"></i>Timezone
                    </label>
                    <select class="form-select" id="timezone" name="timezone">
                        <option value="UTC" {{ ($user->metadata['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : '' }}>UTC</option>
                        <option value="America/New_York" {{ ($user->metadata['timezone'] ?? '') === 'America/New_York' ? 'selected' : '' }}>Eastern Time (ET)</option>
                        <option value="America/Chicago" {{ ($user->metadata['timezone'] ?? '') === 'America/Chicago' ? 'selected' : '' }}>Central Time (CT)</option>
                        <option value="America/Denver" {{ ($user->metadata['timezone'] ?? '') === 'America/Denver' ? 'selected' : '' }}>Mountain Time (MT)</option>
                        <option value="America/Los_Angeles" {{ ($user->metadata['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time (PT)</option>
                        <option value="Europe/London" {{ ($user->metadata['timezone'] ?? '') === 'Europe/London' ? 'selected' : '' }}>London (GMT)</option>
                        <option value="Europe/Paris" {{ ($user->metadata['timezone'] ?? '') === 'Europe/Paris' ? 'selected' : '' }}>Paris (CET)</option>
                        <option value="Asia/Tokyo" {{ ($user->metadata['timezone'] ?? '') === 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo (JST)</option>
                        <option value="Australia/Sydney" {{ ($user->metadata['timezone'] ?? '') === 'Australia/Sydney' ? 'selected' : '' }}>Sydney (AEST)</option>
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Choose your preferred timezone for date and time displays</p>
                </div>

                <!-- Language Settings -->
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-language mr-1 text-green-500"></i>Language
                    </label>
                    <select class="form-select" id="language" name="language">
                        <option value="en" {{ ($user->metadata['language'] ?? 'en') === 'en' ? 'selected' : '' }}>English</option>
                        <option value="es" {{ ($user->metadata['language'] ?? '') === 'es' ? 'selected' : '' }}>Español</option>
                        <option value="fr" {{ ($user->metadata['language'] ?? '') === 'fr' ? 'selected' : '' }}>Français</option>
                        <option value="de" {{ ($user->metadata['language'] ?? '') === 'de' ? 'selected' : '' }}>Deutsch</option>
                        <option value="it" {{ ($user->metadata['language'] ?? '') === 'it' ? 'selected' : '' }}>Italiano</option>
                        <option value="pt" {{ ($user->metadata['language'] ?? '') === 'pt' ? 'selected' : '' }}>Português</option>
                        <option value="ja" {{ ($user->metadata['language'] ?? '') === 'ja' ? 'selected' : '' }}>日本語</option>
                        <option value="zh" {{ ($user->metadata['language'] ?? '') === 'zh' ? 'selected' : '' }}>中文</option>
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Select your preferred language for the interface</p>
                </div>

                <!-- Email Notification Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-envelope mr-2 text-blue-600"></i>
                        Email Notifications
                    </h4>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="email_notifications" class="text-sm font-medium text-gray-700">Account Notifications</label>
                                <p class="text-sm text-gray-500">Receive emails about account activity, security alerts, and important updates</p>
                            </div>
                            <div class="flex items-center">
                                <input type="hidden" name="email_notifications" value="0">
                                <input type="checkbox" 
                                       class="form-checkbox" 
                                       id="email_notifications" 
                                       name="email_notifications" 
                                       value="1"
                                       {{ ($user->metadata['email_notifications'] ?? true) ? 'checked' : '' }}>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="marketing_emails" class="text-sm font-medium text-gray-700">Marketing Emails</label>
                                <p class="text-sm text-gray-500">Receive emails about new features, tips, and promotional offers</p>
                            </div>
                            <div class="flex items-center">
                                <input type="hidden" name="marketing_emails" value="0">
                                <input type="checkbox" 
                                       class="form-checkbox" 
                                       id="marketing_emails" 
                                       name="marketing_emails" 
                                       value="1"
                                       {{ ($user->metadata['marketing_emails'] ?? false) ? 'checked' : '' }}>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                        Privacy & Security
                    </h4>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Profile Visibility</label>
                                <p class="text-sm text-gray-500">Control who can see your profile information</p>
                            </div>
                            <select class="form-select w-auto">
                                <option value="private">Private</option>
                                <option value="public" selected>Public</option>
                            </select>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Analytics Sharing</label>
                                <p class="text-sm text-gray-500">Allow anonymous usage data to help improve our service</p>
                            </div>
                            <input type="checkbox" class="form-checkbox" checked>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('profile.show') }}" class="btn-outline">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="{{ route('profile.password') }}" class="card hover:shadow-lg transition-shadow duration-200">
            <div class="card-body text-center">
                <i class="fas fa-key text-3xl text-blue-600 mb-3"></i>
                <h3 class="text-lg font-medium text-gray-900">Change Password</h3>
                <p class="text-sm text-gray-500">Update your account password</p>
            </div>
        </a>

        <a href="{{ route('profile.api-keys') }}" class="card hover:shadow-lg transition-shadow duration-200">
            <div class="card-body text-center">
                <i class="fas fa-code text-3xl text-green-600 mb-3"></i>
                <h3 class="text-lg font-medium text-gray-900">API Keys</h3>
                <p class="text-sm text-gray-500">Manage your API access keys</p>
            </div>
        </a>

        <a href="{{ route('profile.delete-account') }}" class="card hover:shadow-lg transition-shadow duration-200">
            <div class="card-body text-center">
                <i class="fas fa-trash text-3xl text-red-600 mb-3"></i>
                <h3 class="text-lg font-medium text-gray-900">Delete Account</h3>
                <p class="text-sm text-gray-500">Permanently delete your account</p>
            </div>
        </a>
    </div>
</div>
@endsection
