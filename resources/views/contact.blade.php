@extends('layouts.modern')

@section('title', 'Contact Us - Minilink.at')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Contact Us</h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Get in touch with our team. We're here to help you succeed with your URL shortening needs.
                </p>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid lg:grid-cols-3 gap-16">
            <!-- Contact Information -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-8">Get in Touch</h2>
                    
                    <!-- Contact Methods -->
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-envelope text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Email Support</h3>
                                <p class="text-gray-600 text-sm mb-2">Get help via email</p>
                                <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-comments text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Live Chat</h3>
                                <p class="text-gray-600 text-sm mb-2">Chat with our team</p>
                                <p class="text-gray-500 text-sm">Available 9 AM - 6 PM EST</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-phone text-purple-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Phone Support</h3>
                                <p class="text-gray-600 text-sm mb-2">Call us directly</p>
                                <a href="tel:******-0123" class="text-blue-600 hover:text-blue-700 font-medium">
                                    +****************
                                </a>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-map-marker-alt text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Office Address</h3>
                                <p class="text-gray-600 text-sm">
                                    123 Tech Street<br>
                                    San Francisco, CA 94105<br>
                                    United States
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Response Time -->
                    <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-blue-600 mr-2"></i>
                            <span class="font-semibold text-blue-900">Average Response Time</span>
                        </div>
                        <p class="text-blue-800 text-sm mt-1">We typically respond within 2-4 hours during business hours</p>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-8">Send us a Message</h2>
                    
                    <form class="space-y-6">
                        <!-- Name and Email -->
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Full Name *
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                       placeholder="Your full name">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email Address *
                                </label>
                                <input type="email" 
                                       id="email" 
                                       name="email" 
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                        
                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                Subject *
                            </label>
                            <select id="subject" 
                                    name="subject" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="technical">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="feature">Feature Request</option>
                                <option value="api">API Support</option>
                                <option value="partnership">Partnership Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <!-- Company (Optional) -->
                        <div>
                            <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                                Company (Optional)
                            </label>
                            <input type="text" 
                                   id="company" 
                                   name="company"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                   placeholder="Your company name">
                        </div>
                        
                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                Message *
                            </label>
                            <textarea id="message" 
                                      name="message" 
                                      rows="6" 
                                      required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none"
                                      placeholder="Please describe your inquiry in detail..."></textarea>
                        </div>
                        
                        <!-- Priority -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Priority Level</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="low" class="text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">Low</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="medium" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Medium</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="high" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">High</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="urgent" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Urgent</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Newsletter Subscription -->
                        <div class="flex items-start">
                            <input type="checkbox" 
                                   id="newsletter" 
                                   name="newsletter" 
                                   class="mt-1 text-blue-600 focus:ring-blue-500 rounded">
                            <label for="newsletter" class="ml-3 text-sm text-gray-700">
                                Subscribe to our newsletter for product updates and tips
                            </label>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button type="submit" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- FAQ Section -->
        <div class="mt-16">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Quick answers to common questions about our support and services
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-8">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- FAQ Item 1 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq1')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">How quickly do you respond to support requests?</h3>
                                <i id="faq1-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq1" class="mt-4 text-gray-600 leading-relaxed">
                            We typically respond within 2-4 hours during business hours (9 AM - 6 PM EST, Monday-Friday). Urgent issues are prioritized and handled faster.
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq2')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Do you offer phone support?</h3>
                                <i id="faq2-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq2" class="mt-4 text-gray-600 leading-relaxed hidden">
                            Yes, phone support is available for Pro and Enterprise customers during business hours. Free users can access email and chat support.
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq3')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Can I schedule a demo or consultation?</h3>
                                <i id="faq3-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq3" class="mt-4 text-gray-600 leading-relaxed hidden">
                            Absolutely! Contact us to schedule a personalized demo or consultation to discuss your specific URL shortening needs.
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- FAQ Item 4 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq4')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">What information should I include in my support request?</h3>
                                <i id="faq4-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq4" class="mt-4 text-gray-600 leading-relaxed hidden">
                            Please include your account email, a detailed description of the issue, steps to reproduce the problem, and any error messages you're seeing.
                        </div>
                    </div>

                    <!-- FAQ Item 5 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq5')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Do you provide technical integration support?</h3>
                                <i id="faq5-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq5" class="mt-4 text-gray-600 leading-relaxed hidden">
                            Yes, our technical team can help with API integration, custom implementations, and troubleshooting technical issues.
                        </div>
                    </div>

                    <!-- FAQ Item 6 -->
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
                        <button class="w-full text-left focus:outline-none" onclick="toggleFaq('faq6')">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">How can I provide feedback or suggest features?</h3>
                                <i id="faq6-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                            </div>
                        </button>
                        <div id="faq6" class="mt-4 text-gray-600 leading-relaxed hidden">
                            We love hearing from our users! Use the contact form above with "Feature Request" as the subject, or reach out via any of our support channels.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
        function toggleFaq(faqId) {
            const content = document.getElementById(faqId);
            const icon = document.getElementById(faqId + '-icon');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
        </script>
        
        <!-- Alternative Contact Methods -->
        <div class="mt-16">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white text-center">
                <h2 class="text-2xl font-bold mb-4">Need Immediate Help?</h2>
                <p class="mb-6 opacity-90">For urgent issues or immediate assistance, try these options:</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-comments mr-2"></i>
                        Start Live Chat
                    </a>
                    <a href="{{ route('help-center') }}" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-book mr-2"></i>
                        Browse Help Center
                    </a>
                    <a href="tel:******-0123" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-phone mr-2"></i>
                        Call Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@include('partials.footer')
@endsection
