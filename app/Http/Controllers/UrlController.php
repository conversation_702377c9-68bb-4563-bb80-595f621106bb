<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Models\Analytics;
use App\Services\UrlShortenerService;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class UrlController extends Controller
{
    private UrlShortenerService $urlShortener;
    private AnalyticsService $analytics;

    public function __construct(UrlShortenerService $urlShortener, AnalyticsService $analytics)
    {
        $this->urlShortener = $urlShortener;
        $this->analytics = $analytics;
    }

    /**
     * Display the URL shortening form.
     */
    public function index(): View
    {
        return view('urls.modern-index');
    }

    /**
     * Display the user's URL management page.
     */
    public function manage(): View
    {
        $user = Auth::user();

        // Build query with filters
        $query = $user->urls()->with(['analytics']);

        // Apply search filter
        if (request('search')) {
            $search = request('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('original_url', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (request('status')) {
            switch (request('status')) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
            }
        }

        // Apply date range filter
        if (request('date_range')) {
            switch (request('date_range')) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year);
                    break;
            }
        }

        // Get paginated results
        $urls = $query->orderBy('created_at', 'desc')->paginate(15);

        // Calculate stats
        $stats = [
            'total' => $user->urls()->count(),
            'active' => $user->urls()->where('is_active', true)->count(),
            'total_clicks' => $user->urls()->sum('click_count'),
            'clicks_today' => Analytics::whereHas('url', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->whereDate('clicked_at', today())->count(),
        ];

        return view('urls.modern-manage', compact('urls', 'stats'));
    }

    /**
     * Show the form for creating a new URL.
     */
    public function create(): View
    {
        return view('urls.create');
    }

    /**
     * Store a newly shortened URL.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        try {
            $url = $this->urlShortener->shortenUrl(
                $request->all(),
                Auth::user()
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'id' => $url->id,
                        'original_url' => $url->original_url,
                        'short_url' => $url->short_url,
                        'short_code' => $url->custom_alias ?: $url->short_code,
                        'qr_code_url' => $url->qr_code_path ? asset('storage/' . $url->qr_code_path) : null,
                        'created_at' => $url->created_at,
                    ],
                ]);
            }

            return redirect()->route('urls.show', $url)
                ->with('success', 'URL shortened successfully!');

        } catch (ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $e->errors(),
                ], 422);
            }

            return back()->withErrors($e->errors())->withInput();
        }
    }

    /**
     * Display the specified URL with analytics.
     */
    public function show(Url $url): View
    {
        // Check if user can view this URL
        if ($url->user_id && (!Auth::check() || Auth::id() !== $url->user_id)) {
            abort(403, 'Unauthorized to view this URL.');
        }

        $analytics = $this->analytics->getUrlAnalytics($url);

        return view('urls.show', compact('url', 'analytics'));
    }

    /**
     * Show the form for editing the specified URL.
     */
    public function edit(Url $url): View
    {
        // Check if user can edit this URL
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to edit this URL.');
        }

        return view('urls.modern-edit', compact('url'));
    }

    /**
     * Update the specified URL.
     */
    public function update(Request $request, Url $url): RedirectResponse
    {
        // Check if user can update this URL
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to update this URL.');
        }

        $request->validate([
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'nullable|boolean',
        ]);

        $updateData = $request->only(['title', 'description', 'expires_at']);
        $updateData['is_active'] = $request->has('is_active');

        $url->update($updateData);

        return redirect()->route('urls.show', $url)
            ->with('success', 'URL updated successfully!');
    }

    /**
     * Remove the specified URL.
     */
    public function destroy(Url $url): RedirectResponse
    {
        // Check if user can delete this URL
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to delete this URL.');
        }

        $url->delete();

        return redirect()->route('dashboard')
            ->with('success', 'URL deleted successfully!');
    }

    /**
     * Get analytics for a specific URL.
     */
    public function analytics(Request $request, Url $url): JsonResponse
    {
        // Check if user can view analytics
        if ($url->user_id && (!Auth::check() || Auth::id() !== $url->user_id)) {
            abort(403, 'Unauthorized to view analytics.');
        }

        $period = $request->get('period', 'month');
        $analytics = $this->analytics->getUrlAnalytics($url, $period);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Bulk shorten URLs.
     */
    public function bulkStore(Request $request): JsonResponse
    {
        $request->validate([
            'urls' => 'required|array|max:100',
            'urls.*.original_url' => 'required|url|max:2048',
            'urls.*.custom_alias' => 'nullable|string|max:50|alpha_dash',
        ]);

        $results = $this->urlShortener->bulkShortenUrls(
            $request->input('urls'),
            Auth::user()
        );

        return response()->json([
            'success' => true,
            'data' => $results,
        ]);
    }
}
