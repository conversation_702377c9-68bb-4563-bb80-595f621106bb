<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['Free', 'Pro', 'Premium']),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomElement([0, 999, 2999]),
            'url_limit' => $this->faker->randomElement([50, 1000, null]),
            'click_limit' => $this->faker->randomElement([1000, 10000, null]),
            'custom_domains' => $this->faker->boolean(),
            'custom_domains_limit' => $this->faker->numberBetween(1, 10),
            'analytics' => $this->faker->boolean(),
            'api_access' => $this->faker->boolean(),
            'bulk_operations' => $this->faker->boolean(),
            'password_protection' => $this->faker->boolean(),
            'team_features' => $this->faker->boolean(),
            'priority_support' => $this->faker->boolean(),
            'is_popular' => $this->faker->boolean(20),
            'is_active' => true,
        ];
    }
}
