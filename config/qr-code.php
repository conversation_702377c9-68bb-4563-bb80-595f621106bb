<?php

return [
    /*
    |--------------------------------------------------------------------------
    | QR Code Writer
    |--------------------------------------------------------------------------
    |
    | QR Code writer backend to use. Available options:
    | - 'imagick' (requires ImageMagick extension)
    | - 'gd' (requires GD extension - more commonly available)
    | - 'svg' (for SVG output)
    |
    */
    'writer' => 'gd',

    /*
    |--------------------------------------------------------------------------
    | Default QR Code Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for QR code generation
    |
    */
    'size' => 300,
    'margin' => 2,
    'format' => 'png',
    'error_correction' => 'M',
    'encoding' => 'UTF-8',

    /*
    |--------------------------------------------------------------------------
    | Default Colors
    |--------------------------------------------------------------------------
    |
    | Default foreground and background colors
    |
    */
    'foreground_color' => [0, 0, 0],
    'background_color' => [255, 255, 255],
];
