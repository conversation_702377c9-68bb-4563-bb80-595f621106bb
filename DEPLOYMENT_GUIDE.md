# Minilink.at Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying Minilink.at to a production environment. The application is a full-featured URL shortening service built with Laravel 11, featuring custom domains, analytics, team collaboration, and subscription management.

## System Requirements

### Server Requirements
- **Operating System**: Ubuntu 20.04 LTS or newer (recommended)
- **PHP**: 8.2 or higher
- **Node.js**: 18.x or higher
- **Database**: MySQL 8.0 or MariaDB 10.6
- **Web Server**: Nginx (recommended) or Apache
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **SSL Certificate**: Required for production

### PHP Extensions
- PDO
- PDO MySQL
- Mbstring
- OpenSSL
- Tokenizer
- XML
- Ctype
- JSON
- BCMath
- cURL
- GD
- Zip
- Redis (optional, for caching)

## Pre-deployment Setup

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx mysql-server php8.2-fpm php8.2-mysql php8.2-mbstring \
    php8.2-xml php8.2-curl php8.2-gd php8.2-zip php8.2-bcmath php8.2-tokenizer \
    php8.2-json php8.2-ctype composer nodejs npm git certbot python3-certbot-nginx

# Install Redis (optional)
sudo apt install -y redis-server
```

### 2. Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE minilink_at CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'minilink_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON minilink_at.* TO 'minilink_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. User Setup

```bash
# Create application user
sudo adduser minilink
sudo usermod -aG www-data minilink

# Switch to application user
sudo su - minilink
```

## Deployment Process

### 1. Automated Deployment

The easiest way to deploy is using the provided deployment script:

```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh deploy

# Or deploy with database seeding
./deploy.sh deploy-with-seed
```

### 2. Manual Deployment

If you prefer manual deployment:

#### Step 1: Clone Repository

```bash
cd /var/www
sudo git clone https://github.com/yourusername/minilink-at.git
sudo chown -R minilink:www-data minilink-at
cd minilink-at
```

#### Step 2: Install Dependencies

```bash
# Install PHP dependencies
composer install --no-dev --optimize-autoloader

# Install Node.js dependencies
npm ci --production

# Build assets
npm run build
```

#### Step 3: Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure environment variables
nano .env
```

#### Step 4: Database Migration

```bash
# Run migrations
php artisan migrate --force

# Seed database (optional)
php artisan db:seed --force
```

#### Step 5: Optimize Application

```bash
# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link

# Set permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache
```

## Environment Configuration

### Required Environment Variables

```env
# Application
APP_NAME="Minilink.at"
APP_ENV=production
APP_KEY=base64:your-generated-key-here
APP_DEBUG=false
APP_URL=https://minilink.at

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=minilink_at
DB_USERNAME=minilink_user
DB_PASSWORD=your-secure-password

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Minilink.at"

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id

# Security
FORCE_HTTPS=true
RATE_LIMITING_ENABLED=true
```

## Web Server Configuration

### Nginx Configuration

Create `/etc/nginx/sites-available/minilink-at`:

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name minilink.at www.minilink.at;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name minilink.at www.minilink.at;
    root /var/www/minilink-at/public;

    index index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/minilink.at/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/minilink.at/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        limit_req zone=general burst=20 nodelay;
    }

    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
        limit_req zone=api burst=50 nodelay;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Asset optimization
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/minilink-at /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Using Let's Encrypt

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d minilink.at -d www.minilink.at

# Test auto-renewal
sudo certbot renew --dry-run
```

## Monitoring & Maintenance

### 1. Log Rotation

Create `/etc/logrotate.d/minilink-at`:

```
/var/www/minilink-at/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. Cron Jobs

Add to crontab:

```bash
# Laravel scheduler
* * * * * cd /var/www/minilink-at && php artisan schedule:run >> /dev/null 2>&1

# Database backup (daily at 2 AM)
0 2 * * * mysqldump -u minilink_user -p'password' minilink_at > /var/backups/minilink-at/db-$(date +\%Y\%m\%d).sql

# Log cleanup (weekly)
0 0 * * 0 find /var/www/minilink-at/storage/logs -name "*.log" -mtime +30 -delete
```

### 3. Health Monitoring

Create a health check script:

```bash
#!/bin/bash
# /usr/local/bin/health-check.sh

APP_URL="https://minilink.at"
HEALTH_ENDPOINT="$APP_URL/api/v1/health"

# Check application response
if ! curl -f -s "$HEALTH_ENDPOINT" > /dev/null; then
    echo "Application health check failed" | mail -s "Minilink.at Health Alert" <EMAIL>
fi

# Check database
if ! cd /var/www/minilink-at && php artisan migrate:status > /dev/null 2>&1; then
    echo "Database health check failed" | mail -s "Minilink.at Database Alert" <EMAIL>
fi
```

## Performance Optimization

### 1. PHP-FPM Configuration

Edit `/etc/php/8.2/fpm/pool.d/www.conf`:

```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

### 2. MySQL Optimization

Add to `/etc/mysql/mysql.conf.d/mysqld.cnf`:

```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_type = 1
query_cache_size = 64M
```

### 3. Redis Configuration

Edit `/etc/redis/redis.conf`:

```
maxmemory 512mb
maxmemory-policy allkeys-lru
```

## Security Considerations

### 1. Firewall Setup

```bash
# Install UFW
sudo apt install ufw

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Fail2Ban

```bash
# Install Fail2Ban
sudo apt install fail2ban

# Configure for Nginx
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
```

Add to `/etc/fail2ban/jail.local`:

```ini
[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
```

## Backup Strategy

### 1. Database Backups

```bash
# Daily backup script
#!/bin/bash
BACKUP_DIR="/var/backups/minilink-at"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
mysqldump -u minilink_user -p'password' minilink_at | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Keep only last 30 days
find $BACKUP_DIR -name "db_*.sql.gz" -mtime +30 -delete
```

### 2. File Backups

```bash
# Weekly file backup
#!/bin/bash
BACKUP_DIR="/var/backups/minilink-at"
DATE=$(date +%Y%m%d_%H%M%S)

tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C /var/www minilink-at \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='storage/logs' \
    --exclude='storage/framework/cache'

# Keep only last 4 weeks
find $BACKUP_DIR -name "files_*.tar.gz" -mtime +28 -delete
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data storage bootstrap/cache
   sudo chmod -R 755 storage bootstrap/cache
   ```

2. **Database Connection Issues**
   - Check database credentials in `.env`
   - Verify MySQL service is running: `sudo systemctl status mysql`

3. **SSL Certificate Issues**
   - Renew certificate: `sudo certbot renew`
   - Check certificate status: `sudo certbot certificates`

4. **Performance Issues**
   - Clear application cache: `php artisan cache:clear`
   - Restart PHP-FPM: `sudo systemctl restart php8.2-fpm`
   - Check server resources: `htop`, `df -h`

### Log Locations

- Application logs: `/var/www/minilink-at/storage/logs/`
- Nginx logs: `/var/log/nginx/`
- PHP-FPM logs: `/var/log/php8.2-fpm.log`
- MySQL logs: `/var/log/mysql/`

## Support

For deployment support or issues:
- Email: <EMAIL>
- Documentation: https://docs.minilink.at
- GitHub Issues: https://github.com/yourusername/minilink-at/issues

## License

This deployment guide is part of the Minilink.at project and is subject to the same license terms.
