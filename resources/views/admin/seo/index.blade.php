@extends('layouts.dashboard-template')

@section('page_title', 'SEO Management - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-search mr-3 text-blue-600"></i>SEO Management
@endsection
@section('page_description', 'Manage search engine optimization settings and configurations')

@section('page_actions')
    <a href="{{ route('admin.dashboard') }}" class="btn-secondary">
        <i class="fas fa-tachometer-alt mr-2"></i>Admin Dashboard
    </a>
    <a href="{{ route('admin.seo.audit') }}" class="btn-outline">
        <i class="fas fa-chart-line mr-2"></i>SEO Audit
    </a>
    <a href="{{ route('admin.seo.test') }}" class="btn-primary">
        <i class="fas fa-vial mr-2"></i>Test Configuration
    </a>
@endsection

@section('main_content')
    <!-- SEO Configuration Overview -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-cog mr-2 text-blue-600"></i>SEO Configuration Overview
            </h3>
        </div>
        <div class="card-body">
            <!-- Quick Actions - 5 features in same line -->
            <div class="flex flex-wrap items-center gap-4 mb-8 p-4 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                    <span class="text-sm font-medium text-gray-700">Quick Actions:</span>
                </div>
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('admin.seo.meta') }}" class="btn-outline flex items-center">
                        <i class="fas fa-tags mr-2 text-blue-500"></i>Meta Tags
                    </a>
                    <a href="{{ route('admin.seo.social') }}" class="btn-outline flex items-center">
                        <i class="fas fa-share-alt mr-2 text-green-500"></i>Social Media
                    </a>
                    <a href="{{ route('admin.seo.analytics') }}" class="btn-outline flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-purple-500"></i>Analytics
                    </a>
                    <a href="{{ route('admin.seo.verification') }}" class="btn-outline flex items-center">
                        <i class="fas fa-shield-check mr-2 text-orange-500"></i>Verification
                    </a>
                    <a href="{{ route('admin.seo.test') }}" class="btn-outline flex items-center">
                        <i class="fas fa-vial mr-2 text-red-500"></i>Test Config
                    </a>
                </div>
            </div>

            <!-- SEO Settings Status Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Meta Tags Status -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-tags text-blue-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-900">Meta Tags</h4>
                                <p class="text-xs text-gray-500">Basic SEO settings</p>
                            </div>
                        </div>
                        @if(isset($settings['meta']) && $settings['meta']->count() > 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ $settings['meta']->where('value', '!=', '')->count() }}/{{ $settings['meta']->count() }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                0/0
                            </span>
                        @endif
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-2xl font-bold text-gray-900">
                            @if(isset($settings['meta']))
                                {{ round(($settings['meta']->where('value', '!=', '')->count() / max($settings['meta']->count(), 1)) * 100) }}%
                            @else
                                0%
                            @endif
                        </div>
                        <a href="{{ route('admin.seo.meta') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Configure →
                        </a>
                    </div>
                </div>

                <!-- Social Media Status -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-share-alt text-green-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-900">Social Media</h4>
                                <p class="text-xs text-gray-500">Open Graph & Twitter</p>
                            </div>
                        </div>
                        @if(isset($settings['social']) && $settings['social']->count() > 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ $settings['social']->where('value', '!=', '')->count() }}/{{ $settings['social']->count() }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                0/0
                            </span>
                        @endif
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-2xl font-bold text-gray-900">
                            @if(isset($settings['social']))
                                {{ round(($settings['social']->where('value', '!=', '')->count() / max($settings['social']->count(), 1)) * 100) }}%
                            @else
                                0%
                            @endif
                        </div>
                        <a href="{{ route('admin.seo.social') }}" class="text-green-600 hover:text-green-800 text-sm font-medium">
                            Configure →
                        </a>
                    </div>
                </div>

                <!-- Analytics Status -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-bar text-purple-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-900">Analytics</h4>
                                <p class="text-xs text-gray-500">Tracking codes</p>
                            </div>
                        </div>
                        @if(isset($analytics) && count($analytics) > 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ count($analytics) }} Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Not Set
                            </span>
                        @endif
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-2xl font-bold text-gray-900">
                            {{ isset($analytics) ? count($analytics) : 0 }}
                        </div>
                        <a href="{{ route('admin.seo.analytics') }}" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                            Configure →
                        </a>
                    </div>
                </div>

                <!-- Verification Status -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shield-check text-orange-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-900">Verification</h4>
                                <p class="text-xs text-gray-500">Search engines</p>
                            </div>
                        </div>
                        @if(isset($verification) && count($verification) > 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ count($verification) }} Verified
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Not Set
                            </span>
                        @endif
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-2xl font-bold text-gray-900">
                            {{ isset($verification) ? count($verification) : 0 }}
                        </div>
                        <a href="{{ route('admin.seo.verification') }}" class="text-orange-600 hover:text-orange-800 text-sm font-medium">
                            Configure →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Settings Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Current Settings -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list mr-2 text-blue-600"></i>Current Settings
                </h3>
            </div>
            <div class="card-body">
                @if(isset($settings) && $settings->count() > 0)
                    <div class="space-y-4">
                        @foreach($settings->take(8) as $category => $categorySettings)
                            @foreach($categorySettings->take(2) as $setting)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900">{{ str_replace('_', ' ', ucwords($setting->key)) }}</div>
                                        <div class="text-sm text-gray-500">{{ ucfirst($category) }} setting</div>
                                    </div>
                                    <div class="ml-3">
                                        @if($setting->value)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ✓ Configured
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                ⚠ Empty
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                    <div class="mt-6 text-center">
                        <a href="{{ route('admin.seo.test') }}" class="btn-outline">
                            <i class="fas fa-eye mr-2"></i>View All Settings
                        </a>
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-cog text-gray-400 text-3xl mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No Settings Configured</h4>
                        <p class="text-gray-600 mb-4">Start by configuring your basic SEO settings.</p>
                        <a href="{{ route('admin.seo.meta') }}" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Configure Meta Tags
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- SEO Performance -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-chart-line mr-2 text-green-600"></i>SEO Performance
                </h3>
            </div>
            <div class="card-body">
                <div class="space-y-6">
                    <!-- Overall Score -->
                    <div class="text-center">
                        <div class="relative w-24 h-24 mx-auto mb-4">
                            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <path class="text-blue-600" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="75, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xl font-bold text-gray-900">75%</span>
                            </div>
                        </div>
                        <h4 class="text-lg font-medium text-gray-900">SEO Score</h4>
                        <p class="text-sm text-gray-600">Good optimization level</p>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-blue-50 rounded-lg">
                            <div class="text-lg font-bold text-blue-600">{{ isset($settings) ? $settings->flatten()->where('value', '!=', '')->count() : 0 }}</div>
                            <div class="text-xs text-blue-600">Configured</div>
                        </div>
                        <div class="text-center p-3 bg-green-50 rounded-lg">
                            <div class="text-lg font-bold text-green-600">{{ isset($analytics) ? count($analytics) : 0 }}</div>
                            <div class="text-xs text-green-600">Analytics</div>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="text-center">
                        <a href="{{ route('admin.seo.audit') }}" class="btn-primary w-full">
                            <i class="fas fa-search mr-2"></i>Run SEO Audit
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Tools Section -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-tools mr-2 text-gray-600"></i>SEO Tools
            </h3>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-4 justify-center">
                <a href="{{ route('admin.seo.robots-txt') }}" class="flex-1 min-w-[180px] max-w-[200px] btn-outline text-center flex flex-col items-center p-4 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-robot text-2xl text-gray-600 mb-2"></i>
                    <span class="font-medium">Robots.txt</span>
                    <span class="text-xs text-gray-500 mt-1">Generate robots file</span>
                </a>

                <a href="{{ route('admin.seo.sitemap') }}" class="flex-1 min-w-[180px] max-w-[200px] btn-outline text-center flex flex-col items-center p-4 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-sitemap text-2xl text-blue-600 mb-2"></i>
                    <span class="font-medium">Sitemap</span>
                    <span class="text-xs text-gray-500 mt-1">Generate XML sitemap</span>
                </a>

                <a href="{{ route('admin.seo.export') }}" class="flex-1 min-w-[180px] max-w-[200px] btn-outline text-center flex flex-col items-center p-4 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-download text-2xl text-green-600 mb-2"></i>
                    <span class="font-medium">Export</span>
                    <span class="text-xs text-gray-500 mt-1">Export settings</span>
                </a>

                <button type="button" onclick="openImportModal()" class="flex-1 min-w-[180px] max-w-[200px] btn-outline text-center flex flex-col items-center p-4 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-upload text-2xl text-yellow-600 mb-2"></i>
                    <span class="font-medium">Import</span>
                    <span class="text-xs text-gray-500 mt-1">Import settings</span>
                </button>

                <button type="button" onclick="openResetModal()" class="flex-1 min-w-[180px] max-w-[200px] btn-outline text-center flex flex-col items-center p-4 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-undo text-2xl text-red-600 mb-2"></i>
                    <span class="font-medium">Reset</span>
                    <span class="text-xs text-gray-500 mt-1">Reset to defaults</span>
                </button>
            </div>
        </div>
    </div>

<!-- Import Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Import SEO Settings</h3>
                <button type="button" onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form action="{{ route('admin.seo.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="mb-4">
                    <label for="import_file" class="block text-sm font-medium text-gray-700 mb-2">Select JSON file</label>
                    <input type="file"
                           class="input-field"
                           id="import_file"
                           name="import_file"
                           accept=".json"
                           required>
                    <p class="mt-1 text-sm text-gray-500">Upload a JSON file exported from SEO settings.</p>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeImportModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-upload mr-2"></i>Import Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Modal -->
<div id="resetModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reset SEO Settings</h3>
                <button type="button" onclick="closeResetModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4">
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-yellow-400 mr-3 mt-0.5"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">Warning</h4>
                            <p class="text-sm text-yellow-700 mt-1">This will reset all SEO settings to their default values. This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700">Are you sure you want to reset all SEO settings to defaults?</p>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeResetModal()" class="btn-secondary">Cancel</button>
                <form action="{{ route('admin.seo.reset') }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                        <i class="fas fa-undo mr-2"></i>Reset Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

function closeImportModal() {
    document.getElementById('importModal').classList.add('hidden');
}

function openResetModal() {
    document.getElementById('resetModal').classList.remove('hidden');
}

function closeResetModal() {
    document.getElementById('resetModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('importModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImportModal();
    }
});

document.getElementById('resetModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResetModal();
    }
});
</script>
@endpush
@endsection
