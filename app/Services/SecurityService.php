<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Check if URL is malicious.
     */
    public function isMaliciousUrl(string $url): bool
    {
        try {
            // Check against known malicious domains
            if ($this->isKnownMaliciousDomain($url)) {
                return true;
            }

            // Check URL reputation (mock implementation)
            if ($this->checkUrlReputation($url)) {
                return true;
            }

            // Check for suspicious patterns
            if ($this->hasSuspiciousPatterns($url)) {
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::warning('Malicious URL check failed', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            
            // Err on the side of caution
            return false;
        }
    }

    /**
     * Implement rate limiting for IP addresses.
     */
    public function checkRateLimit(Request $request, int $maxAttempts = 100, int $decayMinutes = 60): bool
    {
        $key = 'rate_limit:' . $request->ip();
        $attempts = Cache::get($key, 0);

        if ($attempts >= $maxAttempts) {
            Log::warning('Rate limit exceeded', [
                'ip' => $request->ip(),
                'attempts' => $attempts,
                'user_agent' => $request->userAgent(),
            ]);
            
            return false;
        }

        Cache::put($key, $attempts + 1, now()->addMinutes($decayMinutes));
        return true;
    }

    /**
     * Detect and prevent bot traffic.
     */
    public function isBotTraffic(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');
        
        // Known bot patterns
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python-requests', 'go-http-client', 'java/', 'okhttp',
            'facebookexternalhit', 'twitterbot', 'linkedinbot',
            'whatsapp', 'telegrambot', 'slackbot', 'discordbot',
        ];

        foreach ($botPatterns as $pattern) {
            if (str_contains($userAgent, $pattern)) {
                return true;
            }
        }

        // Check for suspicious request patterns
        if ($this->hasSuspiciousRequestPattern($request)) {
            return true;
        }

        return false;
    }

    /**
     * Validate and sanitize input data.
     */
    public function sanitizeInput(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Remove potentially dangerous characters
                $value = strip_tags($value);
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                
                // Trim whitespace
                $value = trim($value);
                
                // Check for SQL injection patterns
                if ($this->hasSqlInjectionPattern($value)) {
                    Log::warning('Potential SQL injection attempt', [
                        'key' => $key,
                        'value' => $value,
                        'ip' => request()->ip(),
                    ]);
                    continue; // Skip this field
                }
                
                // Check for XSS patterns
                if ($this->hasXssPattern($value)) {
                    Log::warning('Potential XSS attempt', [
                        'key' => $key,
                        'value' => $value,
                        'ip' => request()->ip(),
                    ]);
                    continue; // Skip this field
                }
            }
            
            $sanitized[$key] = $value;
        }

        return $sanitized;
    }

    /**
     * Check for suspicious IP addresses.
     */
    public function isSuspiciousIp(string $ip): bool
    {
        // Check if IP is in blacklist
        if ($this->isBlacklistedIp($ip)) {
            return true;
        }

        // Check if IP has too many recent requests
        $recentRequests = Cache::get("ip_requests:{$ip}", 0);
        if ($recentRequests > 1000) { // More than 1000 requests in the last hour
            return true;
        }

        // Check if IP is from a known VPN/proxy service
        if ($this->isVpnOrProxy($ip)) {
            return true;
        }

        return false;
    }

    /**
     * Log security events.
     */
    public function logSecurityEvent(string $event, array $data = []): void
    {
        Log::channel('security')->warning($event, array_merge($data, [
            'timestamp' => now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
        ]));
    }

    /**
     * Generate security report.
     */
    public function generateSecurityReport(): array
    {
        return [
            'timestamp' => now()->toISOString(),
            'threats_blocked' => $this->getThreatsBlocked(),
            'rate_limit_violations' => $this->getRateLimitViolations(),
            'malicious_urls_blocked' => $this->getMaliciousUrlsBlocked(),
            'bot_traffic_detected' => $this->getBotTrafficDetected(),
            'security_recommendations' => $this->getSecurityRecommendations(),
        ];
    }

    /**
     * Check if domain is known to be malicious.
     */
    private function isKnownMaliciousDomain(string $url): bool
    {
        $domain = parse_url($url, PHP_URL_HOST);
        
        // List of known malicious domains (this would be updated regularly)
        $maliciousDomains = [
            'malware.com',
            'phishing.net',
            'spam.org',
            // Add more known malicious domains
        ];

        return in_array($domain, $maliciousDomains);
    }

    /**
     * Check URL reputation using external service.
     */
    private function checkUrlReputation(string $url): bool
    {
        // In production, this would integrate with services like:
        // - Google Safe Browsing API
        // - VirusTotal API
        // - URLVoid API
        
        // Mock implementation
        return false;
    }

    /**
     * Check for suspicious URL patterns.
     */
    private function hasSuspiciousPatterns(string $url): bool
    {
        $suspiciousPatterns = [
            '/bit\.ly\/[a-zA-Z0-9]{6,}\/[a-zA-Z0-9]{6,}/', // Double shortened URLs
            '/tinyurl\.com\/[a-zA-Z0-9]{6,}\/[a-zA-Z0-9]{6,}/',
            '/[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/', // IP addresses
            '/localhost/', // Localhost URLs
            '/127\.0\.0\.1/', // Loopback addresses
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for suspicious request patterns.
     */
    private function hasSuspiciousRequestPattern(Request $request): bool
    {
        // Check request frequency
        $ip = $request->ip();
        $requestCount = Cache::get("request_count:{$ip}", 0);
        
        if ($requestCount > 50) { // More than 50 requests per minute
            return true;
        }

        Cache::put("request_count:{$ip}", $requestCount + 1, 60);

        // Check for missing common headers
        if (!$request->hasHeader('Accept') || !$request->hasHeader('Accept-Language')) {
            return true;
        }

        return false;
    }

    /**
     * Check for SQL injection patterns.
     */
    private function hasSqlInjectionPattern(string $value): bool
    {
        $patterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/update\s+set/i',
            '/or\s+1\s*=\s*1/i',
            '/and\s+1\s*=\s*1/i',
            '/\'\s*or\s*\'/i',
            '/\'\s*and\s*\'/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for XSS patterns.
     */
    private function hasXssPattern(string $value): bool
    {
        $patterns = [
            '/<script/i',
            '/javascript:/i',
            '/on\w+\s*=/i', // Event handlers like onclick, onload
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is blacklisted.
     */
    private function isBlacklistedIp(string $ip): bool
    {
        $blacklistedIps = Cache::get('blacklisted_ips', []);
        return in_array($ip, $blacklistedIps);
    }

    /**
     * Check if IP is from VPN or proxy.
     */
    private function isVpnOrProxy(string $ip): bool
    {
        // In production, this would use a service like:
        // - IPQualityScore
        // - MaxMind GeoIP2
        // - IP2Location
        
        // Mock implementation
        return false;
    }

    /**
     * Get threats blocked count.
     */
    private function getThreatsBlocked(): int
    {
        return Cache::get('threats_blocked_today', 0);
    }

    /**
     * Get rate limit violations count.
     */
    private function getRateLimitViolations(): int
    {
        return Cache::get('rate_limit_violations_today', 0);
    }

    /**
     * Get malicious URLs blocked count.
     */
    private function getMaliciousUrlsBlocked(): int
    {
        return Cache::get('malicious_urls_blocked_today', 0);
    }

    /**
     * Get bot traffic detected count.
     */
    private function getBotTrafficDetected(): int
    {
        return Cache::get('bot_traffic_detected_today', 0);
    }

    /**
     * Get security recommendations.
     */
    private function getSecurityRecommendations(): array
    {
        $recommendations = [];

        // Check if HTTPS is enforced
        if (!config('app.force_https')) {
            $recommendations[] = 'Enable HTTPS enforcement for all requests';
        }

        // Check if rate limiting is configured
        if (!config('app.rate_limiting_enabled')) {
            $recommendations[] = 'Enable rate limiting to prevent abuse';
        }

        // Check if security headers are configured
        $recommendations[] = 'Ensure security headers (CSP, HSTS, X-Frame-Options) are configured';

        return $recommendations;
    }
}
