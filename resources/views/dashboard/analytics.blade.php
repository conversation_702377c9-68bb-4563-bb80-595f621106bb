@extends('layouts.modern')

@section('title', 'Analytics Overview - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-chart-bar mr-3 text-blue-600"></i>
                    Analytics Overview
                </h1>
                <p class="text-gray-600 mt-1">Track your URL performance and visitor insights</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <!-- Period Filter -->
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button class="px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ $period === 'today' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}" data-period="today">Today</button>
                    <button class="px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ $period === 'week' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}" data-period="week">Week</button>
                    <button class="px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ $period === 'month' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}" data-period="month">Month</button>
                    <button class="px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ $period === 'year' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}" data-period="year">Year</button>
                </div>
                <button class="btn-outline" onclick="refreshAnalytics()">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Clicks -->
        <div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <div class="card-body text-center">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-mouse-pointer text-2xl text-blue-100"></i>
                </div>
                <h3 class="text-3xl font-bold mb-1" id="totalClicks">{{ number_format($analyticsData['total_clicks']) }}</h3>
                <p class="text-blue-100 font-medium">Total Clicks</p>
                <small class="text-blue-200">{{ ucfirst($period) }} Period</small>
            </div>
        </div>

        <!-- Unique Visitors -->
        <div class="card bg-gradient-to-r from-green-500 to-green-600 text-white">
            <div class="card-body text-center">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-users text-2xl text-green-100"></i>
                </div>
                <h3 class="text-3xl font-bold mb-1" id="uniqueVisitors">{{ number_format($analyticsData['unique_visitors']) }}</h3>
                <p class="text-green-100 font-medium">Unique Visitors</p>
                <small class="text-green-200">Unique IP Addresses</small>
            </div>
        </div>

        <!-- Active URLs -->
        <div class="card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <div class="card-body text-center">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-link text-2xl text-purple-100"></i>
                </div>
                <h3 class="text-3xl font-bold mb-1" id="activeUrls">{{ count($analyticsData['top_urls']) }}</h3>
                <p class="text-purple-100 font-medium">Active URLs</p>
                <small class="text-purple-200">With Clicks</small>
            </div>
        </div>

        <!-- Countries -->
        <div class="card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <div class="card-body text-center">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-globe text-2xl text-orange-100"></i>
                </div>
                <h3 class="text-3xl font-bold mb-1" id="countries">{{ count($analyticsData['countries']) }}</h3>
                <p class="text-orange-100 font-medium">Countries</p>
                <small class="text-orange-200">Geographic Reach</small>
            </div>
        </div>
    </div>

    <!-- Analytics Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Daily Clicks Chart -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-calendar-alt mr-2 text-blue-600"></i>
                        Daily Activity
                    </h3>
                </div>
                <div class="card-body">
                    <div class="h-80">
                        <canvas id="dailyClicksChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Distribution -->
        <div class="lg:col-span-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-mobile-alt mr-2 text-green-600"></i>
                        Device Types
                    </h3>
                </div>
                <div class="card-body">
                    <div class="h-80">
                        <canvas id="deviceChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Top Performing URLs -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-trophy mr-2 text-yellow-500"></i>
                        Top Performing URLs
                    </h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ count($analyticsData['top_urls']) }} URLs
                    </span>
                </div>
            </div>
            <div class="card-body">
                @if(count($analyticsData['top_urls']) > 0)
                    <div class="space-y-4">
                        @foreach($analyticsData['top_urls'] as $url)
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-gray-900 truncate">
                                    {{ $url['title'] ?: 'Untitled' }}
                                </h4>
                                <p class="text-sm text-gray-500 truncate">
                                    {{ Str::limit($url['original_url'], 40) }}
                                </p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ number_format($url['analytics_count']) }} clicks
                                </span>
                                <a href="{{ route('urls.show', $url['id']) }}"
                                   class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200"
                                   title="View Analytics">
                                    <i class="fas fa-chart-bar text-sm"></i>
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-chart-bar text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No URL activity in this period</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-globe mr-2 text-green-500"></i>
                        Geographic Distribution
                    </h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {{ count($analyticsData['countries']) }} Countries
                    </span>
                </div>
            </div>
            <div class="card-body">
                @if(count($analyticsData['countries']) > 0)
                    <div class="space-y-4">
                        @foreach($analyticsData['countries'] as $country)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-flag text-green-600 text-sm"></i>
                                </div>
                                <span class="font-medium text-gray-900">{{ $country['country'] }}</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $country['percentage'] }}%"></div>
                                    </div>
                                    <span class="text-sm text-gray-500">{{ $country['percentage'] }}%</span>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ number_format($country['count']) }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-globe text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No geographic data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Browser and Device Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Browser Statistics -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-browser mr-2 text-purple-500"></i>
                    Browser Statistics
                </h3>
            </div>
            <div class="card-body">
                @if(count($analyticsData['browsers']) > 0)
                    <div class="space-y-4">
                        @foreach($analyticsData['browsers'] as $browser)
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-900">{{ $browser['browser'] }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">{{ $browser['percentage'] }}%</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        {{ number_format($browser['count']) }}
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full transition-all duration-300" style="width: {{ $browser['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-browser text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No browser data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Device Statistics -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-mobile-alt mr-2 text-blue-500"></i>
                    Device Statistics
                </h3>
            </div>
            <div class="card-body">
                @if(count($analyticsData['devices']) > 0)
                    <div class="space-y-4">
                        @foreach($analyticsData['devices'] as $device)
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-900">{{ ucfirst($device['device']) }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">{{ $device['percentage'] }}%</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ number_format($device['count']) }}
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: {{ $device['percentage'] }}%"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-mobile-alt text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No device data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Daily Clicks Chart
const dailyClicksCtx = document.getElementById('dailyClicksChart').getContext('2d');
const dailyClicksChart = new Chart(dailyClicksCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($analyticsData['daily_clicks'], 'date')) !!},
        datasets: [{
            label: 'Clicks',
            data: {!! json_encode(array_column($analyticsData['daily_clicks'], 'clicks')) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Device Types Chart
@if(count($analyticsData['devices']) > 0)
const deviceCtx = document.getElementById('deviceChart').getContext('2d');
const deviceChart = new Chart(deviceCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode(array_column($analyticsData['devices'], 'device')) !!},
        datasets: [{
            data: {!! json_encode(array_column($analyticsData['devices'], 'count')) !!},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
@endif

// Period filter functionality
document.querySelectorAll('[data-period]').forEach(button => {
    button.addEventListener('click', function() {
        // Update active button styles
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
            btn.classList.add('text-gray-600', 'hover:text-gray-900');
        });

        this.classList.remove('text-gray-600', 'hover:text-gray-900');
        this.classList.add('bg-white', 'text-blue-600', 'shadow-sm');

        // Reload page with new period
        const period = this.getAttribute('data-period');
        window.location.href = '{{ route("dashboard.analytics") }}?period=' + period;
    });
});

function refreshAnalytics() {
    window.location.reload();
}
</script>
@endpush
