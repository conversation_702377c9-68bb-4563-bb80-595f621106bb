@extends('layouts.modern')

@section('title', 'API Documentation - Minilink.at')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">API Documentation</h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Integrate Minilink.at into your applications with our powerful REST API
                </p>
                <div class="mt-8 flex justify-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-2"></i>
                        REST API
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-code mr-2"></i>
                        JSON Response
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        <i class="fas fa-shield-alt mr-2"></i>
                        API Key Auth
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl border border-gray-200 p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Documentation</h3>
                    <nav class="space-y-2">
                        <a href="#getting-started" class="block px-3 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">Getting Started</a>
                        <a href="#authentication" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg">Authentication</a>
                        <a href="#endpoints" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg">Endpoints</a>
                        <a href="#examples" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg">Code Examples</a>
                        <a href="#errors" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg">Error Handling</a>
                        <a href="#rate-limits" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg">Rate Limits</a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3 space-y-8">
                <!-- Getting Started -->
                <section id="getting-started" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Getting Started</h2>
                    <p class="text-gray-600 mb-6">
                        The Minilink.at API allows you to programmatically create, manage, and track short URLs. 
                        Our REST API uses JSON for data exchange and requires API key authentication.
                    </p>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">Base URL</h3>
                        <code class="text-blue-800 font-mono">https://minilink.at/api/v1</code>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Start</h3>
                    <ol class="list-decimal list-inside space-y-2 text-gray-600">
                        <li>Get your API key from your dashboard</li>
                        <li>Include the API key in your request headers</li>
                        <li>Make requests to our endpoints</li>
                        <li>Handle the JSON responses</li>
                    </ol>
                </section>

                <!-- Authentication -->
                <section id="authentication" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Authentication</h2>
                    <p class="text-gray-600 mb-6">
                        All API requests require authentication using your API key. Include your API key in the request headers.
                    </p>
                    
                    <div class="bg-gray-900 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-gray-300 font-medium">Request Headers</span>
                            <button class="text-gray-400 hover:text-white">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre class="text-green-400 font-mono text-sm"><code>Authorization: Bearer YOUR_API_KEY
Content-Type: application/json</code></pre>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-yellow-800">Keep your API key secure</h4>
                                <p class="text-yellow-700 text-sm">Never expose your API key in client-side code or public repositories.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Endpoints -->
                <section id="endpoints" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>
                    
                    <!-- Create Short URL -->
                    <div class="border border-gray-200 rounded-lg p-6 mb-6">
                        <div class="flex items-center mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mr-3">
                                POST
                            </span>
                            <code class="text-gray-800 font-mono">/shorten</code>
                        </div>
                        <p class="text-gray-600 mb-4">Create a new short URL</p>
                        
                        <h4 class="font-semibold text-gray-900 mb-2">Request Body</h4>
                        <div class="bg-gray-900 rounded-lg p-4 mb-4">
                            <pre class="text-green-400 font-mono text-sm"><code>{
  "original_url": "https://example.com/very-long-url",
  "custom_alias": "my-link",
  "expires_at": "2024-12-31",
  "description": "My custom link"
}</code></pre>
                        </div>
                        
                        <h4 class="font-semibold text-gray-900 mb-2">Response</h4>
                        <div class="bg-gray-900 rounded-lg p-4">
                            <pre class="text-blue-400 font-mono text-sm"><code>{
  "success": true,
  "data": {
    "id": "abc123",
    "short_url": "https://minilink.at/my-link",
    "original_url": "https://example.com/very-long-url",
    "clicks": 0,
    "created_at": "2024-01-15T10:30:00Z"
  }
}</code></pre>
                        </div>
                    </div>

                    <!-- Get URL Stats -->
                    <div class="border border-gray-200 rounded-lg p-6 mb-6">
                        <div class="flex items-center mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mr-3">
                                GET
                            </span>
                            <code class="text-gray-800 font-mono">/urls/{id}/stats</code>
                        </div>
                        <p class="text-gray-600 mb-4">Get analytics for a specific URL</p>
                        
                        <h4 class="font-semibold text-gray-900 mb-2">Response</h4>
                        <div class="bg-gray-900 rounded-lg p-4">
                            <pre class="text-blue-400 font-mono text-sm"><code>{
  "success": true,
  "data": {
    "total_clicks": 1250,
    "unique_clicks": 890,
    "countries": {
      "US": 450,
      "UK": 200,
      "CA": 150
    },
    "referrers": {
      "direct": 600,
      "twitter.com": 300,
      "facebook.com": 200
    }
  }
}</code></pre>
                        </div>
                    </div>

                    <!-- List URLs -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mr-3">
                                GET
                            </span>
                            <code class="text-gray-800 font-mono">/urls</code>
                        </div>
                        <p class="text-gray-600 mb-4">List all your short URLs with pagination</p>
                        
                        <h4 class="font-semibold text-gray-900 mb-2">Query Parameters</h4>
                        <ul class="text-gray-600 mb-4 space-y-1">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">page</code> - Page number (default: 1)</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">per_page</code> - Items per page (default: 20, max: 100)</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">search</code> - Search term</li>
                        </ul>
                    </div>
                </section>

                <!-- Code Examples -->
                <section id="examples" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Code Examples</h2>
                    
                    <!-- JavaScript Example -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">JavaScript (Node.js)</h3>
                        <div class="bg-gray-900 rounded-lg p-6">
                            <pre class="text-green-400 font-mono text-sm"><code>const axios = require('axios');

const response = await axios.post('https://minilink.at/api/v1/shorten', {
  original_url: 'https://example.com/long-url',
  custom_alias: 'my-link'
}, {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

console.log(response.data.data.short_url);</code></pre>
                        </div>
                    </div>

                    <!-- PHP Example -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">PHP</h3>
                        <div class="bg-gray-900 rounded-lg p-6">
                            <pre class="text-green-400 font-mono text-sm"><code>$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => 'https://minilink.at/api/v1/shorten',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_POST => true,
  CURLOPT_POSTFIELDS => json_encode([
    'original_url' => 'https://example.com/long-url',
    'custom_alias' => 'my-link'
  ]),
  CURLOPT_HTTPHEADER => [
    'Authorization: Bearer YOUR_API_KEY',
    'Content-Type: application/json'
  ]
]);

$response = curl_exec($curl);
$data = json_decode($response, true);
echo $data['data']['short_url'];</code></pre>
                        </div>
                    </div>

                    <!-- Python Example -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Python</h3>
                        <div class="bg-gray-900 rounded-lg p-6">
                            <pre class="text-green-400 font-mono text-sm"><code>import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

data = {
    'original_url': 'https://example.com/long-url',
    'custom_alias': 'my-link'
}

response = requests.post(
    'https://minilink.at/api/v1/shorten',
    headers=headers,
    json=data
)

result = response.json()
print(result['data']['short_url'])</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Error Handling -->
                <section id="errors" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Error Handling</h2>
                    <p class="text-gray-600 mb-6">
                        The API uses conventional HTTP response codes to indicate success or failure of requests.
                    </p>
                    
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-green-100 text-green-800 mr-3">
                                    200
                                </span>
                                <span class="font-medium">OK</span>
                            </div>
                            <p class="text-gray-600 text-sm">Request successful</p>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-red-100 text-red-800 mr-3">
                                    400
                                </span>
                                <span class="font-medium">Bad Request</span>
                            </div>
                            <p class="text-gray-600 text-sm">Invalid request parameters</p>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-red-100 text-red-800 mr-3">
                                    401
                                </span>
                                <span class="font-medium">Unauthorized</span>
                            </div>
                            <p class="text-gray-600 text-sm">Invalid or missing API key</p>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-yellow-100 text-yellow-800 mr-3">
                                    429
                                </span>
                                <span class="font-medium">Too Many Requests</span>
                            </div>
                            <p class="text-gray-600 text-sm">Rate limit exceeded</p>
                        </div>
                    </div>
                </section>

                <!-- Rate Limits -->
                <section id="rate-limits" class="bg-white rounded-xl border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Rate Limits</h2>
                    <p class="text-gray-600 mb-6">
                        API requests are rate limited to ensure fair usage and system stability.
                    </p>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Free Plan</h3>
                            <p class="text-2xl font-bold text-blue-600 mb-2">1,000 requests/hour</p>
                            <p class="text-gray-600 text-sm">Perfect for personal projects and testing</p>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Pro Plan</h3>
                            <p class="text-2xl font-bold text-green-600 mb-2">10,000 requests/hour</p>
                            <p class="text-gray-600 text-sm">Ideal for production applications</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-900 mb-2">Rate Limit Headers</h4>
                        <p class="text-blue-800 text-sm mb-2">Each response includes rate limit information:</p>
                        <ul class="text-blue-800 text-sm space-y-1">
                            <li><code class="bg-blue-100 px-2 py-1 rounded">X-RateLimit-Limit</code> - Total requests allowed</li>
                            <li><code class="bg-blue-100 px-2 py-1 rounded">X-RateLimit-Remaining</code> - Requests remaining</li>
                            <li><code class="bg-blue-100 px-2 py-1 rounded">X-RateLimit-Reset</code> - Reset time (Unix timestamp)</li>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>

@include('partials.footer')
@endsection
