<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class UserController extends ApiController
{
    /**
     * Get the authenticated user's profile.
     */
    public function profile(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            
            // Get user statistics
            $stats = [
                'total_urls' => $user->urls()->count(),
                'total_clicks' => $user->urls()->sum('click_count'),
                'active_urls' => $user->urls()->where('is_active', true)->count(),
                'urls_this_month' => $user->urls()->where('created_at', '>=', now()->subMonth())->count(),
                'clicks_this_month' => $user->urls()
                    ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                    ->where('analytics.clicked_at', '>=', now()->subMonth())
                    ->count(),
            ];

            $profileData = array_merge($this->transformUser($user), [
                'statistics' => $stats,
                'subscription' => $user->subscription ? [
                    'id' => $user->subscription->id,
                    'name' => $user->subscription->name,
                    'url_limit' => $user->subscription->url_limit,
                    'features' => $user->subscription->features,
                ] : null,
                'api_access' => [
                    'has_api_key' => !is_null($user->api_key),
                    'api_key_created' => $user->api_key ? $user->updated_at->toISOString() : null,
                ],
            ]);

            $response = $this->successResponse(
                $profileData,
                'User profile retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update the authenticated user's profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:255',
                'email' => 'sometimes|required|string|email|max:255|unique:users,email,' . $user->id,
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $updateData = $request->only(['name', 'email']);
            $user->update($updateData);

            $response = $this->successResponse(
                $this->transformUser($user->fresh()),
                'Profile updated successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Change the authenticated user's password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);

            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string',
                'new_password' => ['required', 'confirmed', Password::defaults()],
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            // Verify current password
            if (!Hash::check($request->current_password, $user->password)) {
                return $this->errorResponse(
                    'Current password is incorrect',
                    422,
                    ['current_password' => ['The current password is incorrect']]
                );
            }

            $user->update([
                'password' => Hash::make($request->new_password),
            ]);

            $response = $this->successResponse(
                null,
                'Password changed successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Generate a new API key for the user.
     */
    public function generateApiKey(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $newApiKey = $user->generateApiKey();

            $response = $this->successResponse([
                'api_key' => $newApiKey,
                'generated_at' => now()->toISOString(),
                'warning' => 'Store this API key securely. It will not be shown again.',
            ], 'New API key generated successfully');

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Revoke the user's current API key.
     */
    public function revokeApiKey(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            
            if (!$user->api_key) {
                return $this->errorResponse('No API key to revoke', 400);
            }

            $user->update(['api_key' => null]);

            $response = $this->successResponse(
                null,
                'API key revoked successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get user's account usage statistics.
     */
    public function usage(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $period = $request->get('period', 'month'); // week, month, year

            $startDate = match($period) {
                'week' => now()->subWeek(),
                'month' => now()->subMonth(),
                'year' => now()->subYear(),
                default => now()->subMonth(),
            };

            $usage = [
                'period' => $period,
                'urls' => [
                    'total' => $user->urls()->count(),
                    'created_in_period' => $user->urls()->where('created_at', '>=', $startDate)->count(),
                    'active' => $user->urls()->where('is_active', true)->count(),
                    'limit' => $user->subscription?->url_limit,
                    'percentage_used' => $user->subscription?->url_limit 
                        ? round(($user->urls()->count() / $user->subscription->url_limit) * 100, 2)
                        : null,
                ],
                'clicks' => [
                    'total' => $user->urls()->sum('click_count'),
                    'in_period' => $user->urls()
                        ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                        ->where('analytics.clicked_at', '>=', $startDate)
                        ->count(),
                ],
                'api_usage' => [
                    'has_api_key' => !is_null($user->api_key),
                    'rate_limit_info' => $this->getRateLimitInfo($request),
                ],
                'storage' => [
                    'qr_codes' => $user->urls()->whereNotNull('qr_code_path')->count(),
                    'total_files' => $user->urls()->whereNotNull('qr_code_path')->count(),
                ],
            ];

            $response = $this->successResponse(
                $usage,
                'Usage statistics retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get user's activity log.
     */
    public function activity(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $pagination = $this->validatePagination($request);

            // Get recent URLs created
            $recentUrls = $user->urls()
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'short_code', 'created_at']);

            // Get recent clicks on user's URLs
            $recentClicks = $user->urls()
                ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                ->select('urls.title', 'urls.short_code', 'analytics.country', 'analytics.clicked_at')
                ->orderBy('analytics.clicked_at', 'desc')
                ->limit(20)
                ->get();

            $activity = [
                'recent_urls' => $recentUrls->map(function ($url) {
                    return [
                        'id' => $url->id,
                        'title' => $url->title ?: 'Untitled',
                        'short_code' => $url->short_code,
                        'created_at' => $url->created_at->toISOString(),
                        'action' => 'url_created',
                    ];
                }),
                'recent_clicks' => $recentClicks->map(function ($click) {
                    return [
                        'url_title' => $click->title ?: 'Untitled',
                        'short_code' => $click->short_code,
                        'country' => $click->country,
                        'clicked_at' => $click->clicked_at->toISOString(),
                        'action' => 'url_clicked',
                    ];
                }),
                'summary' => [
                    'urls_created_today' => $user->urls()->whereDate('created_at', today())->count(),
                    'clicks_today' => $user->urls()
                        ->join('analytics', 'urls.id', '=', 'analytics.url_id')
                        ->whereDate('analytics.clicked_at', today())
                        ->count(),
                    'last_login' => $user->updated_at->toISOString(),
                ],
            ];

            $response = $this->successResponse(
                $activity,
                'User activity retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update user preferences.
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);

            $validator = Validator::make($request->all(), [
                'timezone' => 'nullable|string|max:50',
                'language' => 'nullable|string|max:10',
                'email_notifications' => 'boolean',
                'marketing_emails' => 'boolean',
                'default_url_expiry' => 'nullable|integer|min:1',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $metadata = $user->metadata ?? [];
            
            if ($request->has('timezone')) {
                $metadata['timezone'] = $request->timezone;
            }
            if ($request->has('language')) {
                $metadata['language'] = $request->language;
            }
            if ($request->has('email_notifications')) {
                $metadata['email_notifications'] = $request->boolean('email_notifications');
            }
            if ($request->has('marketing_emails')) {
                $metadata['marketing_emails'] = $request->boolean('marketing_emails');
            }
            if ($request->has('default_url_expiry')) {
                $metadata['default_url_expiry'] = $request->default_url_expiry;
            }

            $user->update(['metadata' => $metadata]);

            $response = $this->successResponse(
                ['preferences' => $metadata],
                'Preferences updated successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
