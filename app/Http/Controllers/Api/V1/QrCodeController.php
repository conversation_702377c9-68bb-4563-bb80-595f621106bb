<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Url;
use App\Services\QrCodeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class QrCodeController extends ApiController
{
    protected QrCodeService $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Generate QR code for a URL.
     */
    public function generate(Request $request, string $urlId): JsonResponse
    {
        try {
            if (!$this->isFeatureEnabled('qr_codes')) {
                return $this->errorResponse('QR code feature is not enabled', 403);
            }

            $user = $this->getApiUser($request);
            $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $validator = Validator::make($request->all(), [
                'size' => 'nullable|integer|min:100|max:1000',
                'format' => 'nullable|in:png,svg',
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'margin' => 'nullable|integer|min:0|max:50',
                'error_correction' => 'nullable|in:L,M,Q,H',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $options = [
                'size' => $request->get('size', 300),
                'format' => $request->get('format', 'png'),
                'color' => $request->get('color', '#000000'),
                'background' => $request->get('background', '#ffffff'),
                'margin' => $request->get('margin', 10),
                'error_correction' => $request->get('error_correction', 'M'),
            ];

            $qrCodePath = $this->qrCodeService->generateQrCode($url, $options);

            // Update URL with QR code path if not already set
            if (!$url->qr_code_path) {
                $url->update(['qr_code_path' => $qrCodePath]);
            }

            $qrCodeData = [
                'url_id' => $url->id,
                'qr_code_url' => asset('storage/' . $qrCodePath),
                'download_url' => route('api.v1.qr-codes.download', ['urlId' => $url->id]),
                'options' => $options,
                'generated_at' => now()->toISOString(),
            ];

            $response = $this->successResponse(
                $qrCodeData,
                'QR code generated successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get QR code for a URL.
     */
    public function show(Request $request, string $urlId): JsonResponse
    {
        try {
            if (!$this->isFeatureEnabled('qr_codes')) {
                return $this->errorResponse('QR code feature is not enabled', 403);
            }

            $user = $this->getApiUser($request);
            $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            if (!$url->qr_code_path) {
                return $this->errorResponse('QR code not found for this URL', 404);
            }

            $qrCodeData = [
                'url_id' => $url->id,
                'qr_code_url' => asset('storage/' . $url->qr_code_path),
                'download_url' => route('api.v1.qr-codes.download', ['urlId' => $url->id]),
                'created_at' => $url->updated_at->toISOString(),
            ];

            $response = $this->successResponse(
                $qrCodeData,
                'QR code retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Download QR code file.
     */
    public function download(Request $request, string $urlId)
    {
        try {
            if (!$this->isFeatureEnabled('qr_codes')) {
                return $this->errorResponse('QR code feature is not enabled', 403);
            }

            $user = $this->getApiUser($request);
            $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            if (!$url->qr_code_path) {
                return $this->errorResponse('QR code not found for this URL', 404);
            }

            $filePath = storage_path('app/public/' . $url->qr_code_path);

            if (!file_exists($filePath)) {
                return $this->errorResponse('QR code file not found', 404);
            }

            $filename = 'qr-code-' . $url->short_code . '.' . pathinfo($filePath, PATHINFO_EXTENSION);

            return response()->download($filePath, $filename);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete QR code for a URL.
     */
    public function destroy(Request $request, string $urlId): JsonResponse
    {
        try {
            if (!$this->isFeatureEnabled('qr_codes')) {
                return $this->errorResponse('QR code feature is not enabled', 403);
            }

            $user = $this->getApiUser($request);
            $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            if (!$url->qr_code_path) {
                return $this->errorResponse('QR code not found for this URL', 404);
            }

            // Delete the file
            $filePath = storage_path('app/public/' . $url->qr_code_path);
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Update URL to remove QR code path
            $url->update(['qr_code_path' => null]);

            $response = $this->successResponse(
                null,
                'QR code deleted successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Bulk generate QR codes for multiple URLs.
     */
    public function bulkGenerate(Request $request): JsonResponse
    {
        try {
            if (!$this->isFeatureEnabled('qr_codes')) {
                return $this->errorResponse('QR code feature is not enabled', 403);
            }

            $validator = Validator::make($request->all(), [
                'url_ids' => 'required|array|min:1|max:50',
                'url_ids.*' => 'integer|exists:urls,id',
                'size' => 'nullable|integer|min:100|max:1000',
                'format' => 'nullable|in:png,svg',
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'margin' => 'nullable|integer|min:0|max:50',
                'error_correction' => 'nullable|in:L,M,Q,H',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    422,
                    $validator->errors()->toArray()
                );
            }

            $user = $this->getApiUser($request);
            $urlIds = $request->get('url_ids');

            // Verify user owns all URLs
            $urls = Url::whereIn('id', $urlIds)
                       ->where('user_id', $user->id)
                       ->get();

            if ($urls->count() !== count($urlIds)) {
                return $this->errorResponse(
                    'You can only generate QR codes for your own URLs',
                    403
                );
            }

            $options = [
                'size' => $request->get('size', 300),
                'format' => $request->get('format', 'png'),
                'color' => $request->get('color', '#000000'),
                'background' => $request->get('background', '#ffffff'),
                'margin' => $request->get('margin', 10),
                'error_correction' => $request->get('error_correction', 'M'),
            ];

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            foreach ($urls as $url) {
                try {
                    $qrCodePath = $this->qrCodeService->generateQrCode($url, $options);
                    
                    if (!$url->qr_code_path) {
                        $url->update(['qr_code_path' => $qrCodePath]);
                    }

                    $results[] = [
                        'url_id' => $url->id,
                        'status' => 'success',
                        'qr_code_url' => asset('storage/' . $qrCodePath),
                    ];
                    $successCount++;

                } catch (\Exception $e) {
                    $results[] = [
                        'url_id' => $url->id,
                        'status' => 'error',
                        'error' => $e->getMessage(),
                    ];
                    $errorCount++;
                }
            }

            $response = $this->successResponse([
                'results' => $results,
                'summary' => [
                    'total' => count($urlIds),
                    'success' => $successCount,
                    'errors' => $errorCount,
                ],
                'options' => $options,
            ], "Bulk QR code generation completed. {$successCount} successful, {$errorCount} errors.");

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get QR code customization options.
     */
    public function options(Request $request): JsonResponse
    {
        try {
            $options = [
                'sizes' => [
                    'small' => 150,
                    'medium' => 300,
                    'large' => 600,
                    'xlarge' => 1000,
                ],
                'formats' => ['png', 'svg'],
                'error_correction_levels' => [
                    'L' => 'Low (~7%)',
                    'M' => 'Medium (~15%)',
                    'Q' => 'Quartile (~25%)',
                    'H' => 'High (~30%)',
                ],
                'default_options' => [
                    'size' => 300,
                    'format' => 'png',
                    'color' => '#000000',
                    'background' => '#ffffff',
                    'margin' => 10,
                    'error_correction' => 'M',
                ],
                'limits' => [
                    'min_size' => 100,
                    'max_size' => 1000,
                    'max_margin' => 50,
                    'bulk_limit' => 50,
                ],
            ];

            $response = $this->successResponse(
                $options,
                'QR code options retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
