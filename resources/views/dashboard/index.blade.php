@extends('layouts.modern')

@section('title', 'Dashboard')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <a href="{{ route('home') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Link
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['total_urls']) }}</h3>
                            <p class="mb-0">Total URLs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-link fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['total_clicks']) }}</h3>
                            <p class="mb-0">Total Clicks</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-mouse-pointer fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['urls_today']) }}</h3>
                            <p class="mb-0">URLs Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['clicks_today']) }}</h3>
                            <p class="mb-0">Clicks Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- URLs List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Your URLs</h5>
                    <div>
                        <a href="{{ route('dashboard.urls.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-cog"></i> Manage All URLs
                        </a>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" data-filter="all">All</button>
                            <button class="btn btn-outline-secondary" data-filter="active">Active</button>
                            <button class="btn btn-outline-secondary" data-filter="expired">Expired</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($urls->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Original URL</th>
                                        <th>Short Code</th>
                                        <th>Clicks</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($urls as $url)
                                    <tr class="{{ $url->isExpired() ? 'table-warning' : '' }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($url->title)
                                                    <strong>{{ $url->title }}</strong><br>
                                                @endif
                                                <small class="text-muted">{{ Str::limit($url->original_url, 50) }}</small>
                                                @if($url->isExpired())
                                                    <span class="badge bg-warning ms-2">Expired</span>
                                                @endif
                                                @if($url->isPasswordProtected())
                                                    <i class="fas fa-lock text-warning ms-1" title="Password Protected"></i>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control" value="{{ $url->short_url }}" readonly>
                                                <button class="btn btn-outline-secondary copy-btn" onclick="copyToClipboard('{{ $url->short_url }}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ number_format($url->click_count) }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $url->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('urls.show', $url) }}" class="btn btn-outline-info" title="Analytics">
                                                    <i class="fas fa-chart-bar"></i>
                                                </a>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="QR Code">
                                                        <i class="fas fa-qrcode"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}" target="_blank">
                                                            <i class="fas fa-eye"></i> View QR Code
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="{{ route('qr.download', $url->custom_alias ?: $url->short_code) }}">
                                                            <i class="fas fa-download"></i> Download QR Code
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item" href="{{ route('qr.customize', $url) }}">
                                                            <i class="fas fa-palette"></i> Customize QR Code
                                                        </a></li>
                                                    </ul>
                                                </div>
                                                <a href="{{ route('urls.edit', $url) }}" class="btn btn-outline-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('urls.destroy', $url) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $urls->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <h5>No URLs yet</h5>
                            <p class="text-muted">Create your first shortened URL to get started!</p>
                            <a href="{{ route('home') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Your First URL
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Recent Activity</h5>
                </div>
                <div class="card-body">
                    @if($recentActivity['recent_clicks']->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentActivity['recent_clicks']->take(5) as $click)
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $click->url->title ?: 'Untitled' }}</h6>
                                        <p class="mb-1 small text-muted">{{ Str::limit($click->url->original_url, 40) }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> {{ $click->country_name ?: 'Unknown' }}
                                            <i class="fas fa-mobile-alt ms-2"></i> {{ $click->device_type ?: 'Unknown' }}
                                        </small>
                                    </div>
                                    <small class="text-muted">{{ $click->clicked_at->diffForHumans() }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent activity</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success">{{ $stats['active_urls'] }}</h4>
                            <small class="text-muted">Active URLs</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ $stats['expired_urls'] }}</h4>
                            <small class="text-muted">Expired URLs</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Info -->
            @if(auth()->user()->subscription)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-crown"></i> Subscription</h5>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">{{ auth()->user()->subscription->name }} Plan</h6>
                    <p class="text-muted small mb-2">{{ auth()->user()->subscription->description }}</p>
                    
                    @if(auth()->user()->subscription->url_limit)
                        <div class="progress mb-2" style="height: 6px;">
                            <div class="progress-bar" style="width: {{ ($stats['total_urls'] / auth()->user()->subscription->url_limit) * 100 }}%"></div>
                        </div>
                        <small class="text-muted">{{ $stats['total_urls'] }} / {{ auth()->user()->subscription->url_limit }} URLs used</small>
                    @else
                        <small class="text-success"><i class="fas fa-infinity"></i> Unlimited URLs</small>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Filter functionality
document.querySelectorAll('[data-filter]').forEach(button => {
    button.addEventListener('click', function() {
        // Update active button
        document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        
        // Filter logic would go here
        // For now, this is just UI - you could implement AJAX filtering
    });
});
</script>
@endpush
