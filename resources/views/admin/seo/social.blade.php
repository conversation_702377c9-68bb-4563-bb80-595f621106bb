@extends('layouts.dashboard-template')

@section('page_title', 'Social Media Settings - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-share-alt mr-3 text-blue-600"></i>Social Media Settings
@endsection
@section('page_description', 'Configure Open Graph and Twitter Card settings for social media sharing')

@section('page_actions')
    <a href="{{ route('admin.seo.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to SEO
    </a>
    <a href="{{ route('admin.seo.test') }}" class="btn-primary">
        <i class="fas fa-vial mr-2"></i>Test Social Tags
    </a>
@endsection

@section('main_content')
    <!-- Open Graph Settings -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-facebook mr-2 text-blue-600"></i>Open Graph (Facebook, LinkedIn)
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- OG Title -->
                    <div>
                        <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-heading mr-1 text-blue-500"></i>Open Graph Title
                        </label>
                        <input type="text" 
                               id="og_title" 
                               name="social[og_title]" 
                               class="input-field"
                               value="{{ $socialSettings->where('key', 'og_title')->first()->value ?? 'Minilink.at - Professional URL Shortener' }}"
                               placeholder="Title for social media sharing">
                        <p class="mt-1 text-sm text-gray-500">
                            Title that appears when your site is shared on Facebook, LinkedIn, etc.
                        </p>
                    </div>

                    <!-- OG Description -->
                    <div>
                        <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-align-left mr-1 text-green-500"></i>Open Graph Description
                        </label>
                        <textarea id="og_description" 
                                  name="social[og_description]" 
                                  rows="3" 
                                  class="input-field"
                                  placeholder="Description for social media sharing">{{ $socialSettings->where('key', 'og_description')->first()->value ?? 'Create short, trackable links with advanced analytics and custom domains. Perfect for businesses and marketers.' }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">
                            Description that appears in social media previews
                        </p>
                    </div>

                    <!-- OG Image -->
                    <div>
                        <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-image mr-1 text-purple-500"></i>Open Graph Image URL
                        </label>
                        <input type="url" 
                               id="og_image" 
                               name="social[og_image]" 
                               class="input-field"
                               value="{{ $socialSettings->where('key', 'og_image')->first()->value ?? '' }}"
                               placeholder="https://example.com/image.jpg">
                        <p class="mt-1 text-sm text-gray-500">
                            Image that appears in social media previews (1200x630px recommended)
                        </p>
                    </div>

                    <!-- OG Type -->
                    <div>
                        <label for="og_type" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tag mr-1 text-orange-500"></i>Open Graph Type
                        </label>
                        <select id="og_type" name="social[og_type]" class="input-field">
                            <option value="website" {{ ($socialSettings->where('key', 'og_type')->first()->value ?? 'website') === 'website' ? 'selected' : '' }}>
                                Website
                            </option>
                            <option value="article" {{ ($socialSettings->where('key', 'og_type')->first()->value ?? '') === 'article' ? 'selected' : '' }}>
                                Article
                            </option>
                            <option value="business.business" {{ ($socialSettings->where('key', 'og_type')->first()->value ?? '') === 'business.business' ? 'selected' : '' }}>
                                Business
                            </option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Open Graph Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Twitter Card Settings -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fab fa-twitter mr-2 text-blue-400"></i>Twitter Cards
            </h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.seo.update') }}">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Twitter Card Type -->
                    <div>
                        <label for="twitter_card" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-credit-card mr-1 text-blue-500"></i>Twitter Card Type
                        </label>
                        <select id="twitter_card" name="social[twitter_card]" class="input-field">
                            <option value="summary" {{ ($socialSettings->where('key', 'twitter_card')->first()->value ?? 'summary') === 'summary' ? 'selected' : '' }}>
                                Summary Card
                            </option>
                            <option value="summary_large_image" {{ ($socialSettings->where('key', 'twitter_card')->first()->value ?? '') === 'summary_large_image' ? 'selected' : '' }}>
                                Summary Card with Large Image
                            </option>
                        </select>
                    </div>

                    <!-- Twitter Site -->
                    <div>
                        <label for="twitter_site" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-twitter mr-1 text-blue-400"></i>Twitter Username
                        </label>
                        <input type="text" 
                               id="twitter_site" 
                               name="social[twitter_site]" 
                               class="input-field"
                               value="{{ $socialSettings->where('key', 'twitter_site')->first()->value ?? '' }}"
                               placeholder="@yourusername">
                        <p class="mt-1 text-sm text-gray-500">
                            Your Twitter username (include the @)
                        </p>
                    </div>

                    <!-- Twitter Title -->
                    <div>
                        <label for="twitter_title" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-heading mr-1 text-green-500"></i>Twitter Title
                        </label>
                        <input type="text" 
                               id="twitter_title" 
                               name="social[twitter_title]" 
                               class="input-field"
                               value="{{ $socialSettings->where('key', 'twitter_title')->first()->value ?? 'Minilink.at - Professional URL Shortener' }}"
                               placeholder="Title for Twitter sharing">
                    </div>

                    <!-- Twitter Description -->
                    <div>
                        <label for="twitter_description" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-align-left mr-1 text-purple-500"></i>Twitter Description
                        </label>
                        <textarea id="twitter_description" 
                                  name="social[twitter_description]" 
                                  rows="3" 
                                  class="input-field"
                                  placeholder="Description for Twitter sharing">{{ $socialSettings->where('key', 'twitter_description')->first()->value ?? 'Create short, trackable links with advanced analytics. Perfect for social media marketing.' }}</textarea>
                    </div>

                    <!-- Twitter Image -->
                    <div>
                        <label for="twitter_image" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-image mr-1 text-orange-500"></i>Twitter Image URL
                        </label>
                        <input type="url" 
                               id="twitter_image" 
                               name="social[twitter_image]" 
                               class="input-field"
                               value="{{ $socialSettings->where('key', 'twitter_image')->first()->value ?? '' }}"
                               placeholder="https://example.com/twitter-image.jpg">
                        <p class="mt-1 text-sm text-gray-500">
                            Image for Twitter cards (1024x512px for large image, 400x400px for summary)
                        </p>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Twitter Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
