@extends('layouts.dashboard-template')

@section('page_title', 'Bulk Create URLs - Minilink.at')
@section('page_heading', 'Bulk Create URLs')
@section('page_description', 'Create multiple short URLs at once from a list or CSV file')

@section('page_actions')
    <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to Bulk Operations
    </a>
    <a href="{{ route('dashboard') }}" class="btn-outline">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
    </a>
@endsection

@section('main_content')

    <!-- Creation Methods -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Method 1: Text Input -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-edit mr-2"></i>Method 1: Text Input
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ route('bulk-operations.store') }}" method="POST" id="textForm">
                    @csrf
                    <input type="hidden" name="method" value="text">
                    
                    <div class="space-y-6">
                        <!-- URL List -->
                        <div>
                            <label for="url_list" class="block text-sm font-medium text-gray-700 mb-2">
                                URL List <span class="text-red-500">*</span>
                            </label>
                            <textarea id="url_list" 
                                      name="url_list" 
                                      rows="10"
                                      class="input-field @error('url_list') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                      placeholder="Enter one URL per line:&#10;https://example.com/page1&#10;https://example.com/page2&#10;https://example.com/page3"
                                      required>{{ old('url_list') }}</textarea>
                            @error('url_list')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Enter one URL per line. Maximum 1000 URLs per operation.
                            </p>
                        </div>

                        <!-- Common Settings -->
                        @include('bulk-operations.partials.common-settings')

                        <!-- Submit Button -->
                        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                            <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">Cancel</a>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i>Create URLs
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Method 2: CSV Upload -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-file-csv mr-2"></i>Method 2: CSV Upload
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ route('bulk-operations.store') }}" method="POST" enctype="multipart/form-data" id="csvForm">
                    @csrf
                    <input type="hidden" name="method" value="csv">
                    
                    <div class="space-y-6">
                        <!-- File Upload -->
                        <div>
                            <label for="csv_file" class="block text-sm font-medium text-gray-700 mb-2">
                                CSV File <span class="text-red-500">*</span>
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="csv_file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload a file</span>
                                            <input id="csv_file" name="csv_file" type="file" accept=".csv" class="sr-only" onchange="updateFileName(this)">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">CSV files only, up to 10MB</p>
                                    <p id="fileName" class="text-sm text-gray-900 font-medium hidden"></p>
                                </div>
                            </div>
                            @error('csv_file')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- CSV Format Info -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">CSV Format Requirements:</h4>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• First column: URL (required)</li>
                                <li>• Second column: Title (optional)</li>
                                <li>• Third column: Description (optional)</li>
                                <li>• No header row required</li>
                            </ul>
                            <div class="mt-3">
                                <a href="{{ route('bulk-operations.download-template') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                    <i class="fas fa-download mr-1"></i>Download CSV Template
                                </a>
                            </div>
                        </div>

                        <!-- Common Settings -->
                        @include('bulk-operations.partials.common-settings')

                        <!-- Submit Button -->
                        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                            <a href="{{ route('bulk-operations.index') }}" class="btn-secondary">Cancel</a>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-upload mr-2"></i>Upload & Create URLs
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Tips & Guidelines -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Tips & Best Practices</h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-3">URL Requirements</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-2"></i>
                                URLs must include http:// or https://
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-2"></i>
                                Maximum 1000 URLs per operation
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-2"></i>
                                Duplicate URLs will be skipped
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-2"></i>
                                Invalid URLs will be reported
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-900 mb-3">Processing Information</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                Large operations run in background
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                You'll receive email notification when complete
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                Progress can be tracked in operations history
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                Failed URLs will be listed in results
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateFileName(input) {
    const fileName = document.getElementById('fileName');
    if (input.files && input.files[0]) {
        fileName.textContent = input.files[0].name;
        fileName.classList.remove('hidden');
    } else {
        fileName.classList.add('hidden');
    }
}

// URL validation
document.getElementById('url_list').addEventListener('input', function() {
    const urls = this.value.split('\n').filter(url => url.trim());
    const urlCount = urls.length;
    
    // Update counter (you can add a counter element if needed)
    if (urlCount > 1000) {
        this.classList.add('border-red-300');
    } else {
        this.classList.remove('border-red-300');
    }
});

// Form validation
document.getElementById('textForm').addEventListener('submit', function(e) {
    const urlList = document.getElementById('url_list').value.trim();
    if (!urlList) {
        e.preventDefault();
        alert('Please enter at least one URL.');
        return;
    }
    
    const urls = urlList.split('\n').filter(url => url.trim());
    if (urls.length > 1000) {
        e.preventDefault();
        alert('Maximum 1000 URLs allowed per operation.');
        return;
    }
});

document.getElementById('csvForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('csv_file');
    if (!fileInput.files || !fileInput.files[0]) {
        e.preventDefault();
        alert('Please select a CSV file to upload.');
        return;
    }
});
</script>
@endpush
