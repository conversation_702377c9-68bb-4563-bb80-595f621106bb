@extends('layouts.modern')

@section('title', 'Admin Dashboard - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                <p class="text-gray-600 mt-1">System overview and management tools</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('admin.users.index') }}" class="btn-primary">
                    <i class="fas fa-users mr-2"></i>Manage Users
                </a>
                <a href="{{ route('admin.settings.index') }}" class="btn-secondary">
                    <i class="fas fa-cog mr-2"></i>Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <a href="{{ route('admin.users.index') }}" class="btn-outline text-center">
                    <i class="fas fa-users mr-2"></i>Manage Users
                </a>
                <a href="{{ route('admin.urls.index') }}" class="btn-outline text-center">
                    <i class="fas fa-link mr-2"></i>Manage URLs
                </a>
                <a href="{{ route('admin.settings.index') }}" class="btn-outline text-center">
                    <i class="fas fa-cog mr-2"></i>System Settings
                </a>
                <a href="{{ route('admin.seo.index') }}" class="btn-outline text-center">
                    <i class="fas fa-search mr-2"></i>SEO Management
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $stats['new_users_this_month'] }} this month
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total URLs -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-link text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total URLs</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_urls']) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $stats['new_urls_this_month'] }} this month
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Clicks -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-mouse-pointer text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Clicks</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_clicks']) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $stats['clicks_today'] }} today
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">${{ number_format($stats['monthly_revenue'] / 100, 2) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $stats['paid_users'] }} paid users
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- User Growth Chart -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">User Growth</h3>
            </div>
            <div class="card-body">
                <canvas id="userGrowthChart" height="300"></canvas>
            </div>
        </div>

        <!-- URL Creation Chart -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">URL Creation</h3>
            </div>
            <div class="card-body">
                <canvas id="urlCreationChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Management Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Users -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Users</h3>
                    <a href="{{ route('admin.users.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="divide-y divide-gray-200">
                    @foreach($recentUsers as $user)
                    <div class="p-4 hover:bg-gray-50">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <span class="text-blue-600 font-semibold text-sm">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">{{ $user->name }}</div>
                                <div class="text-sm text-gray-500 truncate">{{ $user->email }}</div>
                                <div class="text-xs text-gray-400">{{ $user->created_at->diffForHumans() }}</div>
                            </div>
                            <div class="flex-shrink-0">
                                @if($user->subscription && $user->subscription->price > 0)
                                    <span class="badge badge-primary">{{ $user->subscription->name }}</span>
                                @else
                                    <span class="badge badge-secondary">Free</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Recent URLs -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent URLs</h3>
                    <a href="{{ route('admin.urls.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="divide-y divide-gray-200">
                    @foreach($recentUrls as $url)
                    <div class="p-4 hover:bg-gray-50">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <i class="fas fa-link text-green-600"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">
                                    {{ $url->title ?: 'Untitled' }}
                                </div>
                                <div class="text-sm text-gray-500 truncate">{{ $url->original_url }}</div>
                                <div class="text-xs text-blue-600 font-mono">{{ $url->short_url }}</div>
                                <div class="text-xs text-gray-400">{{ $url->created_at->diffForHumans() }}</div>
                            </div>
                            <div class="flex-shrink-0 text-right">
                                <div class="text-sm font-medium text-gray-900">{{ number_format($url->click_count) }}</div>
                                <div class="text-xs text-gray-500">clicks</div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- System Status and Subscriptions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- System Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">System Status</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Database</span>
                        <span class="badge badge-success">
                            <i class="fas fa-check mr-1"></i>Healthy
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Cache</span>
                        <span class="badge badge-success">
                            <i class="fas fa-check mr-1"></i>Active
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Queue</span>
                        <span class="badge badge-success">
                            <i class="fas fa-check mr-1"></i>Running
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Storage</span>
                        <span class="badge badge-warning">
                            <i class="fas fa-exclamation mr-1"></i>75% Used
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Overview -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Subscriptions</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    @foreach($subscriptionStats as $stat)
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ $stat['name'] }}</span>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">{{ $stat['count'] }}</div>
                            <div class="text-xs text-gray-500">users</div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// User Growth Chart
const userCtx = document.getElementById('userGrowthChart').getContext('2d');
const userGrowthChart = new Chart(userCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($chartData['userGrowth']['labels']) !!},
        datasets: [{
            label: 'New Users',
            data: {!! json_encode($chartData['userGrowth']['data']) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// URL Creation Chart
const urlCtx = document.getElementById('urlCreationChart').getContext('2d');
const urlCreationChart = new Chart(urlCtx, {
    type: 'bar',
    data: {
        labels: {!! json_encode($chartData['urlCreation']['labels']) !!},
        datasets: [{
            label: 'URLs Created',
            data: {!! json_encode($chartData['urlCreation']['data']) !!},
            backgroundColor: 'rgba(34, 197, 94, 0.8)',
            borderColor: 'rgb(34, 197, 94)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
@endpush
