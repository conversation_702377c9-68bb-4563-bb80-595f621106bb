<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Url;
use App\Models\Subscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected string $apiKey;

    protected function setUp(): void
    {
        parent::setUp();

        // Create subscription with API access
        $subscription = Subscription::factory()->create([
            'name' => 'Pro',
            'price' => 999,
            'api_access' => true,
            'url_limit' => 1000,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create([
            'subscription_id' => $subscription->id,
        ]);

        $this->apiKey = $this->user->generateApiKey();
    }

    public function test_api_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/urls');

        $response->assertStatus(401)
                 ->assertJson(['error' => 'Unauthorized']);
    }

    public function test_api_accepts_valid_api_key(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->getJson('/api/v1/urls');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data',
                 ]);
    }

    public function test_api_can_create_url(): void
    {
        $urlData = [
            'original_url' => 'https://example.com',
            'title' => 'API Test URL',
            'description' => 'Created via API',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->postJson('/api/v1/urls', $urlData);

        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'id',
                         'original_url',
                         'short_url',
                         'title',
                         'description',
                     ],
                 ]);

        $this->assertDatabaseHas('urls', [
            'original_url' => 'https://example.com',
            'title' => 'API Test URL',
            'user_id' => $this->user->id,
        ]);
    }

    public function test_api_can_retrieve_url(): void
    {
        $url = Url::factory()->create([
            'user_id' => $this->user->id,
            'title' => 'Test URL',
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->getJson("/api/v1/urls/{$url->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'id' => $url->id,
                         'title' => 'Test URL',
                     ],
                 ]);
    }

    public function test_api_can_update_url(): void
    {
        $url = Url::factory()->create([
            'user_id' => $this->user->id,
            'title' => 'Original Title',
        ]);

        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated Description',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->putJson("/api/v1/urls/{$url->id}", $updateData);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'title' => 'Updated Title',
                         'description' => 'Updated Description',
                     ],
                 ]);
    }

    public function test_api_can_delete_url(): void
    {
        $url = Url::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->deleteJson("/api/v1/urls/{$url->id}");

        $response->assertStatus(200)
                 ->assertJson(['success' => true]);

        $this->assertDatabaseMissing('urls', ['id' => $url->id]);
    }

    public function test_api_rate_limiting_works(): void
    {
        // Make multiple requests quickly
        for ($i = 0; $i < 15; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->getJson('/api/v1/test/rate-limit');
        }

        // Should get rate limited
        $response->assertStatus(429)
                 ->assertJson(['error' => 'Rate limit exceeded']);
    }

    public function test_api_returns_user_profile(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->getJson('/api/v1/user/profile');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'id' => $this->user->id,
                         'name' => $this->user->name,
                         'email' => $this->user->email,
                     ],
                 ]);
    }

    public function test_api_validates_input(): void
    {
        $invalidData = [
            'original_url' => 'not-a-url',
            'title' => str_repeat('a', 300), // Too long
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->postJson('/api/v1/urls', $invalidData);

        $response->assertStatus(422)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'errors',
                 ]);
    }

    public function test_api_health_check(): void
    {
        $response = $this->getJson('/api/v1/health');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'status',
                         'timestamp',
                         'version',
                         'database',
                     ],
                 ]);
    }

    public function test_api_info_endpoint(): void
    {
        $response = $this->getJson('/api/v1/');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'api_name',
                         'version',
                         'description',
                         'endpoints',
                     ],
                 ]);
    }
}
