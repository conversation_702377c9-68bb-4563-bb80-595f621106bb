@extends('layouts.modern')

@section('title', 'Domain: ' . $customDomain->domain)

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-globe"></i> {{ $customDomain->domain }}</h1>
                    <p class="text-muted mb-0">Custom domain management and verification</p>
                </div>
                <div>
                    <a href="{{ route('custom-domains.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Domains
                    </a>
                    @if($customDomain->is_verified)
                        <a href="{{ route('custom-domains.stats', $customDomain) }}" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> Statistics
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Domain Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Domain Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                @if($customDomain->is_verified)
                                    <i class="fas fa-check-circle fa-3x text-success"></i>
                                @else
                                    <i class="fas fa-clock fa-3x text-warning"></i>
                                @endif
                            </div>
                            <h6>Verification</h6>
                            <span class="badge bg-{{ $customDomain->is_verified ? 'success' : 'warning' }}">
                                {{ $customDomain->is_verified ? 'Verified' : 'Pending' }}
                            </span>
                            @if($customDomain->verified_at)
                                <div class="text-muted small mt-1">
                                    {{ $customDomain->verified_at->format('M j, Y') }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                @if($customDomain->is_active)
                                    <i class="fas fa-power-off fa-3x text-primary"></i>
                                @else
                                    <i class="fas fa-pause fa-3x text-secondary"></i>
                                @endif
                            </div>
                            <h6>Status</h6>
                            <span class="badge bg-{{ $customDomain->is_active ? 'primary' : 'secondary' }}">
                                {{ $customDomain->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            @if($customDomain->is_verified && !$customDomain->is_active)
                                <form action="{{ route('custom-domains.update', $customDomain) }}" method="POST" class="mt-2">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="is_active" value="1">
                                    <button type="submit" class="btn btn-sm btn-outline-primary">Activate</button>
                                </form>
                            @endif
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                @if($customDomain->ssl_enabled)
                                    <i class="fas fa-lock fa-3x text-info"></i>
                                @else
                                    <i class="fas fa-unlock fa-3x text-muted"></i>
                                @endif
                            </div>
                            <h6>SSL Certificate</h6>
                            <span class="badge bg-{{ $customDomain->ssl_enabled ? 'info' : 'secondary' }}">
                                {{ $customDomain->ssl_enabled ? 'Enabled' : 'Disabled' }}
                            </span>
                            @if($customDomain->ssl_expires_at)
                                <div class="text-muted small mt-1">
                                    Expires {{ $customDomain->ssl_expires_at->format('M j, Y') }}
                                </div>
                            @endif
                            @if($customDomain->is_verified)
                                <form action="{{ route('custom-domains.check-ssl', $customDomain) }}" method="POST" class="mt-2">
                                    @csrf
                                    <button type="submit" class="btn btn-sm btn-outline-info">Check SSL</button>
                                </form>
                            @endif
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <i class="fas fa-link fa-3x text-success"></i>
                            </div>
                            <h6>URLs Created</h6>
                            <h4 class="mb-0">{{ $customDomain->urls->count() }}</h4>
                            <div class="text-muted small">
                                {{ number_format($customDomain->urls->sum('click_count')) }} total clicks
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(!$customDomain->is_verified)
    <!-- Verification Instructions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Domain Verification Required</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">To verify ownership of your domain, please add the following DNS records:</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-shield-alt"></i> Step 1: Verification Record</h6>
                            <div class="bg-light p-3 rounded mb-3">
                                <div class="row">
                                    <div class="col-3"><strong>Type:</strong></div>
                                    <div class="col-9"><code>{{ $verificationInstructions['txt_record']['type'] }}</code></div>
                                </div>
                                <div class="row">
                                    <div class="col-3"><strong>Name:</strong></div>
                                    <div class="col-9">
                                        <code>{{ $verificationInstructions['txt_record']['name'] }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $verificationInstructions['txt_record']['name'] }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-3"><strong>Value:</strong></div>
                                    <div class="col-9">
                                        <code>{{ $verificationInstructions['txt_record']['value'] }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $verificationInstructions['txt_record']['value'] }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6><i class="fas fa-route"></i> Step 2: Routing Record</h6>
                            <div class="bg-light p-3 rounded mb-3">
                                <div class="row">
                                    <div class="col-3"><strong>Type:</strong></div>
                                    <div class="col-9"><code>{{ $verificationInstructions['cname_record']['type'] }}</code></div>
                                </div>
                                <div class="row">
                                    <div class="col-3"><strong>Name:</strong></div>
                                    <div class="col-9">
                                        <code>{{ $verificationInstructions['cname_record']['name'] }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $verificationInstructions['cname_record']['name'] }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-3"><strong>Value:</strong></div>
                                    <div class="col-9">
                                        <code>{{ $verificationInstructions['cname_record']['value'] }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $verificationInstructions['cname_record']['value'] }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Alternative: A Record</h6>
                        <p class="mb-2">If you cannot use CNAME records, you can use an A record instead:</p>
                        <code>{{ $customDomain->domain }} → {{ $verificationInstructions['a_record']['value'] }}</code>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <form action="{{ route('custom-domains.verify', $customDomain) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check"></i> Verify Domain
                                </button>
                            </form>
                            
                            <form action="{{ route('custom-domains.regenerate-token', $customDomain) }}" method="POST" class="d-inline ms-2">
                                @csrf
                                <button type="submit" class="btn btn-outline-warning">
                                    <i class="fas fa-refresh"></i> Regenerate Token
                                </button>
                            </form>
                        </div>
                        
                        <form action="{{ route('custom-domains.test', $customDomain) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-info">
                                <i class="fas fa-vial"></i> Test Configuration
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent URLs -->
    @if($customDomain->urls->count() > 0)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-link"></i> Recent URLs</h5>
                    <a href="{{ route('dashboard.urls.index', ['domain' => $customDomain->id]) }}" class="btn btn-sm btn-outline-primary">
                        View All URLs
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Short URL</th>
                                    <th>Clicks</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customDomain->urls->take(5) as $url)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $url->title ?: 'Untitled' }}</strong>
                                            @if($url->description)
                                                <br><small class="text-muted">{{ Str::limit($url->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ $url->short_url }}" target="_blank" class="text-decoration-none">
                                            {{ $url->short_url }}
                                            <i class="fas fa-external-link-alt fa-sm"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ number_format($url->click_count) }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $url->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('dashboard.urls.show', $url) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check"></i> Copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy to clipboard');
    });
}

// Auto-refresh verification status
document.addEventListener('DOMContentLoaded', function() {
    @if(!$customDomain->is_verified)
        // Auto-refresh every 30 seconds if domain is not verified
        setTimeout(() => {
            location.reload();
        }, 30000);
    @endif
});
</script>
@endpush
