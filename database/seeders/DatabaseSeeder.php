<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed subscription plans first
        $this->call(SubscriptionSeeder::class);

        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'role' => 'admin',
            'subscription_id' => 3, // Premium plan
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
            'subscription_id' => 1, // Free plan
        ]);
    }
}
