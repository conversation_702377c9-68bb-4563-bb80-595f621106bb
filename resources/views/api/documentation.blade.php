@extends('layouts.modern')

@section('title', 'API Documentation')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-code"></i> API Documentation</h1>
                    <p class="text-muted mb-0">Complete guide to the Minilink.at RESTful API</p>
                </div>
                <div>
                    <a href="{{ route('profile.api-keys') }}" class="btn btn-primary">
                        <i class="fas fa-key"></i> Get API Key
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- API Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> API Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Base URL</h6>
                            <code>{{ config('app.url') }}/api/v1</code>
                            
                            <h6 class="mt-3">Authentication</h6>
                            <p>Include your API key in the Authorization header:</p>
                            <code>Authorization: Bearer YOUR_API_KEY</code>
                            
                            <h6 class="mt-3">Rate Limits</h6>
                            <ul class="list-unstyled">
                                <li><strong>1000</strong> requests per hour</li>
                                <li><strong>60</strong> requests per minute (burst)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Response Format</h6>
                            <p>All responses are in JSON format with the following structure:</p>
                            <pre class="bg-light p-3 rounded"><code>{
  "success": true,
  "message": "Success message",
  "data": { ... },
  "status_code": 200
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Endpoints -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> API Endpoints</h5>
                </div>
                <div class="card-body">
                    <!-- URL Management -->
                    <h6 class="text-primary border-bottom pb-2">URL Management</h6>
                    <div class="table-responsive mb-4">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/urls</code></td>
                                    <td>Get all URLs for authenticated user</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">POST</span></td>
                                    <td><code>/api/v1/urls</code></td>
                                    <td>Create a new short URL</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/urls/{id}</code></td>
                                    <td>Get specific URL details</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">PUT</span></td>
                                    <td><code>/api/v1/urls/{id}</code></td>
                                    <td>Update URL details</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger">DELETE</span></td>
                                    <td><code>/api/v1/urls/{id}</code></td>
                                    <td>Delete a URL</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/urls/{id}/stats</code></td>
                                    <td>Get URL statistics</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">POST</span></td>
                                    <td><code>/api/v1/urls/bulk</code></td>
                                    <td>Bulk operations on URLs</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Analytics -->
                    <h6 class="text-primary border-bottom pb-2">Analytics</h6>
                    <div class="table-responsive mb-4">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/analytics/summary</code></td>
                                    <td>Get analytics summary for all URLs</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/analytics/trends</code></td>
                                    <td>Get click trends over time</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/analytics/realtime</code></td>
                                    <td>Get real-time analytics (last 24h)</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/analytics/urls/{id}</code></td>
                                    <td>Get analytics for specific URL</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/analytics/export</code></td>
                                    <td>Export analytics data</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- User Management -->
                    <h6 class="text-primary border-bottom pb-2">User Management</h6>
                    <div class="table-responsive mb-4">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/user/profile</code></td>
                                    <td>Get user profile and statistics</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">PUT</span></td>
                                    <td><code>/api/v1/user/profile</code></td>
                                    <td>Update user profile</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">PUT</span></td>
                                    <td><code>/api/v1/user/password</code></td>
                                    <td>Change user password</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/user/usage</code></td>
                                    <td>Get account usage statistics</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/user/activity</code></td>
                                    <td>Get user activity log</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">POST</span></td>
                                    <td><code>/api/v1/user/api-key/generate</code></td>
                                    <td>Generate new API key</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger">DELETE</span></td>
                                    <td><code>/api/v1/user/api-key</code></td>
                                    <td>Revoke current API key</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- QR Codes -->
                    <h6 class="text-primary border-bottom pb-2">QR Codes</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-primary">POST</span></td>
                                    <td><code>/api/v1/qr-codes/urls/{id}/generate</code></td>
                                    <td>Generate QR code for URL</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/qr-codes/urls/{id}</code></td>
                                    <td>Get QR code for URL</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/qr-codes/urls/{id}/download</code></td>
                                    <td>Download QR code file</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger">DELETE</span></td>
                                    <td><code>/api/v1/qr-codes/urls/{id}</code></td>
                                    <td>Delete QR code</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">POST</span></td>
                                    <td><code>/api/v1/qr-codes/bulk-generate</code></td>
                                    <td>Bulk generate QR codes</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">GET</span></td>
                                    <td><code>/api/v1/qr-codes/options</code></td>
                                    <td>Get QR code customization options</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Examples -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-code"></i> Example Requests</h5>
                </div>
                <div class="card-body">
                    <h6>Create a Short URL</h6>
                    <pre class="bg-light p-3 rounded"><code>curl -X POST {{ config('app.url') }}/api/v1/urls \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "original_url": "https://example.com",
    "title": "Example Website",
    "description": "This is an example website"
  }'</code></pre>

                    <h6 class="mt-4">Get URL Statistics</h6>
                    <pre class="bg-light p-3 rounded"><code>curl -X GET {{ config('app.url') }}/api/v1/urls/123/stats \
  -H "Authorization: Bearer YOUR_API_KEY"</code></pre>

                    <h6 class="mt-4">Generate QR Code</h6>
                    <pre class="bg-light p-3 rounded"><code>curl -X POST {{ config('app.url') }}/api/v1/qr-codes/urls/123/generate \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "size": 300,
    "format": "png",
    "color": "#000000"
  }'</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
