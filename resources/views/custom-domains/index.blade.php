@extends('layouts.admin-modern')

@section('title', 'Custom Domains - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-globe mr-3 text-blue-600"></i>
                    Custom Domains
                </h1>
                <p class="text-gray-600 mt-1">Manage your custom domains for branded short URLs</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
                <a href="{{ route('dashboard') }}" class="btn-secondary">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                @can('create', App\Models\CustomDomain::class)
                <a href="{{ route('custom-domains.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Add Domain
                </a>
                @else
                <button class="btn-secondary opacity-50 cursor-not-allowed" disabled title="Domain limit reached">
                    <i class="fas fa-plus mr-2"></i>Add Domain
                </button>
                @endcan
            </div>
        </div>
    </div>

    @if($domains->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($domains as $domain)
            <div class="card h-full">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-globe mr-2 text-blue-600"></i>
                            {{ $domain->domain }}
                        </h3>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="btn-outline btn-sm">
                                <i class="fas fa-cog"></i>
                            </button>
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <div class="py-1">
                                    <a href="{{ route('custom-domains.show', $domain) }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-eye mr-2"></i>View Details
                                    </a>
                                    <a href="{{ route('custom-domains.stats', $domain) }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-chart-bar mr-2"></i>Statistics
                                    </a>
                                    @if(!$domain->is_verified)
                                    <hr class="my-1">
                                    <a href="{{ route('custom-domains.edit', $domain) }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-edit mr-2"></i>Settings
                                    </a>
                                    @endif
                                    <hr class="my-1">
                                    <form action="{{ route('custom-domains.destroy', $domain) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                                                onclick="return confirm('Are you sure you want to delete this domain?')">
                                            <i class="fas fa-trash mr-2"></i>Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Status Badges -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        @if($domain->is_verified)
                            <span class="badge badge-success">
                                <i class="fas fa-check-circle mr-1"></i>Verified
                            </span>
                        @else
                            <span class="badge badge-warning">
                                <i class="fas fa-clock mr-1"></i>Pending Verification
                            </span>
                        @endif

                        @if($domain->is_active)
                            <span class="badge badge-primary">
                                <i class="fas fa-power-off mr-1"></i>Active
                            </span>
                        @else
                            <span class="badge badge-secondary">
                                <i class="fas fa-pause mr-1"></i>Inactive
                            </span>
                        @endif

                        @if($domain->ssl_enabled)
                            <span class="badge badge-info">
                                <i class="fas fa-lock mr-1"></i>SSL
                            </span>
                        @endif
                    </div>

                    <!-- Domain Stats -->
                    <div class="grid grid-cols-2 gap-4 text-center mb-4">
                        <div class="border-r border-gray-200">
                            <div class="text-2xl font-bold text-gray-900">{{ $domain->urls_count }}</div>
                            <div class="text-sm text-gray-500">URLs</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-900">{{ number_format($domain->urls->sum('click_count')) }}</div>
                            <div class="text-sm text-gray-500">Clicks</div>
                        </div>
                    </div>

                    <!-- SSL Expiry Warning -->
                    @if($domain->ssl_enabled && $domain->ssl_expires_at)
                        @php
                            $daysUntilExpiry = now()->diffInDays($domain->ssl_expires_at, false);
                        @endphp
                        @if($daysUntilExpiry <= 30 && $daysUntilExpiry > 0)
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-yellow-800">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    SSL expires in {{ $daysUntilExpiry }} days
                                </div>
                            </div>
                        @elseif($daysUntilExpiry <= 0)
                            <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-red-800">
                                    <i class="fas fa-times-circle mr-2"></i>
                                    SSL certificate expired
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
                <div class="card-footer">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">
                            Added {{ $domain->created_at->diffForHumans() }}
                        </span>
                            @if(!$domain->is_verified)
                            <form action="{{ route('custom-domains.verify', $domain) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="btn-success btn-sm">
                                    <i class="fas fa-check mr-1"></i>Verify
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    @else
        <div class="card">
            <div class="card-body text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-globe text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Custom Domains</h3>
                <p class="text-gray-600 mb-6">
                    Add a custom domain to create branded short URLs with your own domain name.
                </p>
                @can('create', App\Models\CustomDomain::class)
                    <a href="{{ route('custom-domains.create') }}" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>Add Your First Domain
                    </a>
                @else
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                        <div class="flex items-center text-blue-800">
                            <i class="fas fa-info-circle mr-2"></i>
                            You have reached your custom domain limit. Upgrade your subscription to add more domains.
                        </div>
                    </div>
                @endcan
            </div>
        </div>
    @endif

    <!-- Domain Information -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                Custom Domain Information
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Current Usage</h4>
                    <p class="text-gray-600 mb-4">
                        <span class="font-semibold">{{ $domains->count() }}</span> of
                        <span class="font-semibold">{{ Auth::user()->subscription?->custom_domains_limit ?? 1 }}</span> domains used
                    </p>

                    <h4 class="font-medium text-gray-900 mb-3">Benefits</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Branded short URLs
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Increased trust and recognition
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            SSL certificate support
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Custom analytics tracking
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Requirements</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-arrow-right text-blue-500 mr-2"></i>
                            Domain ownership verification
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-arrow-right text-blue-500 mr-2"></i>
                            DNS configuration (CNAME or A record)
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-arrow-right text-blue-500 mr-2"></i>
                            TXT record for verification
                        </li>
                        <li class="flex items-center text-gray-600">
                            <i class="fas fa-arrow-right text-blue-500 mr-2"></i>
                            Optional SSL certificate setup
                        </li>
                    </ul>

                    @if($domains->count() >= (Auth::user()->subscription?->custom_domains_limit ?? 1))
                        <div class="mt-4">
                            <a href="{{ route('subscriptions.index') }}" class="btn-primary">
                                <i class="fas fa-arrow-up mr-2"></i>Upgrade Subscription
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-refresh verification status
document.addEventListener('DOMContentLoaded', function() {
    // Check for pending verifications and auto-refresh every 30 seconds
    const pendingDomains = document.querySelectorAll('.badge:contains("Pending")');
    if (pendingDomains.length > 0) {
        setTimeout(() => {
            location.reload();
        }, 30000);
    }
});
</script>
@endpush
