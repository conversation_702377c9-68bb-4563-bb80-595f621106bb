<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Controllers\Api\V1\UrlController;
use App\Http\Controllers\Api\V1\AnalyticsController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\QrCodeController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // API Information and Health Check
    Route::get('/', [ApiController::class, 'info'])->name('api.v1.info');
    Route::get('/health', [ApiController::class, 'health'])->name('api.v1.health');
});

// Authenticated API routes
Route::prefix('v1')->middleware(['api.auth', 'api.rate_limit:1000,60'])->group(function () {
    
    // URL Management
    Route::prefix('urls')->name('api.v1.urls.')->group(function () {
        Route::get('/', [UrlController::class, 'index'])->name('index');
        Route::post('/', [UrlController::class, 'store'])->name('store');
        Route::get('/{id}', [UrlController::class, 'show'])->name('show');
        Route::put('/{id}', [UrlController::class, 'update'])->name('update');
        Route::delete('/{id}', [UrlController::class, 'destroy'])->name('destroy');
        Route::get('/{id}/stats', [UrlController::class, 'stats'])->name('stats');
        Route::post('/bulk', [UrlController::class, 'bulk'])->name('bulk');
    });

    // Analytics
    Route::prefix('analytics')->name('api.v1.analytics.')->group(function () {
        Route::get('/summary', [AnalyticsController::class, 'summary'])->name('summary');
        Route::get('/trends', [AnalyticsController::class, 'trends'])->name('trends');
        Route::get('/realtime', [AnalyticsController::class, 'realtime'])->name('realtime');
        Route::get('/export', [AnalyticsController::class, 'export'])->name('export');
        Route::get('/urls/{urlId}', [AnalyticsController::class, 'show'])->name('show');
        Route::get('/download/{filename}', function($filename) {
            $filePath = storage_path('app/exports/' . $filename);
            if (!file_exists($filePath)) {
                abort(404, 'Export file not found.');
            }
            return response()->download($filePath)->deleteFileAfterSend();
        })->name('download');
    });

    // User Management
    Route::prefix('user')->name('api.v1.user.')->group(function () {
        Route::get('/profile', [UserController::class, 'profile'])->name('profile');
        Route::put('/profile', [UserController::class, 'updateProfile'])->name('update-profile');
        Route::put('/password', [UserController::class, 'changePassword'])->name('change-password');
        Route::post('/api-key/generate', [UserController::class, 'generateApiKey'])->name('generate-api-key');
        Route::delete('/api-key', [UserController::class, 'revokeApiKey'])->name('revoke-api-key');
        Route::get('/usage', [UserController::class, 'usage'])->name('usage');
        Route::get('/activity', [UserController::class, 'activity'])->name('activity');
        Route::put('/preferences', [UserController::class, 'updatePreferences'])->name('update-preferences');
    });

    // QR Codes
    Route::prefix('qr-codes')->name('api.v1.qr-codes.')->group(function () {
        Route::get('/options', [QrCodeController::class, 'options'])->name('options');
        Route::post('/urls/{urlId}/generate', [QrCodeController::class, 'generate'])->name('generate');
        Route::get('/urls/{urlId}', [QrCodeController::class, 'show'])->name('show');
        Route::get('/urls/{urlId}/download', [QrCodeController::class, 'download'])->name('download');
        Route::delete('/urls/{urlId}', [QrCodeController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-generate', [QrCodeController::class, 'bulkGenerate'])->name('bulk-generate');
    });
});

// API Documentation routes (if using a documentation package)
Route::prefix('docs')->group(function () {
    // These would be handled by a documentation package like L5-Swagger
    // Route::get('/', 'ApiDocumentationController@index')->name('api.docs');
    // Route::get('/json', 'ApiDocumentationController@json')->name('api.docs.json');
});

// Webhook routes (for future use)
Route::prefix('webhooks')->group(function () {
    // Route::post('/analytics', 'WebhookController@analytics')->name('api.webhooks.analytics');
    // Route::post('/url-clicked', 'WebhookController@urlClicked')->name('api.webhooks.url-clicked');
});

// Rate limit testing route (for development)
if (config('app.debug')) {
    Route::prefix('v1')->middleware(['api.auth'])->group(function () {
        Route::get('/test/rate-limit', function () {
            return response()->json([
                'message' => 'Rate limit test endpoint',
                'timestamp' => now()->toISOString(),
            ]);
        })->middleware('api.rate_limit:10,1'); // 10 requests per minute for testing
    });
}
