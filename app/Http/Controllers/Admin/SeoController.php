<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SeoSetting;
use App\Services\SeoService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class Seo<PERSON>ontroller extends Controller
{
    protected SeoService $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->middleware(['auth', 'admin']);
        $this->seoService = $seoService;
    }

    /**
     * Display SEO settings dashboard.
     */
    public function index(): View
    {
        $settings = SeoSetting::orderBy('category')->orderBy('key')->get()->groupBy('category');
        
        $analytics = $this->seoService->generateAnalyticsCodes();
        $verification = $this->seoService->generateVerificationTags();

        return view('admin.seo.index', compact('settings', 'analytics', 'verification'));
    }

    /**
     * Show meta tags management.
     */
    public function meta(): View
    {
        $metaSettings = SeoSetting::where('category', 'meta')->get();
        
        return view('admin.seo.meta', compact('metaSettings'));
    }

    /**
     * Show social media settings.
     */
    public function social(): View
    {
        $socialSettings = SeoSetting::where('category', 'social')->get();
        
        return view('admin.seo.social', compact('socialSettings'));
    }

    /**
     * Show analytics settings.
     */
    public function analytics(): View
    {
        $analyticsSettings = SeoSetting::where('category', 'analytics')->get();
        
        return view('admin.seo.analytics', compact('analyticsSettings'));
    }

    /**
     * Show verification settings.
     */
    public function verification(): View
    {
        $verificationSettings = SeoSetting::where('category', 'verification')->get();
        
        return view('admin.seo.verification', compact('verificationSettings'));
    }

    /**
     * Update SEO settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string|max:2000',
        ]);

        foreach ($request->input('settings', []) as $key => $value) {
            $setting = SeoSetting::where('key', $key)->first();
            
            if ($setting) {
                $processedValue = match ($setting->type) {
                    'boolean' => $request->has("settings.{$key}") ? '1' : '0',
                    'json' => json_encode($value),
                    default => (string) $value,
                };

                $setting->update(['value' => $processedValue]);
            }
        }

        // Clear cache
        SeoSetting::clearCache();

        return redirect()->back()->with('success', 'SEO settings updated successfully!');
    }

    /**
     * Generate and download robots.txt.
     */
    public function robotsTxt(): Response
    {
        $content = $this->seoService->generateRobotsTxt();

        return response($content)
            ->header('Content-Type', 'text/plain')
            ->header('Content-Disposition', 'attachment; filename="robots.txt"');
    }

    /**
     * Generate and download sitemap.xml.
     */
    public function sitemap(): Response
    {
        $urls = $this->seoService->getSitemapUrls();

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>{$url['url']}</loc>\n";
            $xml .= "    <lastmod>{$url['lastmod']}</lastmod>\n";
            $xml .= "    <changefreq>{$url['changefreq']}</changefreq>\n";
            $xml .= "    <priority>{$url['priority']}</priority>\n";
            $xml .= "  </url>\n";
        }

        $xml .= '</urlset>';

        return response($xml)
            ->header('Content-Type', 'application/xml')
            ->header('Content-Disposition', 'attachment; filename="sitemap.xml"');
    }

    /**
     * Test SEO configuration.
     */
    public function test(): View
    {
        $testResults = [
            'meta_tags' => $this->seoService->generateMetaTags(),
            'open_graph' => $this->seoService->generateOpenGraphTags(),
            'twitter_card' => $this->seoService->generateTwitterCardTags(),
            'schema_markup' => $this->seoService->generateSchemaMarkup(),
            'analytics_codes' => $this->seoService->generateAnalyticsCodes(),
            'verification_tags' => $this->seoService->generateVerificationTags(),
        ];

        return view('admin.seo.test', compact('testResults'));
    }

    /**
     * Reset SEO settings to defaults.
     */
    public function reset(): RedirectResponse
    {
        // Delete all current settings
        SeoSetting::truncate();
        
        // Seed defaults
        SeoSetting::seedDefaults();
        
        // Clear cache
        SeoSetting::clearCache();

        return redirect()->route('admin.seo.index')
            ->with('success', 'SEO settings have been reset to defaults!');
    }

    /**
     * Export SEO settings.
     */
    public function export(): Response
    {
        $settings = SeoSetting::all()->toArray();

        $json = json_encode($settings, JSON_PRETTY_PRINT);

        return response($json)
            ->header('Content-Type', 'application/json')
            ->header('Content-Disposition', 'attachment; filename="seo-settings-export.json"');
    }

    /**
     * Import SEO settings.
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'import_file' => 'required|file|mimes:json|max:2048',
        ]);

        try {
            $content = file_get_contents($request->file('import_file')->getRealPath());
            $settings = json_decode($content, true);

            if (!is_array($settings)) {
                throw new \Exception('Invalid JSON format');
            }

            foreach ($settings as $setting) {
                SeoSetting::updateOrCreate(
                    ['key' => $setting['key']],
                    [
                        'value' => $setting['value'],
                        'type' => $setting['type'],
                        'category' => $setting['category'],
                        'description' => $setting['description'],
                        'is_active' => $setting['is_active'],
                    ]
                );
            }

            // Clear cache
            SeoSetting::clearCache();

            return redirect()->route('admin.seo.index')
                ->with('success', 'SEO settings imported successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to import settings: ' . $e->getMessage());
        }
    }

    /**
     * Get SEO audit data.
     */
    public function audit(): View
    {
        $auditResults = [
            'meta_tags_complete' => $this->checkMetaTagsComplete(),
            'social_media_setup' => $this->checkSocialMediaSetup(),
            'analytics_configured' => $this->checkAnalyticsConfigured(),
            'verification_setup' => $this->checkVerificationSetup(),
            'technical_seo' => $this->checkTechnicalSeo(),
        ];

        $overallScore = $this->calculateSeoScore($auditResults);

        return view('admin.seo.audit', compact('auditResults', 'overallScore'));
    }

    /**
     * Check if meta tags are complete.
     */
    private function checkMetaTagsComplete(): array
    {
        $required = ['site_title', 'site_description', 'site_keywords'];
        $complete = 0;

        foreach ($required as $key) {
            if (SeoSetting::get($key)) {
                $complete++;
            }
        }

        return [
            'score' => ($complete / count($required)) * 100,
            'total' => count($required),
            'complete' => $complete,
            'missing' => array_diff($required, array_filter($required, fn($key) => SeoSetting::get($key))),
        ];
    }

    /**
     * Check social media setup.
     */
    private function checkSocialMediaSetup(): array
    {
        $required = ['og_site_name', 'og_image', 'twitter_site'];
        $complete = 0;

        foreach ($required as $key) {
            if (SeoSetting::get($key)) {
                $complete++;
            }
        }

        return [
            'score' => ($complete / count($required)) * 100,
            'total' => count($required),
            'complete' => $complete,
        ];
    }

    /**
     * Check analytics configuration.
     */
    private function checkAnalyticsConfigured(): array
    {
        $analytics = ['google_analytics_id', 'google_tag_manager_id'];
        $configured = 0;

        foreach ($analytics as $key) {
            if (SeoSetting::get($key)) {
                $configured++;
            }
        }

        return [
            'score' => $configured > 0 ? 100 : 0,
            'configured' => $configured,
            'total' => count($analytics),
        ];
    }

    /**
     * Check verification setup.
     */
    private function checkVerificationSetup(): array
    {
        $verifications = ['google_site_verification', 'bing_site_verification'];
        $verified = 0;

        foreach ($verifications as $key) {
            if (SeoSetting::get($key)) {
                $verified++;
            }
        }

        return [
            'score' => ($verified / count($verifications)) * 100,
            'verified' => $verified,
            'total' => count($verifications),
        ];
    }

    /**
     * Check technical SEO.
     */
    private function checkTechnicalSeo(): array
    {
        $checks = [
            'sitemap_enabled' => SeoSetting::get('enable_sitemap', false),
            'robots_enabled' => SeoSetting::get('enable_robots_txt', false),
            'schema_enabled' => SeoSetting::get('enable_schema_markup', false),
        ];

        $passed = array_sum($checks);

        return [
            'score' => ($passed / count($checks)) * 100,
            'passed' => $passed,
            'total' => count($checks),
            'checks' => $checks,
        ];
    }

    /**
     * Calculate overall SEO score.
     */
    private function calculateSeoScore(array $auditResults): int
    {
        $totalScore = 0;
        $categories = count($auditResults);

        foreach ($auditResults as $result) {
            $totalScore += $result['score'];
        }

        return round($totalScore / $categories);
    }
}
