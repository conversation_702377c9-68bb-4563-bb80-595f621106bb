@extends('layouts.dashboard-template')

@section('page_title', 'Edit User - ' . $user->name . ' - Minilink.at Admin')
@section('page_heading')
    <i class="fas fa-user-edit mr-3 text-blue-600"></i>Edit User: {{ $user->name }}
@endsection
@section('page_description', 'Update user information and settings')

@section('page_actions')
    <a href="{{ route('admin.users.show', $user) }}" class="btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i>Back to User
    </a>
    <a href="{{ route('admin.users.index') }}" class="btn-outline">
        <i class="fas fa-users mr-2"></i>All Users
    </a>
@endsection

@section('main_content')
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">User Information</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.users.update', $user) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-1 text-blue-500"></i>Full Name *
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               class="input-field @error('name') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               value="{{ old('name', $user->name) }}"
                               placeholder="Enter full name"
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-1 text-green-500"></i>Email Address *
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="input-field @error('email') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               value="{{ old('email', $user->email) }}"
                               placeholder="Enter email address"
                               required>
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Password Section -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Password (Optional)</h4>
                    <p class="text-sm text-gray-600 mb-4">Leave blank to keep current password</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-lock mr-1 text-purple-500"></i>New Password
                            </label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   class="input-field @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                   placeholder="Enter new password">
                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Minimum 8 characters with letters and numbers
                            </p>
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-lock mr-1 text-purple-500"></i>Confirm New Password
                            </label>
                            <input type="password" 
                                   id="password_confirmation" 
                                   name="password_confirmation" 
                                   class="input-field"
                                   placeholder="Confirm new password">
                        </div>
                    </div>
                </div>

                <!-- User Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">User Settings</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Role -->
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user-tag mr-1 text-orange-500"></i>User Role
                            </label>
                            <select id="role" 
                                    name="role" 
                                    class="input-field @error('role') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                    {{ $user->id === auth()->id() ? 'disabled' : '' }}>
                                <option value="user" {{ old('role', $user->role) === 'user' ? 'selected' : '' }}>
                                    👤 User
                                </option>
                                <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>
                                    👑 Admin
                                </option>
                            </select>
                            @if($user->id === auth()->id())
                                <input type="hidden" name="role" value="{{ $user->role }}">
                                <p class="mt-1 text-sm text-yellow-600">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    You cannot change your own role
                                </p>
                            @else
                                @error('role')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">
                                    Admin users have full system access
                                </p>
                            @endif
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="is_active" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-toggle-on mr-1 text-red-500"></i>Account Status
                            </label>
                            <select id="is_active" 
                                    name="is_active" 
                                    class="input-field @error('is_active') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                    {{ $user->id === auth()->id() ? 'disabled' : '' }}>
                                <option value="1" {{ old('is_active', $user->is_active) ? 'selected' : '' }}>
                                    ✅ Active
                                </option>
                                <option value="0" {{ !old('is_active', $user->is_active) ? 'selected' : '' }}>
                                    ❌ Inactive
                                </option>
                            </select>
                            @if($user->id === auth()->id())
                                <input type="hidden" name="is_active" value="1">
                                <p class="mt-1 text-sm text-yellow-600">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    You cannot deactivate your own account
                                </p>
                            @else
                                @error('is_active')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Email Verification -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Email Verification</h4>
                    
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="email_verified" 
                               name="email_verified" 
                               class="form-checkbox h-4 w-4 text-blue-600"
                               value="1"
                               {{ old('email_verified', $user->email_verified_at ? true : false) ? 'checked' : '' }}>
                        <label for="email_verified" class="ml-2 text-sm text-gray-700">
                            <i class="fas fa-shield-check mr-1 text-green-500"></i>
                            Email is verified
                        </label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        Current status: 
                        @if($user->email_verified_at)
                            <span class="text-green-600 font-medium">Verified on {{ $user->email_verified_at->format('M d, Y') }}</span>
                        @else
                            <span class="text-yellow-600 font-medium">Not verified</span>
                        @endif
                    </p>
                </div>

                <!-- Subscription Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Subscription Settings</h4>
                    
                    <div>
                        <label for="subscription_type" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-crown mr-1 text-yellow-500"></i>Subscription Type
                        </label>
                        <select id="subscription_type" 
                                name="subscription_type" 
                                class="input-field @error('subscription_type') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                            <option value="free" {{ old('subscription_type', $user->subscription_type ?? 'free') === 'free' ? 'selected' : '' }}>
                                🆓 Free Plan
                            </option>
                            <option value="pro" {{ old('subscription_type', $user->subscription_type ?? '') === 'pro' ? 'selected' : '' }}>
                                ⭐ Pro Plan
                            </option>
                            <option value="enterprise" {{ old('subscription_type', $user->subscription_type ?? '') === 'enterprise' ? 'selected' : '' }}>
                                🏢 Enterprise Plan
                            </option>
                        </select>
                        @error('subscription_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            Determines the user's feature access and limits
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.users.show', $user) }}" class="btn-secondary">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="card mt-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chart-bar mr-2 text-blue-500"></i>User Statistics
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ $user->urls()->count() }}</div>
                    <div class="text-sm text-blue-600">Total URLs</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ $user->urls()->sum('click_count') }}</div>
                    <div class="text-sm text-green-600">Total Clicks</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">{{ $user->urls()->where('is_active', true)->count() }}</div>
                    <div class="text-sm text-purple-600">Active URLs</div>
                </div>
                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">{{ $user->created_at->diffInDays(now()) }}</div>
                    <div class="text-sm text-orange-600">Days Active</div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Password confirmation validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('password_confirmation').value;
    
    if (password && password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match!');
        return false;
    }
});

// Role change warning
document.getElementById('role').addEventListener('change', function() {
    if (this.value === 'admin') {
        if (!confirm('Are you sure you want to give this user admin privileges? This will grant them full system access.')) {
            this.value = 'user';
        }
    }
});

// Status change warning
document.getElementById('is_active').addEventListener('change', function() {
    if (this.value === '0') {
        if (!confirm('Are you sure you want to deactivate this user? They will not be able to log in.')) {
            this.value = '1';
        }
    }
});
</script>
@endpush
