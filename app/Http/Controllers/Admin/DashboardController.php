<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Url;
use App\Models\Analytics;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display the admin dashboard.
     */
    public function index(): View
    {
        // Get system statistics
        $stats = $this->getSystemStats();

        // Get recent activity
        $recentActivity = $this->getRecentActivity();

        // Get system health metrics
        $systemHealth = $this->getSystemHealth();

        // Get growth metrics
        $growthMetrics = $this->getGrowthMetrics();

        // Get recent users and URLs for modern dashboard
        $recentUsers = User::latest()->take(7)->get();
        $recentUrls = Url::with('user')->latest()->take(7)->get();

        // Get subscription statistics
        $subscriptionStats = Subscription::withCount('users')->get()->map(function ($subscription) {
            return [
                'name' => $subscription->name,
                'count' => $subscription->users_count
            ];
        });

        // Get chart data
        $chartData = $this->getChartData();

        return view('admin.dashboard.modern-index', compact(
            'stats',
            'recentActivity',
            'systemHealth',
            'growthMetrics',
            'recentUsers',
            'recentUrls',
            'subscriptionStats',
            'chartData'
        ));
    }

    /**
     * Get system statistics.
     */
    private function getSystemStats(): array
    {
        // Calculate revenue from paid subscriptions
        $paidUsers = User::whereHas('subscription', function($query) {
            $query->where('price', '>', 0);
        })->count();

        $monthlyRevenue = User::whereHas('subscription', function($query) {
            $query->where('price', '>', 0);
        })->with('subscription')->get()->sum(function($user) {
            return $user->subscription->price ?? 0;
        });

        return [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'new_users_week' => User::where('created_at', '>=', now()->subWeek())->count(),
            'new_users_month' => User::where('created_at', '>=', now()->subMonth())->count(),
            'new_users_this_month' => User::where('created_at', '>=', now()->subMonth())->count(),

            'total_urls' => Url::count(),
            'active_urls' => Url::where('is_active', true)->count(),
            'urls_today' => Url::whereDate('created_at', today())->count(),
            'urls_week' => Url::where('created_at', '>=', now()->subWeek())->count(),
            'urls_month' => Url::where('created_at', '>=', now()->subMonth())->count(),
            'new_urls_this_month' => Url::where('created_at', '>=', now()->subMonth())->count(),

            'total_clicks' => Analytics::count(),
            'clicks_today' => Analytics::whereDate('clicked_at', today())->count(),
            'clicks_week' => Analytics::where('clicked_at', '>=', now()->subWeek())->count(),
            'clicks_month' => Analytics::where('clicked_at', '>=', now()->subMonth())->count(),

            'admin_users' => User::where('role', 'admin')->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'users_with_api_keys' => User::whereNotNull('api_key')->count(),

            // Additional stats for modern dashboard
            'monthly_revenue' => $monthlyRevenue,
            'paid_users' => $paidUsers,
        ];
    }

    /**
     * Get recent activity.
     */
    private function getRecentActivity(): array
    {
        return [
            'recent_users' => User::with('subscription')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
            
            'recent_urls' => Url::with(['user', 'analytics'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
            
            'recent_clicks' => Analytics::with(['url.user'])
                ->orderBy('clicked_at', 'desc')
                ->limit(15)
                ->get(),
                
            'top_urls_today' => Url::withCount(['analytics' => function($query) {
                    $query->whereDate('clicked_at', today());
                }])
                ->having('analytics_count', '>', 0)
                ->orderBy('analytics_count', 'desc')
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Get system health metrics.
     */
    private function getSystemHealth(): array
    {
        $dbConnection = true;
        $dbResponseTime = 0;
        
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $dbResponseTime = round((microtime(true) - $start) * 1000, 2);
        } catch (\Exception $e) {
            $dbConnection = false;
        }

        // Check for expired URLs
        $expiredUrls = Url::where('expires_at', '<', now())->count();
        
        // Check for inactive users
        $inactiveUsers = User::where('is_active', false)->count();
        
        // Check storage usage (simplified)
        $storageUsage = $this->getStorageUsage();

        return [
            'database_connection' => $dbConnection,
            'database_response_time' => $dbResponseTime,
            'expired_urls' => $expiredUrls,
            'inactive_users' => $inactiveUsers,
            'storage_usage' => $storageUsage,
            'system_status' => $this->calculateSystemStatus($dbConnection, $dbResponseTime, $expiredUrls),
        ];
    }

    /**
     * Get growth metrics for charts.
     */
    private function getGrowthMetrics(): array
    {
        $days = 30;
        $dates = collect();
        $userGrowth = collect();
        $urlGrowth = collect();
        $clickGrowth = collect();

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dates->push($date->format('M j'));
            
            $userGrowth->push(User::whereDate('created_at', $date)->count());
            $urlGrowth->push(Url::whereDate('created_at', $date)->count());
            $clickGrowth->push(Analytics::whereDate('clicked_at', $date)->count());
        }

        return [
            'dates' => $dates->toArray(),
            'user_growth' => $userGrowth->toArray(),
            'url_growth' => $urlGrowth->toArray(),
            'click_growth' => $clickGrowth->toArray(),
        ];
    }

    /**
     * Get storage usage (simplified implementation).
     */
    private function getStorageUsage(): array
    {
        $qrCodePath = storage_path('app/public/qr-codes');
        $exportPath = storage_path('app/exports');
        
        $qrCodeSize = 0;
        $exportSize = 0;
        
        if (is_dir($qrCodePath)) {
            $qrCodeSize = $this->getDirSize($qrCodePath);
        }
        
        if (is_dir($exportPath)) {
            $exportSize = $this->getDirSize($exportPath);
        }

        $totalSize = $qrCodeSize + $exportSize;
        $maxSize = 1024 * 1024 * 1024; // 1GB limit for example

        return [
            'qr_codes' => $this->formatBytes($qrCodeSize),
            'exports' => $this->formatBytes($exportSize),
            'total' => $this->formatBytes($totalSize),
            'percentage' => min(100, ($totalSize / $maxSize) * 100),
        ];
    }

    /**
     * Calculate directory size.
     */
    private function getDirSize(string $directory): int
    {
        $size = 0;
        if (is_dir($directory)) {
            foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory)) as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        return $size;
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Calculate overall system status.
     */
    private function calculateSystemStatus(bool $dbConnection, float $dbResponseTime, int $expiredUrls): string
    {
        if (!$dbConnection) {
            return 'critical';
        }
        
        if ($dbResponseTime > 1000 || $expiredUrls > 100) {
            return 'warning';
        }
        
        return 'healthy';
    }

    /**
     * Get system information for admin.
     */
    public function systemInfo(): View
    {
        $systemInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => DB::select('SELECT VERSION() as version')[0]->version ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'debug_mode' => config('app.debug'),
        ];

        return view('admin.dashboard.system-info', compact('systemInfo'));
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData(): array
    {
        $days = 30;
        $startDate = Carbon::now()->subDays($days);

        // User growth data
        $userGrowthData = [];
        $userGrowthLabels = [];

        for ($i = $days; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $userGrowthLabels[] = $date->format('M j');
            $userGrowthData[] = User::whereDate('created_at', $date)->count();
        }

        // URL creation data
        $urlCreationData = [];
        $urlCreationLabels = [];

        for ($i = $days; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $urlCreationLabels[] = $date->format('M j');
            $urlCreationData[] = Url::whereDate('created_at', $date)->count();
        }

        return [
            'userGrowth' => [
                'labels' => $userGrowthLabels,
                'data' => $userGrowthData
            ],
            'urlCreation' => [
                'labels' => $urlCreationLabels,
                'data' => $urlCreationData
            ]
        ];
    }
}
