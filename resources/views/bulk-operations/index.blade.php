@extends('layouts.modern')

@section('title', 'Bulk Operations - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Bulk Operations</h1>
                <p class="text-gray-600 mt-1">Manage multiple URLs efficiently with bulk operations</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <a href="{{ route('bulk-operations.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>New Bulk Operation
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Bulk Create -->
        <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location='{{ route('bulk-operations.create') }}'">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-plus-circle text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Bulk Create URLs</h3>
                <p class="text-gray-600 text-sm">Create multiple short URLs from a list of long URLs</p>
            </div>
        </div>

        <!-- Bulk Edit -->
        <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location='{{ route('bulk-operations.edit') }}'">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-edit text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Bulk Edit URLs</h3>
                <p class="text-gray-600 text-sm">Update multiple URLs at once with batch editing</p>
            </div>
        </div>

        <!-- Bulk Export -->
        <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="showExportModal()">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-download text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Export URLs</h3>
                <p class="text-gray-600 text-sm">Export your URLs and analytics data to CSV</p>
            </div>
        </div>

        <!-- Bulk Analytics -->
        <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location='{{ route('analytics.advanced.index') }}'">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-bar text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Bulk Analytics</h3>
                <p class="text-gray-600 text-sm">Analyze performance across multiple URLs</p>
            </div>
        </div>
    </div>

    <!-- Recent Operations -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Bulk Operations -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Operations</h3>
                    <a href="{{ route('bulk-operations.results') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if(count($recentBulkOperations) > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($recentBulkOperations as $operation)
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 rounded-lg flex items-center justify-center
                                            @if($operation['type'] === 'create') bg-blue-100
                                            @elseif($operation['type'] === 'edit') bg-green-100
                                            @elseif($operation['type'] === 'export') bg-purple-100
                                            @else bg-gray-100 @endif">
                                            @if($operation['type'] === 'create')
                                                <i class="fas fa-plus text-blue-600"></i>
                                            @elseif($operation['type'] === 'edit')
                                                <i class="fas fa-edit text-green-600"></i>
                                            @elseif($operation['type'] === 'export')
                                                <i class="fas fa-download text-purple-600"></i>
                                            @else
                                                <i class="fas fa-cog text-gray-600"></i>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ ucfirst($operation['type']) }} Operation
                                        </div>
                                        <div class="text-sm text-gray-600">
                                            {{ $operation['description'] }}
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ $operation['created_at'] }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-shrink-0 text-right">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $operation['items_count'] }} items
                                    </div>
                                    <div class="text-xs">
                                        @if($operation['status'] === 'completed')
                                            <span class="badge badge-success">Completed</span>
                                        @elseif($operation['status'] === 'processing')
                                            <span class="badge badge-warning">Processing</span>
                                        @elseif($operation['status'] === 'failed')
                                            <span class="badge badge-danger">Failed</span>
                                        @else
                                            <span class="badge badge-secondary">{{ ucfirst($operation['status']) }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-history text-gray-400 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Operations Yet</h3>
                        <p class="text-gray-600 mb-4">You haven't performed any bulk operations yet.</p>
                        <a href="{{ route('bulk-operations.create') }}" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Start Your First Operation
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Tips & Guidelines -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Tips & Guidelines</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-lightbulb text-yellow-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Bulk Create</h4>
                            <p class="text-sm text-gray-600">Upload a CSV file or paste URLs line by line. Maximum 1000 URLs per operation.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-shield-alt text-green-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Data Safety</h4>
                            <p class="text-sm text-gray-600">All operations are reversible. We keep backups of your data for 30 days.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-blue-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Processing Time</h4>
                            <p class="text-sm text-gray-600">Large operations are processed in the background. You'll receive an email when complete.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-csv text-purple-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Export Formats</h4>
                            <p class="text-sm text-gray-600">Export data in CSV, Excel, or JSON formats with customizable fields.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export URLs</h3>
                <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form action="{{ route('bulk-operations.export') }}" method="POST">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                        <select name="format" class="input-field">
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Include Fields</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="fields[]" value="title" checked class="rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Title</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="fields[]" value="original_url" checked class="rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Original URL</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="fields[]" value="short_url" checked class="rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Short URL</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="fields[]" value="click_count" checked class="rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Click Count</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="fields[]" value="created_at" class="rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Created Date</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideExportModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('exportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});
</script>
@endpush
