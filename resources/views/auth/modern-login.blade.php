@extends('layouts.modern')

@section('title', 'Sign In - Minilink.at')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="flex justify-center">
                <div class="w-16 h-16 gradient-bg rounded-xl flex items-center justify-center">
                    <i class="fas fa-link text-white text-2xl"></i>
                </div>
            </div>
            <h2 class="mt-6 text-3xl font-bold text-gray-900">
                Welcome back
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Sign in to your Minilink.at account
            </p>
        </div>

        <!-- Form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="{{ route('login') }}" class="space-y-6">
                    @csrf

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email address
                        </label>
                        <input id="email" 
                               name="email" 
                               type="email" 
                               autocomplete="email" 
                               required 
                               class="input-field @error('email') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               placeholder="Enter your email"
                               value="{{ old('email') }}">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password
                        </label>
                        <input id="password" 
                               name="password" 
                               type="password" 
                               autocomplete="current-password" 
                               required 
                               class="input-field @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                               placeholder="Enter your password">
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember" 
                                   name="remember" 
                                   type="checkbox" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                   {{ old('remember') ? 'checked' : '' }}>
                            <label for="remember" class="ml-2 block text-sm text-gray-700">
                                Remember me
                            </label>
                        </div>

                        @if (Route::has('password.request'))
                            <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-700">
                                Forgot password?
                            </a>
                        @endif
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" class="btn-primary w-full">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign in
                        </button>
                    </div>

                    <!-- Divider -->
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or</span>
                        </div>
                    </div>

                    <!-- Social Login (Optional) -->
                    <div class="grid grid-cols-2 gap-3">
                        <button type="button" class="btn-secondary">
                            <i class="fab fa-google mr-2"></i>
                            Google
                        </button>
                        <button type="button" class="btn-secondary">
                            <i class="fab fa-github mr-2"></i>
                            GitHub
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Don't have an account?
                <a href="{{ route('register') }}" class="font-medium text-blue-600 hover:text-blue-700">
                    Sign up for free
                </a>
            </p>
        </div>

        <!-- Demo Accounts -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Demo Accounts</h3>
                <div class="space-y-2 text-xs">
                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span class="font-medium">Admin:</span>
                        <span class="font-mono"><EMAIL> / admin123</span>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span class="font-medium">User:</span>
                        <span class="font-mono"><EMAIL> / password</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-fill demo credentials
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for demo accounts
    const demoButtons = document.querySelectorAll('.demo-account');
    demoButtons.forEach(button => {
        button.addEventListener('click', function() {
            const email = this.dataset.email;
            const password = this.dataset.password;
            
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        });
    });
});
</script>
@endpush
