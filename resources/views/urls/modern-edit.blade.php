@extends('layouts.modern')

@section('title', 'Edit Link - Minilink.at')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Link</h1>
                <p class="text-gray-600 mt-1">{{ $url->title ?: 'Untitled Link' }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('urls.show', $url) }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Analytics
                </a>
                <a href="{{ route('urls.manage') }}" class="btn-outline">
                    <i class="fas fa-link mr-2"></i>My Links
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Edit Form -->
        <div class="lg:col-span-2 space-y-6">
            <!-- URL Settings -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-cog mr-2"></i>URL Settings
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('urls.update', $url) }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Original URL -->
                        <div>
                            <label for="original_url" class="block text-sm font-medium text-gray-700 mb-2">
                                Original URL <span class="text-red-500">*</span>
                            </label>
                            <input type="url" 
                                   id="original_url" 
                                   name="original_url" 
                                   class="input-field @error('original_url') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                   value="{{ old('original_url', $url->original_url) }}"
                                   required>
                            @error('original_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Title -->
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Title (Optional)
                            </label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="input-field @error('title') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                   value="{{ old('title', $url->title) }}"
                                   placeholder="My Awesome Link">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description (Optional)
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      class="input-field @error('description') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                      placeholder="Brief description of your link">{{ old('description', $url->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Custom Alias -->
                        <div>
                            <label for="short_code" class="block text-sm font-medium text-gray-700 mb-2">
                                Custom Alias
                            </label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                    {{ $url->customDomain ? $url->customDomain->domain : config('app.url') }}/
                                </span>
                                <input type="text" 
                                       id="short_code" 
                                       name="short_code" 
                                       class="input-field rounded-l-none @error('short_code') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                       value="{{ old('short_code', $url->short_code) }}"
                                       required>
                            </div>
                            @error('short_code')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Only letters, numbers, and hyphens allowed.
                            </p>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       {{ old('is_active', $url->is_active) ? 'checked' : '' }}>
                                <label for="is_active" class="ml-2 block text-sm text-gray-700">
                                    Link is active and accessible
                                </label>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Advanced Options</h4>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Expiration Date -->
                                <div>
                                    <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">
                                        Expiration Date (Optional)
                                    </label>
                                    <input type="datetime-local" 
                                           id="expires_at" 
                                           name="expires_at" 
                                           class="input-field @error('expires_at') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           value="{{ old('expires_at', $url->expires_at ? $url->expires_at->format('Y-m-d\TH:i') : '') }}">
                                    @error('expires_at')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Password Protection -->
                                <div>
                                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                        Password Protection (Optional)
                                    </label>
                                    <input type="password" 
                                           id="password" 
                                           name="password" 
                                           class="input-field @error('password') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           placeholder="Leave empty to keep current password">
                                    @error('password')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    @if($url->password)
                                        <p class="mt-1 text-sm text-green-600">
                                            <i class="fas fa-lock mr-1"></i>Password protection is currently enabled
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Custom Domain (Premium Feature) -->
                        @if(Auth::user()->subscription?->custom_domains && Auth::user()->customDomains->count() > 0)
                        <div class="border-t border-gray-200 pt-6">
                            <label for="custom_domain_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Custom Domain (Optional)
                            </label>
                            <select id="custom_domain_id" 
                                    name="custom_domain_id" 
                                    class="input-field @error('custom_domain_id') border-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                                <option value="">Use default domain ({{ config('app.url') }})</option>
                                @foreach(Auth::user()->customDomains as $domain)
                                    <option value="{{ $domain->id }}" {{ old('custom_domain_id', $url->custom_domain_id) == $domain->id ? 'selected' : '' }}>
                                        {{ $domain->domain }}
                                    </option>
                                @endforeach
                            </select>
                            @error('custom_domain_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        @endif

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                            <a href="{{ route('urls.show', $url) }}" class="btn-secondary">
                                Cancel
                            </a>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save mr-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card border-red-200">
                <div class="card-header bg-red-50">
                    <h3 class="text-lg font-semibold text-red-900">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Danger Zone
                    </h3>
                </div>
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">Delete this link</h4>
                            <p class="text-sm text-gray-600">
                                Once you delete a link, there is no going back. Please be certain.
                            </p>
                        </div>
                        <form action="{{ route('urls.destroy', $url) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this link? This action cannot be undone.')">
                                <i class="fas fa-trash mr-2"></i>Delete Link
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Link Overview -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Link Overview</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-600">Short URL</label>
                            <div class="flex items-center mt-1">
                                <span class="text-sm font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded flex-1">
                                    {{ $url->short_url }}
                                </span>
                                <button onclick="copyToClipboard('{{ $url->short_url }}')" 
                                        class="ml-2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600">Total Clicks</span>
                                <span class="text-sm font-medium text-gray-900">{{ number_format($url->click_count) }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600">Created</span>
                                <span class="text-sm font-medium text-gray-900">{{ $url->created_at->format('M j, Y') }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Status</span>
                                @if($url->is_active)
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-danger">Inactive</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-3">
                        <a href="{{ $url->short_url }}" target="_blank" class="btn-outline w-full">
                            <i class="fas fa-external-link-alt mr-2"></i>Visit Link
                        </a>
                        <a href="{{ route('urls.show', $url) }}" class="btn-outline w-full">
                            <i class="fas fa-chart-bar mr-2"></i>View Analytics
                        </a>
                        @if($url->qr_code_path)
                            <a href="{{ route('qr.show', $url->short_code) }}" target="_blank" class="btn-outline w-full">
                                <i class="fas fa-qrcode mr-2"></i>View QR Code
                            </a>
                        @else
                            <form action="{{ route('qr.generate', $url) }}" method="POST">
                                @csrf
                                <button type="submit" class="btn-outline w-full">
                                    <i class="fas fa-qrcode mr-2"></i>Generate QR Code
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            @if($url->analytics->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-3">
                        @foreach($url->analytics->take(5) as $click)
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-mouse-pointer text-gray-600 text-xs"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-900">
                                    Click from {{ $click->country ?? 'Unknown' }}
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ $click->clicked_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-600');
        }, 2000);
    });
}

// Custom alias validation
document.getElementById('short_code').addEventListener('input', function() {
    let value = this.value;
    // Remove invalid characters
    value = value.replace(/[^a-zA-Z0-9-]/g, '');
    // Update the input
    this.value = value;
});
</script>
@endpush
