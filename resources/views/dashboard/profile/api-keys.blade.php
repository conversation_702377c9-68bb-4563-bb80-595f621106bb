@extends('layouts.modern')

@section('title', 'API Keys - Minilink.at')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-code mr-3 text-green-600"></i>
                    API Keys
                </h1>
                <p class="text-gray-600 mt-1">Manage your API access keys for programmatic access</p>
            </div>
            <div>
                <a href="{{ route('profile.show') }}" class="btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Profile
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-4xl mx-auto space-y-8">
        <!-- New API Key Alert -->
        @if(session('new_api_key'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 relative">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                </div>
                <div class="ml-4 flex-1">
                    <h3 class="text-lg font-medium text-green-800 mb-2">New API Key Generated!</h3>
                    <p class="text-green-700 mb-4">Your new API key has been generated. Please copy it now as it won't be shown again.</p>

                    <div class="flex items-center space-x-2 mb-4">
                        <input type="text"
                               class="form-input font-mono text-sm flex-1"
                               value="{{ session('new_api_key') }}"
                               readonly
                               id="newApiKey">
                        <button class="btn-primary" type="button" onclick="copyApiKey('newApiKey')">
                            <i class="fas fa-copy mr-2"></i>Copy
                        </button>
                    </div>

                    <p class="text-sm text-green-700">
                        <strong>Important:</strong> Store this key securely. You won't be able to see it again.
                    </p>
                </div>
                <button type="button"
                        class="absolute top-4 right-4 text-green-400 hover:text-green-600"
                        onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        @endif

        <!-- Current API Key -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-key mr-2 text-green-600"></i>
                    Current API Key
                </h3>
            </div>
            <div class="card-body">
                @if($user->api_key)
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-blue-800">API Key Active</h4>
                                <p class="text-blue-700 text-sm">You have an active API key for programmatic access.</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Active
                            </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                            <div class="flex items-center space-x-2">
                                <input type="password"
                                       class="form-input font-mono text-sm flex-1"
                                       value="{{ $user->api_key }}"
                                       readonly
                                       id="currentApiKey">
                                <button class="inline-flex items-center justify-center w-10 h-10 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200"
                                        type="button"
                                        onclick="toggleApiKeyVisibility('currentApiKey')"
                                        title="Toggle visibility">
                                    <i class="fas fa-eye" id="currentApiKeyIcon"></i>
                                </button>
                                <button class="inline-flex items-center justify-center w-10 h-10 rounded-md bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200"
                                        type="button"
                                        onclick="copyApiKey('currentApiKey')"
                                        title="Copy to clipboard">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">Click the eye icon to reveal your API key.</p>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
                            <form action="{{ route('profile.generate-api-key') }}" method="POST" class="inline">
                                @csrf
                                <button type="submit"
                                        class="btn-outline text-yellow-600 border-yellow-300 hover:bg-yellow-50"
                                        onclick="return confirm('This will replace your current API key. Any applications using the old key will stop working. Continue?')">
                                    <i class="fas fa-sync mr-2"></i>Regenerate Key
                                </button>
                            </form>

                            <form action="{{ route('profile.revoke-api-key') }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="btn-outline text-red-600 border-red-300 hover:bg-red-50"
                                        onclick="return confirm('This will permanently revoke your API key. Any applications using this key will stop working. Continue?')">
                                    <i class="fas fa-trash mr-2"></i>Revoke Key
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-key text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No API Key</h3>
                        <p class="text-gray-500 mb-6">You don't have an API key yet. Generate one to access the API programmatically.</p>

                        <form action="{{ route('profile.generate-api-key') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i>Generate API Key
                            </button>
                        </form>
                    </div>
                @endif
                </div>
            </div>

        <!-- API Documentation -->
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-book mr-2 text-blue-600"></i>
                    API Usage
                </h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600 mb-6">Use your API key to access the Minilink.at API programmatically. Include it in the Authorization header of your requests.</p>

                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Authentication</h4>
                        <div class="bg-gray-50 rounded-lg p-4 border">
                            <code class="text-sm text-gray-800">Authorization: Bearer YOUR_API_KEY</code>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Example Request</h4>
                        <div class="bg-gray-50 rounded-lg p-4 border overflow-x-auto">
                            <pre class="text-sm text-gray-800"><code>curl -X POST {{ config('app.url') }}/api/urls \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"original_url": "https://example.com"}'</code></pre>
                        </div>
                    </div>

                    <div class="pt-4 border-t border-gray-200">
                        <a href="#" class="btn-outline">
                            <i class="fas fa-external-link-alt mr-2"></i>View Full API Documentation
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Best Practices -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-shield-alt mr-2 text-red-600"></i>
                    Security Best Practices
                </h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Keep your API key secret</h4>
                            <p class="text-sm text-gray-600">Never share it publicly or commit it to version control.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Use environment variables</h4>
                            <p class="text-sm text-gray-600">Store your API key in environment variables, not in your code.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Regenerate regularly</h4>
                            <p class="text-sm text-gray-600">Consider regenerating your API key periodically for better security.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Monitor usage</h4>
                            <p class="text-sm text-gray-600">Keep track of your API usage and watch for any suspicious activity.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Revoke if compromised</h4>
                            <p class="text-sm text-gray-600">If you suspect your key is compromised, revoke it immediately.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleApiKeyVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + 'Icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function copyApiKey(inputId) {
    const input = document.getElementById(inputId);
    const originalType = input.type;

    // Temporarily show the value to copy it
    input.type = 'text';
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(input.value).then(function() {
        // Show success feedback
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';

        // Update button styling for Tailwind
        btn.classList.remove('bg-blue-100', 'text-blue-600', 'hover:bg-blue-200');
        btn.classList.add('bg-green-100', 'text-green-600');

        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('bg-green-100', 'text-green-600');
            btn.classList.add('bg-blue-100', 'text-blue-600', 'hover:bg-blue-200');
        }, 2000);

        // Restore original input type
        input.type = originalType;
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        alert('Failed to copy API key. Please copy it manually.');
        input.type = originalType;
    });
}
</script>
@endpush
