<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Url;
use App\Models\Analytics;
use App\Models\Subscription;
use App\Models\CustomDomain;
use App\Models\Team;
use App\Models\TeamMember;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users
        $usersData = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'subscription_id' => 2, // Pro plan
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'subscription_id' => 3, // Premium plan
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'subscription_id' => 1, // Free plan
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'subscription_id' => 2, // Pro plan
            ],
        ];

        $users = collect();
        foreach ($usersData as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
            $users->push($user);
        }

        // Create sample custom domains
        $customDomains = [
            [
                'user_id' => $users[1]->id, // Sarah (Premium user)
                'domain' => 'short.company.com',
                'is_verified' => true,
                'is_active' => true,
                'verification_token' => Str::random(32),
            ],
            [
                'user_id' => $users[3]->id, // Emily (Pro user)
                'domain' => 'links.startup.io',
                'is_verified' => true,
                'is_active' => true,
                'verification_token' => Str::random(32),
            ],
        ];

        foreach ($customDomains as $domainData) {
            CustomDomain::firstOrCreate(
                ['domain' => $domainData['domain']],
                $domainData
            );
        }

        // Create sample teams
        $team1 = Team::firstOrCreate(
            ['name' => 'Marketing Team', 'owner_id' => $users[1]->id],
            [
                'description' => 'Company marketing and social media team',
                'settings' => json_encode(['auto_generate_qr' => true, 'default_expiry' => 30]),
            ]
        );

        $team2 = Team::firstOrCreate(
            ['name' => 'Development Team', 'owner_id' => $users[3]->id],
            [
                'description' => 'Software development and engineering team',
                'settings' => json_encode(['auto_generate_qr' => false, 'default_expiry' => null]),
            ]
        );

        // Add team members
        TeamMember::firstOrCreate([
            'team_id' => $team1->id,
            'user_id' => $users[1]->id, // Sarah (owner)
        ], [
            'role' => 'admin',
            'invited_by' => null,
            'invited_at' => now(),
        ]);

        TeamMember::firstOrCreate([
            'team_id' => $team1->id,
            'user_id' => $users[0]->id, // John
        ], [
            'role' => 'member',
            'invited_by' => $users[1]->id,
            'invited_at' => now(),
        ]);

        TeamMember::firstOrCreate([
            'team_id' => $team2->id,
            'user_id' => $users[3]->id, // Emily (owner)
        ], [
            'role' => 'admin',
            'invited_by' => null,
            'invited_at' => now(),
        ]);

        TeamMember::firstOrCreate([
            'team_id' => $team2->id,
            'user_id' => $users[2]->id, // Mike
        ], [
            'role' => 'member',
            'invited_by' => $users[3]->id,
            'invited_at' => now(),
        ]);

        // Sample URLs with realistic data
        $sampleUrls = [
            [
                'user_id' => $users[0]->id,
                'team_id' => $team1->id,
                'original_url' => 'https://www.google.com/search?q=laravel+tutorial',
                'title' => 'Laravel Tutorial Search',
                'description' => 'Google search for Laravel tutorials',
                'click_count' => rand(50, 500),
                'is_active' => true,
            ],
            [
                'user_id' => $users[1]->id,
                'custom_domain_id' => 1,
                'original_url' => 'https://github.com/laravel/laravel',
                'title' => 'Laravel GitHub Repository',
                'description' => 'Official Laravel framework repository',
                'click_count' => rand(100, 1000),
                'is_active' => true,
            ],
            [
                'user_id' => $users[1]->id,
                'team_id' => $team1->id,
                'original_url' => 'https://tailwindcss.com/docs',
                'title' => 'Tailwind CSS Documentation',
                'description' => 'Complete guide to Tailwind CSS framework',
                'click_count' => rand(200, 800),
                'is_active' => true,
            ],
            [
                'user_id' => $users[2]->id,
                'original_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'title' => 'Never Gonna Give You Up',
                'description' => 'Rick Astley classic music video',
                'click_count' => rand(1000, 5000),
                'is_active' => true,
            ],
            [
                'user_id' => $users[3]->id,
                'team_id' => $team2->id,
                'custom_domain_id' => 2,
                'original_url' => 'https://docs.docker.com/get-started/',
                'title' => 'Docker Getting Started',
                'description' => 'Docker containerization tutorial',
                'click_count' => rand(150, 600),
                'is_active' => true,
            ],
            [
                'user_id' => $users[0]->id,
                'original_url' => 'https://www.amazon.com/dp/B08N5WRWNW',
                'title' => 'Echo Dot (4th Gen)',
                'description' => 'Amazon Echo Dot smart speaker',
                'click_count' => rand(75, 300),
                'is_active' => true,
            ],
            [
                'user_id' => $users[1]->id,
                'original_url' => 'https://www.netflix.com/title/70136120',
                'title' => 'Breaking Bad on Netflix',
                'description' => 'Watch Breaking Bad TV series',
                'click_count' => rand(300, 1200),
                'is_active' => true,
            ],
            [
                'user_id' => $users[2]->id,
                'original_url' => 'https://stackoverflow.com/questions/tagged/php',
                'title' => 'PHP Questions on Stack Overflow',
                'description' => 'Browse PHP programming questions',
                'click_count' => rand(400, 900),
                'is_active' => true,
            ],
        ];

        $createdUrls = [];
        foreach ($sampleUrls as $urlData) {
            $url = Url::create(array_merge($urlData, [
                'short_code' => $this->generateUniqueShortCode(),
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(0, 5)),
            ]));
            $createdUrls[] = $url;
        }

        // Generate realistic analytics data
        $this->generateAnalyticsData($createdUrls);
    }

    private function generateUniqueShortCode(): string
    {
        do {
            $code = Str::random(6);
        } while (Url::where('short_code', $code)->exists());

        return $code;
    }

    private function generateAnalyticsData($urls): void
    {
        $countries = ['US', 'GB', 'CA', 'AU', 'DE', 'FR', 'JP', 'BR', 'IN', 'NL'];
        $cities = [
            'US' => ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
            'GB' => ['London', 'Manchester', 'Birmingham', 'Leeds', 'Glasgow'],
            'CA' => ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],
            'AU' => ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
            'DE' => ['Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt'],
        ];

        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'];
        $platforms = ['Windows', 'macOS', 'Linux', 'iOS', 'Android'];
        $devices = ['Desktop', 'Mobile', 'Tablet'];
        $referrers = [
            'https://google.com',
            'https://facebook.com',
            'https://twitter.com',
            'https://linkedin.com',
            'https://reddit.com',
            'direct',
            'https://github.com',
            'https://stackoverflow.com',
        ];

        foreach ($urls as $url) {
            $clickCount = $url->click_count;

            for ($i = 0; $i < $clickCount; $i++) {
                $country = $countries[array_rand($countries)];
                $city = $cities[$country] ?? ['Unknown City'];

                Analytics::create([
                    'url_id' => $url->id,
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateUserAgent(),
                    'referer' => $referrers[array_rand($referrers)],
                    'country' => $country,
                    'country_name' => $this->getCountryName($country),
                    'city' => $city[array_rand($city)],
                    'region' => $this->getRegionName($country),
                    'device_type' => strtolower($devices[array_rand($devices)]),
                    'browser' => $browsers[array_rand($browsers)],
                    'browser_version' => rand(80, 120) . '.0',
                    'platform' => $platforms[array_rand($platforms)],
                    'platform_version' => $this->getPlatformVersion($platforms[array_rand($platforms)]),
                    'is_bot' => rand(1, 100) <= 5, // 5% chance of being a bot
                    'clicked_at' => Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                ]);
            }
        }
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(0, 255) . '.' . rand(0, 255) . '.' . rand(1, 255);
    }

    private function generateUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        ];

        return $userAgents[array_rand($userAgents)];
    }

    private function getCountryName(string $code): string
    {
        $countries = [
            'US' => 'United States',
            'GB' => 'United Kingdom',
            'CA' => 'Canada',
            'AU' => 'Australia',
            'DE' => 'Germany',
            'FR' => 'France',
            'JP' => 'Japan',
            'BR' => 'Brazil',
            'IN' => 'India',
            'NL' => 'Netherlands',
        ];

        return $countries[$code] ?? 'Unknown';
    }

    private function getRegionName(string $countryCode): string
    {
        $regions = [
            'US' => ['California', 'New York', 'Texas', 'Florida', 'Illinois'],
            'GB' => ['England', 'Scotland', 'Wales', 'Northern Ireland'],
            'CA' => ['Ontario', 'Quebec', 'British Columbia', 'Alberta'],
            'AU' => ['New South Wales', 'Victoria', 'Queensland', 'Western Australia'],
            'DE' => ['Bavaria', 'North Rhine-Westphalia', 'Baden-Württemberg', 'Lower Saxony'],
        ];

        $countryRegions = $regions[$countryCode] ?? ['Unknown Region'];
        return $countryRegions[array_rand($countryRegions)];
    }

    private function getPlatformVersion(string $platform): string
    {
        $versions = [
            'Windows' => ['10.0', '11.0', '8.1'],
            'macOS' => ['12.0', '11.0', '10.15', '10.14'],
            'Linux' => ['Ubuntu 20.04', 'Ubuntu 22.04', 'CentOS 8'],
            'iOS' => ['15.0', '14.6', '14.0', '13.7'],
            'Android' => ['12', '11', '10', '9'],
        ];

        $platformVersions = $versions[$platform] ?? ['Unknown'];
        return $platformVersions[array_rand($platformVersions)];
    }
}
