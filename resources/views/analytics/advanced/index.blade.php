@extends('layouts.modern')

@section('title', 'Advanced Analytics - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Advanced Analytics</h1>
                <p class="text-gray-600 mt-1">Comprehensive insights into your link performance</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <a href="{{ route('dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <form action="{{ route('analytics.advanced.export') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-download mr-2"></i>Export Data
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Clicks -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-mouse-pointer text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Clicks</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($overview['total_clicks']) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $overview['clicks_change'] }}% vs last period
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unique Visitors -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Unique Visitors</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($overview['unique_visitors']) }}</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>{{ $overview['visitors_change'] }}% vs last period
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing URL -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-trophy text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Top URL Clicks</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($overview['top_url_clicks']) }}</p>
                        <p class="text-xs text-gray-500 truncate">{{ $overview['top_url_title'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversion Rate -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-percentage text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Click Rate</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($overview['click_rate'], 1) }}%</p>
                        <p class="text-xs text-gray-500">Clicks per URL</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Clicks Over Time -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Clicks Over Time</h3>
            </div>
            <div class="card-body">
                <canvas id="clicksChart" height="300"></canvas>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Geographic Distribution</h3>
                    <a href="{{ route('analytics.advanced.geographic') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View Details
                    </a>
                </div>
            </div>
            <div class="card-body">
                <canvas id="geoChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top URLs -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Top Performing URLs</h3>
            </div>
            <div class="card-body p-0">
                <div class="divide-y divide-gray-200">
                    @foreach($overview['top_urls'] as $url)
                    <div class="p-4 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">
                                    {{ $url['title'] ?: 'Untitled' }}
                                </div>
                                <div class="text-sm text-blue-600 font-mono truncate">{{ $url['short_url'] }}</div>
                                <div class="text-xs text-gray-500 truncate">{{ $url['original_url'] }}</div>
                            </div>
                            <div class="flex-shrink-0 text-right ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ number_format($url['clicks']) }}</div>
                                <div class="text-xs text-gray-500">clicks</div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Device Types -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Device Types</h3>
                    <a href="{{ route('analytics.advanced.devices') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View Details
                    </a>
                </div>
            </div>
            <div class="card-body">
                <canvas id="deviceChart" height="250"></canvas>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
            <div class="card-body p-0">
                <div class="divide-y divide-gray-200 max-h-80 overflow-y-auto">
                    @foreach($recentActivity as $activity)
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-mouse-pointer text-gray-600 text-xs"></i>
                                </div>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="text-sm text-gray-900">
                                    Click on <span class="font-medium">{{ $activity['url_title'] ?: 'Untitled' }}</span>
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ $activity['country'] ?? 'Unknown location' }} • 
                                    {{ $activity['device'] ?? 'Unknown device' }}
                                </div>
                                <div class="text-xs text-gray-400">{{ $activity['time_ago'] }}</div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">Advanced Analytics Tools</h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="{{ route('analytics.advanced.compare') }}" class="btn-outline">
                        <i class="fas fa-chart-line mr-2"></i>Compare URLs
                    </a>
                    <a href="{{ route('analytics.advanced.realtime') }}" class="btn-outline">
                        <i class="fas fa-broadcast-tower mr-2"></i>Real-time Data
                    </a>
                    <a href="{{ route('analytics.advanced.trends') }}" class="btn-outline">
                        <i class="fas fa-trending-up mr-2"></i>Trend Analysis
                    </a>
                    <a href="{{ route('analytics.advanced.sources') }}" class="btn-outline">
                        <i class="fas fa-external-link-alt mr-2"></i>Traffic Sources
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Clicks Over Time Chart
const clicksCtx = document.getElementById('clicksChart').getContext('2d');
const clicksChart = new Chart(clicksCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($overview['chart_labels']) !!},
        datasets: [{
            label: 'Clicks',
            data: {!! json_encode($overview['chart_data']) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Geographic Chart
const geoCtx = document.getElementById('geoChart').getContext('2d');
const geoChart = new Chart(geoCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($overview['geo_labels']) !!},
        datasets: [{
            data: {!! json_encode($overview['geo_data']) !!},
            backgroundColor: [
                'rgba(59, 130, 246, 0.8)',
                'rgba(34, 197, 94, 0.8)',
                'rgba(168, 85, 247, 0.8)',
                'rgba(251, 191, 36, 0.8)',
                'rgba(239, 68, 68, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Device Chart
const deviceCtx = document.getElementById('deviceChart').getContext('2d');
const deviceChart = new Chart(deviceCtx, {
    type: 'pie',
    data: {
        labels: {!! json_encode($overview['device_labels']) !!},
        datasets: [{
            data: {!! json_encode($overview['device_data']) !!},
            backgroundColor: [
                'rgba(59, 130, 246, 0.8)',
                'rgba(34, 197, 94, 0.8)',
                'rgba(168, 85, 247, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
@endpush
