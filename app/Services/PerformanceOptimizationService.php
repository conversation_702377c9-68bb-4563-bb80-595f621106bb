<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceOptimizationService
{
    /**
     * Optimize database queries and add indexes.
     */
    public function optimizeDatabase(): array
    {
        $optimizations = [];

        try {
            // Add database indexes for better performance
            $this->addDatabaseIndexes();
            $optimizations[] = 'Database indexes optimized';

            // Clean up old analytics data
            $this->cleanupOldAnalytics();
            $optimizations[] = 'Old analytics data cleaned up';

            // Optimize database tables
            $this->optimizeTables();
            $optimizations[] = 'Database tables optimized';

        } catch (\Exception $e) {
            Log::error('Database optimization failed', ['error' => $e->getMessage()]);
            $optimizations[] = 'Database optimization failed: ' . $e->getMessage();
        }

        return $optimizations;
    }

    /**
     * Implement caching strategies.
     */
    public function implementCaching(): array
    {
        $cacheStrategies = [];

        try {
            // Cache popular URLs
            $this->cachePopularUrls();
            $cacheStrategies[] = 'Popular URLs cached';

            // Cache user statistics
            $this->cacheUserStatistics();
            $cacheStrategies[] = 'User statistics cached';

            // Cache analytics data
            $this->cacheAnalyticsData();
            $cacheStrategies[] = 'Analytics data cached';

            // Cache subscription data
            $this->cacheSubscriptionData();
            $cacheStrategies[] = 'Subscription data cached';

        } catch (\Exception $e) {
            Log::error('Caching implementation failed', ['error' => $e->getMessage()]);
            $cacheStrategies[] = 'Caching failed: ' . $e->getMessage();
        }

        return $cacheStrategies;
    }

    /**
     * Optimize application performance.
     */
    public function optimizeApplication(): array
    {
        $optimizations = [];

        try {
            // Clear and optimize config cache
            \Artisan::call('config:cache');
            $optimizations[] = 'Configuration cached';

            // Clear and optimize route cache
            \Artisan::call('route:cache');
            $optimizations[] = 'Routes cached';

            // Clear and optimize view cache
            \Artisan::call('view:cache');
            $optimizations[] = 'Views cached';

            // Optimize autoloader
            \Artisan::call('optimize');
            $optimizations[] = 'Application optimized';

        } catch (\Exception $e) {
            Log::error('Application optimization failed', ['error' => $e->getMessage()]);
            $optimizations[] = 'Application optimization failed: ' . $e->getMessage();
        }

        return $optimizations;
    }

    /**
     * Monitor application performance.
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit'),
            ],
            'database' => [
                'total_queries' => DB::getQueryLog() ? count(DB::getQueryLog()) : 0,
                'connection_count' => $this->getDatabaseConnectionCount(),
            ],
            'cache' => [
                'hit_ratio' => $this->getCacheHitRatio(),
                'size' => $this->getCacheSize(),
            ],
            'response_times' => [
                'average' => $this->getAverageResponseTime(),
                'p95' => $this->get95thPercentileResponseTime(),
            ],
        ];
    }

    /**
     * Add necessary database indexes.
     */
    private function addDatabaseIndexes(): void
    {
        $indexes = [
            'urls' => [
                ['short_code'],
                ['user_id', 'created_at'],
                ['is_active', 'expires_at'],
                ['custom_domain_id'],
            ],
            'analytics' => [
                ['url_id', 'clicked_at'],
                ['ip_address'],
                ['country'],
                ['clicked_at'],
            ],
            'users' => [
                ['email'],
                ['api_key'],
                ['subscription_id'],
            ],
            'custom_domains' => [
                ['user_id'],
                ['domain'],
                ['is_verified', 'is_active'],
            ],
        ];

        foreach ($indexes as $table => $tableIndexes) {
            foreach ($tableIndexes as $columns) {
                $indexName = $table . '_' . implode('_', $columns) . '_index';
                
                try {
                    DB::statement("CREATE INDEX IF NOT EXISTS {$indexName} ON {$table} (" . implode(',', $columns) . ")");
                } catch (\Exception $e) {
                    // Index might already exist, continue
                    Log::info("Index {$indexName} might already exist");
                }
            }
        }
    }

    /**
     * Clean up old analytics data.
     */
    private function cleanupOldAnalytics(): void
    {
        // Keep analytics data for 2 years
        $cutoffDate = now()->subYears(2);
        
        DB::table('analytics')
            ->where('clicked_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Optimize database tables.
     */
    private function optimizeTables(): void
    {
        $tables = ['urls', 'analytics', 'users', 'custom_domains'];
        
        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE {$table}");
            } catch (\Exception $e) {
                Log::warning("Could not optimize table {$table}: " . $e->getMessage());
            }
        }
    }

    /**
     * Cache popular URLs for faster access.
     */
    private function cachePopularUrls(): void
    {
        $popularUrls = DB::table('urls')
            ->where('is_active', true)
            ->orderBy('click_count', 'desc')
            ->limit(1000)
            ->get(['short_code', 'original_url', 'password', 'expires_at']);

        foreach ($popularUrls as $url) {
            Cache::put("url:{$url->short_code}", $url, 3600); // Cache for 1 hour
        }
    }

    /**
     * Cache user statistics.
     */
    private function cacheUserStatistics(): void
    {
        $users = DB::table('users')->where('created_at', '>', now()->subDays(30))->get(['id']);
        
        foreach ($users as $user) {
            $stats = [
                'total_urls' => DB::table('urls')->where('user_id', $user->id)->count(),
                'total_clicks' => DB::table('urls')->where('user_id', $user->id)->sum('click_count'),
                'active_urls' => DB::table('urls')->where('user_id', $user->id)->where('is_active', true)->count(),
            ];
            
            Cache::put("user_stats:{$user->id}", $stats, 1800); // Cache for 30 minutes
        }
    }

    /**
     * Cache analytics data.
     */
    private function cacheAnalyticsData(): void
    {
        // Cache daily analytics summary
        $today = now()->format('Y-m-d');
        $todayStats = [
            'total_clicks' => DB::table('analytics')->whereDate('clicked_at', $today)->count(),
            'unique_visitors' => DB::table('analytics')->whereDate('clicked_at', $today)->distinct('ip_address')->count(),
            'top_countries' => DB::table('analytics')
                ->whereDate('clicked_at', $today)
                ->select('country', DB::raw('COUNT(*) as count'))
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
        ];
        
        Cache::put("daily_analytics:{$today}", $todayStats, 3600); // Cache for 1 hour
    }

    /**
     * Cache subscription data.
     */
    private function cacheSubscriptionData(): void
    {
        $subscriptions = DB::table('subscriptions')->where('is_active', true)->get();
        Cache::put('active_subscriptions', $subscriptions, 7200); // Cache for 2 hours
    }

    /**
     * Get database connection count.
     */
    private function getDatabaseConnectionCount(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return $result[0]->Value ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cache hit ratio.
     */
    private function getCacheHitRatio(): float
    {
        // This would need to be implemented based on your cache driver
        // For Redis, you could use INFO stats
        return 0.85; // Mock value
    }

    /**
     * Get cache size.
     */
    private function getCacheSize(): string
    {
        // This would need to be implemented based on your cache driver
        return '50MB'; // Mock value
    }

    /**
     * Get average response time.
     */
    private function getAverageResponseTime(): float
    {
        // This would typically come from application monitoring
        return 150.5; // Mock value in milliseconds
    }

    /**
     * Get 95th percentile response time.
     */
    private function get95thPercentileResponseTime(): float
    {
        // This would typically come from application monitoring
        return 300.2; // Mock value in milliseconds
    }

    /**
     * Clear all application caches.
     */
    public function clearAllCaches(): array
    {
        $cleared = [];

        try {
            \Artisan::call('cache:clear');
            $cleared[] = 'Application cache cleared';

            \Artisan::call('config:clear');
            $cleared[] = 'Configuration cache cleared';

            \Artisan::call('route:clear');
            $cleared[] = 'Route cache cleared';

            \Artisan::call('view:clear');
            $cleared[] = 'View cache cleared';

            Cache::flush();
            $cleared[] = 'All caches flushed';

        } catch (\Exception $e) {
            Log::error('Cache clearing failed', ['error' => $e->getMessage()]);
            $cleared[] = 'Cache clearing failed: ' . $e->getMessage();
        }

        return $cleared;
    }

    /**
     * Generate performance report.
     */
    public function generatePerformanceReport(): array
    {
        return [
            'timestamp' => now()->toISOString(),
            'metrics' => $this->getPerformanceMetrics(),
            'database_stats' => [
                'total_urls' => DB::table('urls')->count(),
                'total_clicks' => DB::table('analytics')->count(),
                'total_users' => DB::table('users')->count(),
                'database_size' => $this->getDatabaseSize(),
            ],
            'recommendations' => $this->getPerformanceRecommendations(),
        ];
    }

    /**
     * Get database size.
     */
    private function getDatabaseSize(): string
    {
        try {
            $result = DB::select("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            
            return ($result[0]->size_mb ?? 0) . ' MB';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get performance recommendations.
     */
    private function getPerformanceRecommendations(): array
    {
        $recommendations = [];

        // Check if indexes are needed
        $slowQueries = $this->getSlowQueries();
        if (count($slowQueries) > 0) {
            $recommendations[] = 'Consider adding indexes for slow queries';
        }

        // Check cache hit ratio
        $hitRatio = $this->getCacheHitRatio();
        if ($hitRatio < 0.8) {
            $recommendations[] = 'Improve cache hit ratio by caching more frequently accessed data';
        }

        // Check database size
        $dbSize = $this->getDatabaseSize();
        if (str_contains($dbSize, 'GB')) {
            $recommendations[] = 'Consider archiving old data to reduce database size';
        }

        return $recommendations;
    }

    /**
     * Get slow queries.
     */
    private function getSlowQueries(): array
    {
        // This would typically come from slow query log analysis
        return []; // Mock implementation
    }
}
