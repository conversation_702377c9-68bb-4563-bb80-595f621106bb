<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class AnalyticsController extends Controller
{
    private AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
        $this->middleware('auth');
    }

    /**
     * Show advanced analytics for a URL.
     */
    public function show(Request $request, Url $url): View
    {
        // Check if user can view analytics
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to view analytics for this URL.');
        }

        $filters = $request->only(['start_date', 'end_date', 'country', 'device_type', 'browser', 'is_bot']);
        $analytics = $this->analyticsService->getAdvancedAnalytics($url, $filters);
        $realTimeAnalytics = $this->analyticsService->getRealTimeAnalytics($url);

        return view('analytics.show', compact('url', 'analytics', 'realTimeAnalytics', 'filters'));
    }

    /**
     * Get filtered analytics data via AJAX.
     */
    public function filter(Request $request, Url $url): JsonResponse
    {
        // Check if user can view analytics
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to view analytics for this URL.');
        }

        $filters = $request->only(['start_date', 'end_date', 'country', 'device_type', 'browser', 'is_bot']);
        $analytics = $this->analyticsService->getAdvancedAnalytics($url, $filters);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get real-time analytics data.
     */
    public function realTime(Url $url): JsonResponse
    {
        // Check if user can view analytics
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to view analytics for this URL.');
        }

        $realTimeAnalytics = $this->analyticsService->getRealTimeAnalytics($url);

        return response()->json([
            'success' => true,
            'data' => $realTimeAnalytics,
        ]);
    }

    /**
     * Compare performance between periods.
     */
    public function compare(Request $request, Url $url): JsonResponse
    {
        // Check if user can view analytics
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to view analytics for this URL.');
        }

        $request->validate([
            'current_period' => 'required|in:today,week,month,year',
            'previous_period' => 'required|in:today,week,month,year',
        ]);

        $comparison = $this->analyticsService->comparePerformance(
            $url,
            $request->input('current_period'),
            $request->input('previous_period')
        );

        return response()->json([
            'success' => true,
            'data' => $comparison,
        ]);
    }

    /**
     * Export analytics report.
     */
    public function export(Request $request, Url $url): Response
    {
        // Check if user can export analytics
        if ($url->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized to export analytics for this URL.');
        }

        $request->validate([
            'format' => 'required|in:csv,json,pdf',
            'period' => 'in:today,week,month,year,all',
            'include_raw_data' => 'boolean',
        ]);

        $options = [
            'format' => $request->input('format', 'csv'),
            'period' => $request->input('period', 'month'),
            'include_raw_data' => $request->boolean('include_raw_data', false),
            'filters' => $request->only(['start_date', 'end_date', 'country', 'device_type', 'browser']),
        ];

        $report = $this->analyticsService->generateReport($url, $options);

        return $this->generateExportResponse($report, $options['format'], $url);
    }

    /**
     * Generate export response based on format.
     */
    private function generateExportResponse(array $report, string $format, Url $url): Response
    {
        $filename = 'analytics_' . ($url->title ? \Str::slug($url->title) : $url->id) . '_' . now()->format('Y-m-d');

        switch ($format) {
            case 'csv':
                return $this->generateCsvResponse($report, $filename);
            case 'json':
                return $this->generateJsonResponse($report, $filename);
            case 'pdf':
                return $this->generatePdfResponse($report, $filename);
            default:
                abort(400, 'Invalid export format');
        }
    }

    /**
     * Generate CSV export response.
     */
    private function generateCsvResponse(array $report, string $filename): Response
    {
        $csv = "URL Analytics Report\n";
        $csv .= "Generated: " . $report['report_info']['generated_at'] . "\n";
        $csv .= "URL: " . $report['url_info']['original_url'] . "\n";
        $csv .= "Short URL: " . $report['url_info']['short_url'] . "\n\n";

        // Summary statistics
        $csv .= "Summary Statistics\n";
        $csv .= "Total Clicks," . $report['analytics']['total_clicks'] . "\n";
        $csv .= "Unique Visitors," . $report['analytics']['unique_visitors'] . "\n\n";

        // Countries
        $csv .= "Top Countries\n";
        $csv .= "Country,Clicks,Percentage\n";
        foreach ($report['analytics']['countries'] as $country) {
            $csv .= "{$country['country']},{$country['count']},{$country['percentage']}%\n";
        }

        // Devices
        $csv .= "\nTop Devices\n";
        $csv .= "Device,Clicks,Percentage\n";
        foreach ($report['analytics']['devices'] as $device) {
            $csv .= "{$device['device']},{$device['count']},{$device['percentage']}%\n";
        }

        // Browsers
        $csv .= "\nTop Browsers\n";
        $csv .= "Browser,Clicks,Percentage\n";
        foreach ($report['analytics']['browsers'] as $browser) {
            $csv .= "{$browser['browser']},{$browser['count']},{$browser['percentage']}%\n";
        }

        return response($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}.csv\"",
        ]);
    }

    /**
     * Generate JSON export response.
     */
    private function generateJsonResponse(array $report, string $filename): Response
    {
        return response()->json($report, 200, [
            'Content-Disposition' => "attachment; filename=\"{$filename}.json\"",
        ]);
    }

    /**
     * Generate PDF export response.
     */
    private function generatePdfResponse(array $report, string $filename): Response
    {
        // For now, return JSON. In production, you'd use a PDF library like DomPDF
        $html = $this->generateHtmlReport($report);

        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => "attachment; filename=\"{$filename}.html\"",
        ]);
    }

    /**
     * Generate HTML report for PDF conversion.
     */
    private function generateHtmlReport(array $report): string
    {
        $html = "<!DOCTYPE html><html><head><title>Analytics Report</title>";
        $html .= "<style>body{font-family:Arial,sans-serif;margin:20px;}table{border-collapse:collapse;width:100%;}th,td{border:1px solid #ddd;padding:8px;text-align:left;}th{background-color:#f2f2f2;}</style>";
        $html .= "</head><body>";

        $html .= "<h1>URL Analytics Report</h1>";
        $html .= "<p><strong>Generated:</strong> " . $report['report_info']['generated_at'] . "</p>";
        $html .= "<p><strong>URL:</strong> " . $report['url_info']['original_url'] . "</p>";
        $html .= "<p><strong>Short URL:</strong> " . $report['url_info']['short_url'] . "</p>";

        $html .= "<h2>Summary Statistics</h2>";
        $html .= "<p><strong>Total Clicks:</strong> " . number_format($report['analytics']['total_clicks']) . "</p>";
        $html .= "<p><strong>Unique Visitors:</strong> " . number_format($report['analytics']['unique_visitors']) . "</p>";

        // Countries table
        $html .= "<h2>Top Countries</h2><table><tr><th>Country</th><th>Clicks</th><th>Percentage</th></tr>";
        foreach ($report['analytics']['countries'] as $country) {
            $html .= "<tr><td>{$country['country']}</td><td>{$country['count']}</td><td>{$country['percentage']}%</td></tr>";
        }
        $html .= "</table>";

        // Devices table
        $html .= "<h2>Top Devices</h2><table><tr><th>Device</th><th>Clicks</th><th>Percentage</th></tr>";
        foreach ($report['analytics']['devices'] as $device) {
            $html .= "<tr><td>{$device['device']}</td><td>{$device['count']}</td><td>{$device['percentage']}%</td></tr>";
        }
        $html .= "</table>";

        $html .= "</body></html>";

        return $html;
    }
}
