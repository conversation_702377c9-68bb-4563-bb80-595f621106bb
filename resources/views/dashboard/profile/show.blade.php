@extends('layouts.modern')

@section('title', 'Profile - Minilink.at')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-user mr-3 text-blue-600"></i>
                    Profile
                </h1>
                <p class="text-gray-600 mt-1">Manage your account information and preferences</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <a href="{{ route('dashboard') }}" class="btn-outline">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <a href="{{ route('profile.edit') }}" class="btn-primary">
                    <i class="fas fa-edit mr-2"></i>Edit Profile
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Profile Overview -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user-circle mr-2 text-blue-600"></i>
                        Profile Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="flex items-center space-x-6 mb-8">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                                <span class="text-white font-bold text-2xl">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        </div>
                        <!-- User Info -->
                        <div class="flex-1">
                            <h2 class="text-2xl font-bold text-gray-900">{{ $user->name }}</h2>
                            <p class="text-gray-600 flex items-center mt-1">
                                <i class="fas fa-envelope mr-2"></i>
                                {{ $user->email }}
                                @if($user->email_verified_at)
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Verified
                                    </span>
                                @else
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-exclamation-circle mr-1"></i>Unverified
                                    </span>
                                @endif
                            </p>
                            <p class="text-gray-500 text-sm mt-1">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                Member since {{ $user->created_at->format('F j, Y') }}
                            </p>
                        </div>
                    </div>

                    <!-- Account Information Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-1">
                            <label class="text-sm font-medium text-gray-500">Account Type</label>
                            <div class="flex items-center">
                                <span class="text-lg font-semibold text-gray-900">{{ ucfirst($user->role) }}</span>
                                @if($user->isAdmin())
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-crown mr-1"></i>Admin
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="space-y-1">
                            <label class="text-sm font-medium text-gray-500">Account Status</label>
                            <div class="flex items-center">
                                @if($user->is_active)
                                    <span class="text-lg font-semibold text-green-600">Active</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Good Standing
                                    </span>
                                @else
                                    <span class="text-lg font-semibold text-red-600">Inactive</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>Suspended
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="space-y-1">
                            <label class="text-sm font-medium text-gray-500">Last Activity</label>
                            <div class="flex items-center">
                                <span class="text-lg font-semibold text-gray-900">{{ $user->updated_at->format('M j, Y') }}</span>
                                <span class="ml-2 text-sm text-gray-500">{{ $user->updated_at->format('g:i A') }}</span>
                            </div>
                        </div>
                        <div class="space-y-1">
                            <label class="text-sm font-medium text-gray-500">Total URLs Created</label>
                            <div class="flex items-center">
                                <span class="text-lg font-semibold text-gray-900">{{ $stats['total_urls'] ?? 0 }}</span>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-link mr-1"></i>Links
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-bolt mr-2 text-yellow-500"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="{{ route('profile.edit') }}" class="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                                    <i class="fas fa-edit text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-gray-900">Edit Profile</h4>
                                <p class="text-sm text-gray-500">Update your personal information</p>
                            </div>
                            <div class="ml-auto">
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-blue-600 transition-colors duration-200"></i>
                            </div>
                        </a>

                        <a href="{{ route('profile.password') }}" class="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-200">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors duration-200">
                                    <i class="fas fa-key text-yellow-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-gray-900">Change Password</h4>
                                <p class="text-sm text-gray-500">Update your account password</p>
                            </div>
                            <div class="ml-auto">
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-yellow-600 transition-colors duration-200"></i>
                            </div>
                        </a>

                        <a href="{{ route('profile.api-keys') }}" class="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                                    <i class="fas fa-code text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-gray-900">API Keys</h4>
                                <p class="text-sm text-gray-500">Manage your API access keys</p>
                            </div>
                            <div class="ml-auto">
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-green-600 transition-colors duration-200"></i>
                            </div>
                        </a>

                        <a href="{{ route('profile.settings') }}" class="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all duration-200">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                                    <i class="fas fa-sliders-h text-purple-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-gray-900">Account Settings</h4>
                                <p class="text-sm text-gray-500">Configure your preferences</p>
                            </div>
                            <div class="ml-auto">
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-purple-600 transition-colors duration-200"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-8">
            <!-- Account Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-green-600"></i>
                        Account Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- Stats Grid -->
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ number_format($stats['total_urls']) }}</div>
                                <div class="text-sm text-gray-600 mt-1">Total URLs</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">{{ number_format($stats['total_clicks']) }}</div>
                                <div class="text-sm text-gray-600 mt-1">Total Clicks</div>
                            </div>
                        </div>

                        <!-- Additional Stats -->
                        <div class="space-y-3">
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm text-gray-600">This Month</span>
                                <span class="text-sm font-medium text-gray-900">{{ $stats['monthly_urls'] ?? 0 }} URLs</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm text-gray-600">Avg. Clicks/URL</span>
                                <span class="text-sm font-medium text-gray-900">{{ $stats['avg_clicks'] ?? 0 }}</span>
                            </div>
                            <div class="flex justify-between items-center py-2">
                                <span class="text-sm text-gray-600">Account Age</span>
                                <span class="text-sm font-medium text-gray-900">{{ $stats['account_age'] ?? $user->created_at->diffForHumans() }}</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-3 pt-4 border-t border-gray-200">
                            <a href="{{ route('dashboard.analytics') }}" class="w-full btn-outline text-center">
                                <i class="fas fa-chart-line mr-2"></i>View Analytics
                            </a>
                            <a href="{{ route('dashboard.urls.index') }}" class="w-full btn-primary text-center">
                                <i class="fas fa-link mr-2"></i>Manage URLs
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Info -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-crown mr-2 text-yellow-500"></i>
                        Subscription
                    </h3>
                </div>
                <div class="card-body">
                    @if($user->subscription)
                        <div class="text-center mb-4">
                            <div class="text-xl font-bold text-gray-900">{{ $user->subscription->name }}</div>
                            <div class="text-sm text-gray-600">${{ number_format($user->subscription->price, 2) }}/{{ $user->subscription->billing_cycle }}</div>
                        </div>

                        @if($user->subscription_expires_at)
                            <div class="bg-blue-50 rounded-lg p-3 mb-4">
                                <div class="text-sm text-blue-800">
                                    <i class="fas fa-calendar-alt mr-2"></i>
                                    Expires: {{ $user->subscription_expires_at->format('M j, Y') }}
                                </div>
                            </div>
                        @endif

                        <div class="space-y-2">
                            <a href="{{ route('subscriptions.index') }}" class="w-full btn-outline text-center">
                                <i class="fas fa-cog mr-2"></i>Manage Subscription
                            </a>
                        </div>
                    @else
                        <div class="text-center mb-4">
                            <div class="text-xl font-bold text-gray-900">Free Plan</div>
                            <div class="text-sm text-gray-600">Basic features included</div>
                        </div>

                        <div class="bg-yellow-50 rounded-lg p-3 mb-4">
                            <div class="text-sm text-yellow-800">
                                <i class="fas fa-star mr-2"></i>
                                Upgrade for more features!
                            </div>
                        </div>

                        <div class="space-y-2">
                            <a href="{{ route('subscriptions.index') }}" class="w-full btn-primary text-center">
                                <i class="fas fa-arrow-up mr-2"></i>Upgrade Plan
                            </a>
                        </div>
                    @endif
                </div>
            </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-shield-alt mr-2 text-red-600"></i>
                        Security
                    </h3>
                </div>
                <div class="card-body">
                    <div class="space-y-6">
                        <!-- Two-Factor Authentication -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-mobile-alt text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                                    <p class="text-sm text-gray-500">
                                        @if($user->two_factor_enabled ?? false)
                                            Your account is protected with 2FA
                                        @else
                                            Add an extra layer of security
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div>
                                @if($user->two_factor_enabled ?? false)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Enabled
                                    </span>
                                @else
                                    <button class="btn-outline text-sm">
                                        <i class="fas fa-plus mr-1"></i>Enable
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- API Access -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-code text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">API Access</h4>
                                    <p class="text-sm text-gray-500">
                                        @if($user->api_key ?? false)
                                            API key is configured and active
                                        @else
                                            Generate API key for integrations
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div>
                                @if($user->api_key ?? false)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-check-circle mr-1"></i>Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-times-circle mr-1"></i>Inactive
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Security Actions -->
                        <div class="space-y-3 pt-4 border-t border-gray-200">
                            <a href="{{ route('profile.password') }}" class="w-full btn-outline text-center">
                                <i class="fas fa-key mr-2"></i>Change Password
                            </a>
                            @if(!($user->two_factor_enabled ?? false))
                                <button class="w-full btn-primary text-center">
                                    <i class="fas fa-mobile-alt mr-2"></i>Enable 2FA
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
