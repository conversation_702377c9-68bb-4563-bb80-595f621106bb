<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Url;
use App\Services\UrlShortenerService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UrlShortenerServiceTest extends TestCase
{
    use RefreshDatabase;

    protected UrlShortenerService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new UrlShortenerService();
    }

    public function test_can_shorten_url_for_guest(): void
    {
        $data = [
            'original_url' => 'https://example.com',
            'title' => 'Test URL',
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertInstanceOf(Url::class, $url);
        $this->assertEquals('https://example.com', $url->original_url);
        $this->assertEquals('Test URL', $url->title);
        $this->assertNull($url->user_id);
        $this->assertNotNull($url->short_code);
    }

    public function test_can_shorten_url_for_user(): void
    {
        $user = User::factory()->create();
        $data = [
            'original_url' => 'https://example.com',
            'title' => 'User URL',
        ];

        $url = $this->service->shortenUrl($data, $user);

        $this->assertEquals($user->id, $url->user_id);
        $this->assertEquals('User URL', $url->title);
    }

    public function test_generates_unique_short_codes(): void
    {
        $codes = [];

        for ($i = 0; $i < 10; $i++) {
            $url = $this->service->shortenUrl([
                'original_url' => "https://example{$i}.com",
            ]);
            $codes[] = $url->short_code;
        }

        $this->assertEquals(count($codes), count(array_unique($codes)));
    }

    public function test_validates_url_format(): void
    {
        $this->expectException(\Exception::class);

        $this->service->shortenUrl([
            'original_url' => 'not-a-valid-url',
        ]);
    }

    public function test_handles_custom_alias(): void
    {
        $data = [
            'original_url' => 'https://example.com',
            'custom_alias' => 'my-custom-link',
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertEquals('my-custom-link', $url->short_code);
        $this->assertEquals('my-custom-link', $url->custom_alias);
    }

    public function test_prevents_duplicate_custom_alias(): void
    {
        // Create first URL with custom alias
        $this->service->shortenUrl([
            'original_url' => 'https://example1.com',
            'custom_alias' => 'duplicate',
        ]);

        $this->expectException(\Exception::class);

        // Try to create second URL with same alias
        $this->service->shortenUrl([
            'original_url' => 'https://example2.com',
            'custom_alias' => 'duplicate',
        ]);
    }

    public function test_handles_password_protection(): void
    {
        $data = [
            'original_url' => 'https://example.com',
            'password' => 'secret123',
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertNotNull($url->password);
        $this->assertTrue(\Hash::check('secret123', $url->password));
    }

    public function test_handles_expiration_date(): void
    {
        $expirationDate = now()->addDays(7);
        $data = [
            'original_url' => 'https://example.com',
            'expires_at' => $expirationDate,
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertEquals($expirationDate->format('Y-m-d H:i:s'), $url->expires_at->format('Y-m-d H:i:s'));
    }

    public function test_validates_required_fields(): void
    {
        $this->expectException(\Exception::class);

        $this->service->shortenUrl([]);
    }

    public function test_sets_default_values(): void
    {
        $data = [
            'original_url' => 'https://example.com',
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertTrue($url->is_active);
        $this->assertEquals(0, $url->click_count);
        $this->assertNull($url->expires_at);
        $this->assertNull($url->password);
    }

    public function test_handles_metadata(): void
    {
        $metadata = ['source' => 'api', 'campaign' => 'test'];
        $data = [
            'original_url' => 'https://example.com',
            'metadata' => $metadata,
        ];

        $url = $this->service->shortenUrl($data);

        $this->assertEquals($metadata, $url->metadata);
    }

    public function test_creates_short_url_property(): void
    {
        $data = [
            'original_url' => 'https://example.com',
        ];

        $url = $this->service->shortenUrl($data);

        $expectedShortUrl = config('app.url') . '/' . $url->short_code;
        $this->assertEquals($expectedShortUrl, $url->short_url);
    }
}
