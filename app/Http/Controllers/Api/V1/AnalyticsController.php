<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Analytics;
use App\Models\Url;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends ApiController
{
    /**
     * Get analytics for a specific URL.
     */
    public function show(Request $request, string $urlId): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();

            if (!$url) {
                return $this->errorResponse('URL not found', 404);
            }

            $pagination = $this->validatePagination($request);
            
            $query = $url->analytics()->orderBy('clicked_at', 'desc');

            // Apply date range filter
            if ($request->filled('date_from')) {
                $query->whereDate('clicked_at', '>=', $request->get('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('clicked_at', '<=', $request->get('date_to'));
            }

            // Apply country filter
            if ($request->filled('country')) {
                $query->where('country', $request->get('country'));
            }

            // Apply device filter
            if ($request->filled('device_type')) {
                $query->where('device_type', $request->get('device_type'));
            }

            $analytics = $query->paginate($pagination['per_page'], ['*'], 'page', $pagination['page']);

            $transformedAnalytics = $analytics->getCollection()->map(function ($analytic) {
                return $this->transformAnalytics($analytic);
            });

            $analytics->setCollection($transformedAnalytics);

            $response = $this->paginatedResponse($analytics, 'Analytics retrieved successfully');
            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get analytics summary for all user's URLs.
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $period = $request->get('period', 'week'); // week, month, year, all

            $startDate = match($period) {
                'week' => now()->subWeek(),
                'month' => now()->subMonth(),
                'year' => now()->subYear(),
                'all' => null,
                default => now()->subWeek(),
            };

            $urlIds = $user->urls()->pluck('id');
            $analyticsQuery = Analytics::whereIn('url_id', $urlIds);

            if ($startDate) {
                $analyticsQuery->where('clicked_at', '>=', $startDate);
            }

            $summary = [
                'period' => $period,
                'total_clicks' => $analyticsQuery->count(),
                'unique_clicks' => $analyticsQuery->distinct('ip_address')->count(),
                'total_urls' => $user->urls()->count(),
                'active_urls' => $user->urls()->where('is_active', true)->count(),
                'top_performing_urls' => $user->urls()
                    ->withCount(['analytics' => function($query) use ($startDate) {
                        if ($startDate) {
                            $query->where('clicked_at', '>=', $startDate);
                        }
                    }])
                    ->orderBy('analytics_count', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function ($url) {
                        return [
                            'id' => $url->id,
                            'title' => $url->title ?: 'Untitled',
                            'short_url' => $url->short_url,
                            'clicks' => $url->analytics_count,
                        ];
                    }),
                'countries' => $analyticsQuery
                    ->select('country', DB::raw('COUNT(*) as count'))
                    ->whereNotNull('country')
                    ->groupBy('country')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
                'devices' => $analyticsQuery
                    ->select('device_type', DB::raw('COUNT(*) as count'))
                    ->whereNotNull('device_type')
                    ->groupBy('device_type')
                    ->orderBy('count', 'desc')
                    ->get(),
                'browsers' => $analyticsQuery
                    ->select('browser', DB::raw('COUNT(*) as count'))
                    ->whereNotNull('browser')
                    ->groupBy('browser')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
                'referrers' => $analyticsQuery
                    ->select('referrer', DB::raw('COUNT(*) as count'))
                    ->whereNotNull('referrer')
                    ->where('referrer', '!=', '')
                    ->groupBy('referrer')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get(),
            ];

            $response = $this->successResponse(
                $summary,
                'Analytics summary retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get click trends over time.
     */
    public function trends(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $period = $request->get('period', 'week'); // week, month, year
            $urlId = $request->get('url_id'); // Optional: specific URL

            $days = match($period) {
                'week' => 7,
                'month' => 30,
                'year' => 365,
                default => 7,
            };

            $query = Analytics::query();

            if ($urlId) {
                $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();
                if (!$url) {
                    return $this->errorResponse('URL not found', 404);
                }
                $query->where('url_id', $urlId);
            } else {
                $urlIds = $user->urls()->pluck('id');
                $query->whereIn('url_id', $urlIds);
            }

            $trends = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $count = $query->clone()
                    ->whereDate('clicked_at', $date)
                    ->count();

                $trends[] = [
                    'date' => $date->format('Y-m-d'),
                    'clicks' => $count,
                ];
            }

            $response = $this->successResponse([
                'period' => $period,
                'url_id' => $urlId,
                'trends' => $trends,
            ], 'Click trends retrieved successfully');

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get real-time analytics (last 24 hours).
     */
    public function realtime(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $urlIds = $user->urls()->pluck('id');

            $last24Hours = now()->subDay();
            
            $realtimeData = [
                'clicks_last_24h' => Analytics::whereIn('url_id', $urlIds)
                    ->where('clicked_at', '>=', $last24Hours)
                    ->count(),
                'clicks_last_hour' => Analytics::whereIn('url_id', $urlIds)
                    ->where('clicked_at', '>=', now()->subHour())
                    ->count(),
                'active_urls_24h' => Analytics::whereIn('url_id', $urlIds)
                    ->where('clicked_at', '>=', $last24Hours)
                    ->distinct('url_id')
                    ->count(),
                'top_countries_24h' => Analytics::whereIn('url_id', $urlIds)
                    ->where('clicked_at', '>=', $last24Hours)
                    ->select('country', DB::raw('COUNT(*) as count'))
                    ->whereNotNull('country')
                    ->groupBy('country')
                    ->orderBy('count', 'desc')
                    ->limit(5)
                    ->get(),
                'hourly_breakdown' => $this->getHourlyBreakdown($urlIds),
                'recent_clicks' => Analytics::whereIn('url_id', $urlIds)
                    ->with('url:id,title,short_code')
                    ->orderBy('clicked_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function ($analytic) {
                        return [
                            'url_title' => $analytic->url->title ?: 'Untitled',
                            'short_code' => $analytic->url->short_code,
                            'country' => $analytic->country,
                            'device_type' => $analytic->device_type,
                            'clicked_at' => $analytic->clicked_at->toISOString(),
                        ];
                    }),
            ];

            $response = $this->successResponse(
                $realtimeData,
                'Real-time analytics retrieved successfully'
            );

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Export analytics data.
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $user = $this->getApiUser($request);
            $format = $request->get('format', 'json'); // json, csv
            $urlId = $request->get('url_id'); // Optional: specific URL

            $query = Analytics::query();

            if ($urlId) {
                $url = Url::where('id', $urlId)->where('user_id', $user->id)->first();
                if (!$url) {
                    return $this->errorResponse('URL not found', 404);
                }
                $query->where('url_id', $urlId);
            } else {
                $urlIds = $user->urls()->pluck('id');
                $query->whereIn('url_id', $urlIds);
            }

            // Apply date range
            if ($request->filled('date_from')) {
                $query->whereDate('clicked_at', '>=', $request->get('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('clicked_at', '<=', $request->get('date_to'));
            }

            $analytics = $query->with('url:id,title,short_code')
                ->orderBy('clicked_at', 'desc')
                ->limit(10000) // Limit for performance
                ->get();

            if ($format === 'csv') {
                return $this->exportToCsv($analytics);
            }

            $exportData = $analytics->map(function ($analytic) {
                return $this->transformAnalytics($analytic);
            });

            $response = $this->successResponse([
                'format' => $format,
                'count' => $analytics->count(),
                'data' => $exportData,
                'exported_at' => now()->toISOString(),
            ], 'Analytics data exported successfully');

            return $this->addRateLimitHeaders($response, $request);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get hourly breakdown for the last 24 hours.
     */
    private function getHourlyBreakdown(array $urlIds): array
    {
        $breakdown = [];
        for ($i = 23; $i >= 0; $i--) {
            $hour = now()->subHours($i);
            $count = Analytics::whereIn('url_id', $urlIds)
                ->whereBetween('clicked_at', [
                    $hour->copy()->startOfHour(),
                    $hour->copy()->endOfHour()
                ])
                ->count();

            $breakdown[] = [
                'hour' => $hour->format('H:00'),
                'clicks' => $count,
            ];
        }

        return $breakdown;
    }

    /**
     * Export analytics to CSV format.
     */
    private function exportToCsv($analytics): JsonResponse
    {
        $csvData = [];
        $csvData[] = ['URL Title', 'Short Code', 'IP Address', 'Country', 'City', 'Device', 'Browser', 'Referrer', 'Clicked At'];

        foreach ($analytics as $analytic) {
            $csvData[] = [
                $analytic->url->title ?: 'Untitled',
                $analytic->url->short_code,
                $analytic->ip_address,
                $analytic->country,
                $analytic->city,
                $analytic->device_type,
                $analytic->browser,
                $analytic->referrer,
                $analytic->clicked_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'analytics_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $filePath = storage_path('app/exports/' . $filename);
        
        // Ensure directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        $file = fopen($filePath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return $this->successResponse([
            'format' => 'csv',
            'filename' => $filename,
            'download_url' => route('api.v1.analytics.download', ['filename' => $filename]),
            'count' => count($csvData) - 1, // Exclude header
        ], 'CSV export generated successfully');
    }
}
